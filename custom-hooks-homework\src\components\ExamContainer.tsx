import useExamStore from "../hooks/useExamStore";
import Question from "./Question";

const ExamContainer = () => {
  const { examState, selectAnswer, submitExam, resetExam } = useExamStore();
  const { questions, userAnswers, submitted, score } = examState;

  const handleSubmit = () => {
    if (Object.keys(userAnswers).length === 0) {
      alert("请至少回答一道题目！");
      return;
    }
    submitExam();
  };

  return (
    <div className="exam-container">
      <h1 className="exam-title">在线考试</h1>
      <div className="questions-list">
        {questions.map((question, index) => (
          <Question
            key={index}
            question={question}
            index={index}
            selectedOption={userAnswers[index]}
            onSelectOption={selectAnswer}
            submitted={submitted}
          />
        ))}
      </div>

      {submitted ? (
        <div className="result-container">
          <div className="score-text">
            您的得分：{score} / {questions.length}
          </div>
          <button className="submit-btn" onClick={resetExam}>
            重新考试
          </button>
        </div>
      ) : (
        <button className="submit-btn" onClick={handleSubmit}>
          提交答案
        </button>
      )}
    </div>
  );
};

export default ExamContainer;
