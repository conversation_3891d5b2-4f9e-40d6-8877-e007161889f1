/* 手机登录页面样式 */
.phone-login-section {
  width: 400px;
  height: 500px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 2rem;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  position: relative;
}

.phone-login-form {
  width: 100%;
  height: 100%;
  max-width: 320px;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.phone-login-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  position: relative;
}

.back-button {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  position: absolute;
  left: 0;
  top: 0;
}
.back-button:before {
  width: 14px;
  height: 13px;
  content: url("@/assets/arrowL.svg");
  margin-right: 4px;
  display: inline-block;
  vertical-align: middle;
}

.back-button:hover {
  color: #5a5f69;
}

.phone-login-title {
  width: 100%;
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  text-align: center;
  margin-top: 1rem;
}

.phone-login-subtitle {
  color: #6b7280;
  text-align: center;
  margin-bottom: 2rem;
}

.phone-login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 表单字段样式 */
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.phone-input-group {
  display: flex;
  gap: 0.5rem;
}

.country-select {
  width: 80px;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
}

.phone-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.phone-input:focus,
.country-select:focus,
.verification-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 验证按钮样式 */
.captcha-button {
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.captcha-button:hover:not(:disabled) {
  border-color: #9ca3af;
  background-color: #f9fafb;
}

.captcha-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.captcha-button.loading {
  border-style: solid;
  border-color: #3b82f6;
  background-color: #f0f7ff;
}

.captcha-button.completed {
  border-style: solid;
  border-color: #10b981;
  background-color: #f0fdf9;
}

.captcha-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #10b981, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.captcha-text {
  color: #374151;
  font-size: 0.875rem;
}

/* 验证码输入样式 */
.verification-input-group {
  display: flex;
  gap: 0.5rem;
}

.verification-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.send-code-btn {
  padding: 0 1rem;
  background-color: #f3f4f6;
  color: #3b82f6;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s;
}

.send-code-btn:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.send-code-btn:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

/* 提交按钮样式 */
.submit-button {
  width: 100%;
  height: 48px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.submit-button:disabled,
.submit-button.disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.footer-text {
  text-align: center;
  font-size: 0.75rem;
  color: #9ca3af;
}

/* 账号选择页面样式 */
.account-select-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 1rem;
  position: relative;
}

.account-select-title {
  width: 100%;
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  text-align: center;
  margin-top: 1rem; /* 添加顶部边距，与返回按钮分开 */
}

.account-select-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
  margin-bottom: 1.5rem;
  font-weight: normal;
}

.accounts-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.account-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.account-item:hover:not(.logged-in) {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.account-item.selected {
  border-color: #3b82f6;
  background-color: #f0f7ff;
}

.account-item.logged-in {
  opacity: 0.7;
  cursor: not-allowed;
}

.account-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
  font-size: 1.25rem;
}

.account-info {
  flex: 1;
}

.account-name {
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.account-user {
  font-size: 0.875rem;
  color: #6b7280;
}

.account-status {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 0.25rem;
}

.account-check {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.account-footer {
  margin-top: auto;
}

.selected-info {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: center;
  margin-bottom: 1rem;
}

.confirm-login-btn {
  width: 100%;
  height: 48px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.confirm-login-btn:hover {
  background-color: #2563eb;
}

/* 添加错误信息样式 */
.error-message {
  color: #f44336;
  font-size: 12px;
  margin: 5px 0 10px;
  text-align: left;
  padding-left: 5px;
}
