<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Flex Layout</title>
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
        min-width: 800px;
      }
      .layout {
        display: flex;
        flex-direction: column;
        height: 100vh;
        width: 100%;
      }

      .header {
        background-color: #001627;
        color: #f9fafb;
        width: 100%;
        height: 80px;
        flex-shrink: 0;
        font-size: 24px;
        display: flex;
        align-items: center;
        padding-left: 20px;
      }

      .container {
        display: flex;
        flex: 1;
        overflow-x: auto;
      }

      .sidebar {
        width: 200px;
        background-color: #001627;
        color: white;
        flex-shrink: 0;
      }

      .sidebar .item {
        color: #f9fdff;
        margin-bottom: 1rem;
      }

      .nav-link:hover {
        background-color: #334155;
      }
      .nav-link.active {
        background-color: #1878ff;
        font-weight: 500;
      }
      .nav-icon {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .nav-icon-selected {
        width: 16px;
        height: 16px;
        border: 1px solid white;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .nav-icon-dot {
        width: 8px;
        height: 8px;
        background-color: white;
        border-radius: 2px;
      }

      .nav-link {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
      }

      /* 主内容区域样式 */
      .main-content {
        display: flex;
        width: 100%;
        flex-direction: column;
        overflow-x: auto;
        flex: 1;
      }
    </style>
  </head>
  <body>
    <div class="layout">
      <header class="header">研发培训院课件系统</header>
      <div class="container">
        <!-- 左侧边栏 -->
        <aside class="sidebar">
          <div class="item">
            <div class="nav-link active">
              <div class="nav-icon-selected">
                <div class="nav-icon-dot"></div>
              </div>
              <span>笔试管理</span>
            </div>
          </div>
        </aside>
        <!-- 主内容区域 -->
        <main class="main-content">
          <div style="text-align: center">
            <h1
              style="font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem"
            >
              主内容区域
            </h1>
            <p style="color: #374151; margin-bottom: 1rem">
              这个区域会随着页面宽度的变化而自动调整大小
            </p>
            <div class="content-box">
              <p style="font-size: 0.875rem">
                尝试调整浏览器窗口大小，观察这个区域如何响应变化
              </p>
            </div>
          </div>
        </main>
      </div>
    </div>
  </body>
</html>
