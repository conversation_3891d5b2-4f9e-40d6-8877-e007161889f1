import React from "react";
import { useMindMapStore } from "../../store/index";

export interface EdgeProps {
  id: string;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  label?: string;
  labelX?: number;
  labelY?: number;
  className?: string;
  style?: React.CSSProperties;
  strokeWidth?: number;
  strokeColor?: string;
  strokeDasharray?: string;
  animated?: boolean;
  selected?: boolean;
  bendPoint?: { x: number; y: number }; // 添加拐点属性
  lineStyle?: "straight" | "curved" | "polyline"; // 线条样式
}

export function Edge({
  sourceX,
  sourceY,
  targetX,
  targetY,
  label,
  labelX,
  labelY,
  className = "",
  style = {},
  strokeWidth = 2,
  strokeColor = "#888",
  strokeDasharray,
  animated = false,
  selected = false,
  bendPoint,
  lineStyle = "polyline",
}: EdgeProps) {
  const { clearSelection } = useMindMapStore();

  // 处理边点击
  const handleEdgeClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    clearSelection();
    // 可以添加边的选择状态
  };

  // 计算边的路径
  const getPath = () => {
    if (lineStyle === "straight") {
      return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;
    } else if (lineStyle === "curved") {
      // 使用二次贝塞尔曲线，只需要一个控制点
      const dx = targetX - sourceX;
      const dy = targetY - sourceY;

      // 控制点
      const cx = sourceX + dx * 0.2;
      const cy = sourceY + dy * 0.8;

      // 使用二次贝塞尔曲线 Q 命令
      return `M ${sourceX} ${sourceY} Q ${cx} ${cy} ${targetX} ${targetY}`;
    } else if (lineStyle === "polyline" || bendPoint) {
      // 折线样式，第一个转角为直角，第二个转角圆润
      const midX = bendPoint?.x || (sourceX + targetX) / 2;

      // 计算圆润半径
      const radius = Math.min(
        20,
        Math.abs(targetX - midX) * 0.3,
        Math.abs(targetY - sourceY) * 0.3
      );

      // 判断方向
      const goingRight = targetX > midX;
      const goingDown = targetY > sourceY;

      // 第二个转角的圆润处理
      const corner2X = midX + (goingRight ? radius : -radius);
      const corner2Y = targetY - (goingDown ? radius : -radius);

      return `M ${sourceX} ${sourceY} 
              L ${midX} ${sourceY} 
              L ${midX} ${corner2Y}
              Q ${midX} ${targetY} ${corner2X} ${targetY}
              L ${targetX} ${targetY}`;
    } else {
      // 默认直线
      return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;
    }
  };

  const path = getPath();

  // 计算标签位置 - 考虑拐点
  const labelPosition = {
    x: labelX ?? (bendPoint?.x || (sourceX + targetX) / 2),
    y: labelY ?? (bendPoint?.y || (sourceY + targetY) / 2),
  };

  const edgeStyle = {
    stroke: selected ? "#3b82f6" : strokeColor,
    strokeWidth: selected ? strokeWidth + 1 : strokeWidth,
    strokeDasharray,
    ...style,
  };

  return (
    <g className={`edge ${className}`}>
      {/* 边的路径 */}
      <path
        d={path}
        fill="none"
        style={edgeStyle}
        className={`edge-path ${animated ? "animate-pulse" : ""}`}
        onClick={handleEdgeClick}
        cursor="pointer"
      />

      {/* 交互区域（更宽的透明路径） */}
      <path
        d={path}
        fill="none"
        stroke="transparent"
        strokeWidth={20}
        onClick={handleEdgeClick}
        cursor="pointer"
      />

      {/* 边的标签 */}
      {label && (
        <g transform={`translate(${labelPosition.x}, ${labelPosition.y})`}>
          <rect
            x={-label.length * 3 - 4}
            y={-8}
            width={label.length * 6 + 8}
            height={16}
            rx={4}
            fill="white"
            stroke="#ccc"
            strokeWidth={1}
          />
          <text
            textAnchor="middle"
            alignmentBaseline="middle"
            fontSize={12}
            fill="#333"
          >
            {label}
          </text>
        </g>
      )}
    </g>
  );
}
