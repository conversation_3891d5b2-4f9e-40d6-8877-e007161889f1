# 完成练习及小作业

## 1. KAE 部署练习

- ✅ Dockerfile 编写  
- ✅ Nginx 配置  
- ✅ KCICD 流水线构建配置  
- ✅ KLB 负载均衡配置  
- ✅ CDN 内容分发配置  

> 状态：**已完成**

---

## 2. KAE 部署小作业,网关配置

- ✅ 新建func-abtest分支,配置gitlab webhook触发func分支构建(支持func-开头的所有分支)  
- ✅ 复制应用实例部署此func分支(健康检查、日志告警)  
- ✅ KLB上新增同名的网关,Chrome浏览器访问指向此func分支应用,Edge浏览器访问指向  
项目1,(注意网关地址是同一个)   在路由配置时设置Var : 
- ✅ KLB上新增新的网关,xxx-abtest,创建一个新上游组,配置50%流量指向项目1,配置
50%流量指向项目2  
![alt text](3182d9fcc3da1d79fa4cee152caccf42.jpg)
![alt text](ce332c78a6c8270e442f43de3b75a241.jpg)
> 状态：**已完成**

---

## 3. WPS 端内调试与开发小练习

- ✅ 开发前端页面  
- ✅ 实现登录、退出登录、原子权益等按钮  
- ✅ 点击按钮调用对应 JS API 接口（如登录、鉴权、登出等）  

> 状态：**已完成**

---

## 4. WPS 端内开发小作业

- ✅ 新增上传文件按钮  
- ✅ 用户选择本地文件（调用文件选择窗 JS API）  
- ✅ 将文件上传至用户的个人云文档  
- ✅ 项目部署至 KAE 平台  
![alt text](image.png)
![alt text](image-1.png)
> 状态：**已完成**
