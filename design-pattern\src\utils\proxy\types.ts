// 定义服务接口和真实服务
export interface ImageService {
  display(): void
  getImageData(): string
}

// 真实服务 - 高分辨率图片加载
export class HighResolutionImage implements ImageService {
  constructor(private filename: string) {
    this.loadImage()
  }

  private loadImage(): void {
    console.log(`加载高分辨率图片: ${this.filename}`)
  }

  // 新增异步加载方法
  async load(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log(`图片加载完成: ${this.filename}`)
        resolve()
      }, 2000) // 模拟2秒加载时间
    })
  }

  display(): void {
    console.log(`显示高分辨率图片: ${this.filename}`)
  }

  getImageData(): string {
    return `高分辨率图片数据: ${this.filename}`
  }
}
