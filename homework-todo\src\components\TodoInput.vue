<!-- components/TodoInput.vue -->
<template>
  <div class="add-todo-section">
    <div class="input-wrapper">
      <input
        v-model="text"
        placeholder="添加待办事项 (@快速分配参与人)"
        class="todo-input"
        @keyup.enter="createTodo"
      />
      <button type="button" @click="createTodo" :disabled="!text.trim()" class="create-btn">
        新建
      </button>
    </div>

    <div class="todo-options">
      <button class="option-btn" @click="showParticipants = !showParticipants">
        <svg viewBox="0 0 1024 1024" width="18" height="18">
          <path
            d="M725.333333 721.066667l-38.4-38.4H379.733333l-38.4 38.4V768h384v-46.933333z m85.333334-38.4v170.666666H256v-170.666666h4.266667l85.333333-85.333334h375.466667l89.6 85.333334z m-85.333334-320c0 106.666667-85.333333 192-192 192S341.333333 469.333333 341.333333 362.666667 426.666667 170.666667 533.333333 170.666667 725.333333 256 725.333333 362.666667z m-85.333333 0C640 302.933333 593.066667 256 533.333333 256S426.666667 302.933333 426.666667 362.666667s46.933333 106.666667 106.666666 106.666666S640 422.4 640 362.666667z"
            fill="#444444"
          ></path>
        </svg>
        参与人
      </button>
      <div class="divider"></div>

      <!-- 时间选择按钮组 -->
      <div v-if="!selectedTimeOption" class="time-buttons">
        <button class="option-btn" @click="setDueDate('today')">今天</button>
        <button class="option-btn" @click="setDueDate('tomorrow')">明天</button>

        <!-- 使用 TimePopover 组件，以按钮作为触发器 -->
        <TimePopover
          v-model="showTimePopover"
          :custom-date="customDate"
          :custom-time="customTime"
          :enable-time="enableTime"
          :enable-reminder="enableReminder"
          @confirm="handleTimeConfirm"
          @cancel="handleTimeCancel"
        >
          <template #trigger>
            <button class="option-btn" @click="showTimePopover = true">
              <svg viewBox="0 0 1024 1024" width="18" height="18">
                <path
                  d="M554.666667 516.266667l102.4 102.4-59.733334 59.733333-123.733333-123.733333H469.333333V341.333333h85.333334v174.933334zM512 853.333333c-187.733333 0-341.333333-153.6-341.333333-341.333333s153.6-341.333333 341.333333-341.333333 341.333333 153.6 341.333333 341.333333-153.6 341.333333-341.333333 341.333333z m0-85.333333c140.8 0 256-115.2 256-256s-115.2-256-256-256-256 115.2-256 256 115.2 256 256 256z"
                  fill="#444444"
                ></path>
              </svg>
              其他时间
            </button>
          </template>
        </TimePopover>
      </div>

      <!-- 已选择时间显示 -->
      <div v-else class="selected-time-display">
        <button class="selected-time-btn">
          {{ selectedTimeDisplay }}
          <span class="cancel-time" @click.stop="cancelTimeSelection">×</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import TimePopover from './TimePopover.vue'

const emit = defineEmits<{
  create: (todo: { text: string; dueDate?: string; time?: string }) => void
}>()

const text = ref('')
const showParticipants = ref(false)
const showTimePopover = ref(false)
const customDate = ref('')
const customTime = ref('00:00')
const enableTime = ref(false)
const enableReminder = ref(false)

// 选择的时间选项
const selectedTimeOption = ref<'today' | 'tomorrow' | 'custom' | null>(null)
const selectedTimeDisplay = computed(() => {
  if (!selectedTimeOption.value) return ''

  if (selectedTimeOption.value === 'today') {
    return `今天 ${tempTime} 截止`
  } else if (selectedTimeOption.value === 'tomorrow') {
    return `明天 ${tempTime} 截止`
  } else if (tempDueDate) {
    // 格式化日期为更友好的显示
    const date = new Date(tempDueDate)
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${month}月${day}日 ${tempTime} 截止`
  }

  return ''
})

let tempDueDate = ''
let tempTime = ''

function setDueDate(option: 'today' | 'tomorrow') {
  const now = new Date()
  if (option === 'tomorrow') now.setDate(now.getDate() + 1)
  now.setHours(18, 0, 0, 0) // 默认设置为下午6点
  tempDueDate = now.toISOString().split('T')[0]
  tempTime = '18:00'
  selectedTimeOption.value = option
}

function handleTimeConfirm(data: {
  date: string
  time: string
  enableTime: boolean
  enableReminder: boolean
  reminderText: string
}) {
  tempDueDate = data.date
  tempTime = data.enableTime ? data.time : ''
  selectedTimeOption.value = 'custom'
  showTimePopover.value = false

  // 这里可以处理提醒相关的逻辑
  console.log('Time selection confirmed:', data)
}

function handleTimeCancel() {
  showTimePopover.value = false
  console.log('Time selection cancelled')
}

function cancelTimeSelection() {
  selectedTimeOption.value = null
  tempDueDate = ''
  tempTime = ''
}

function createTodo() {
  if (!text.value.trim()) return
  emit('create', {
    text: text.value,
    dueDate: tempDueDate || undefined,
    time: tempTime || undefined,
  })
  text.value = ''
  tempDueDate = ''
  tempTime = ''
  enableTime.value = false
  enableReminder.value = false
  showParticipants.value = false
  selectedTimeOption.value = null
}
</script>

<style scoped>
.add-todo-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.input-wrapper {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  width: 100%;
  position: relative;
}

.todo-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.todo-input:focus {
  border-color: #409eff;
}

.create-btn {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: opacity 0.2s;
}

.create-btn:hover:not(:disabled) {
  background: #337ecc;
}

.create-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.todo-options {
  display: flex;
  align-items: center;
  margin-top: 10px;
  flex-wrap: wrap;
}

.option-btn {
  background: none;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  color: #444;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
}

.option-btn:hover {
  color: #409eff;
}

.divider {
  height: 16px;
  width: 1px;
  background-color: #ddd;
  margin: 0 8px;
}

.option-btn svg {
  vertical-align: middle;
  flex-shrink: 0;
}

.time-buttons {
  display: flex;
  gap: 6px;
}

.selected-time-display {
  display: flex;
  align-items: center;
}

.selected-time-btn {
  background: #f0f7ff;
  border: 1px solid #d0e7ff;
  color: #409eff;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: default;
}

.cancel-time {
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 4px;
  color: #999;
}

.cancel-time:hover {
  color: #f56c6c;
}
</style>
