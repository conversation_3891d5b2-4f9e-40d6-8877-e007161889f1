[2025-08-13T16:47:28.195+08:00] waitForNavigation begin
[2025-08-13T16:47:28.195+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:28.218+08:00] waitForNavigation begin
[2025-08-13T16:47:28.218+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:28.274+08:00] waitForNavigation end
[2025-08-13T16:47:28.274+08:00] screenshotBase64 begin
[2025-08-13T16:47:28.281+08:00] waitForNavigation end
[2025-08-13T16:47:28.281+08:00] evaluate function begin
[2025-08-13T16:47:28.311+08:00] evaluate function end
[2025-08-13T16:47:28.311+08:00] getElementsNodeTree end, cost: 30ms
[2025-08-13T16:47:28.391+08:00] screenshotBase64 end, cost: 117ms
[2025-08-13T16:47:28.392+08:00] evaluate function begin
[2025-08-13T16:47:28.398+08:00] evaluate function end
[2025-08-13T16:47:35.393+08:00] waitForNavigation begin
[2025-08-13T16:47:35.393+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:35.394+08:00] waitForNavigation begin
[2025-08-13T16:47:35.394+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:35.404+08:00] waitForNavigation end
[2025-08-13T16:47:35.404+08:00] screenshotBase64 begin
[2025-08-13T16:47:35.407+08:00] waitForNavigation end
[2025-08-13T16:47:35.407+08:00] evaluate function begin
[2025-08-13T16:47:35.427+08:00] evaluate function end
[2025-08-13T16:47:35.427+08:00] getElementsNodeTree end, cost: 20ms
[2025-08-13T16:47:35.485+08:00] screenshotBase64 end, cost: 81ms
[2025-08-13T16:47:35.487+08:00] waitForNavigation begin
[2025-08-13T16:47:35.488+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:35.496+08:00] waitForNavigation end
[2025-08-13T16:47:35.496+08:00] screenshotBase64 begin
[2025-08-13T16:47:35.548+08:00] screenshotBase64 end, cost: 52ms
[2025-08-13T16:47:35.549+08:00] mouse move to 640, 173
[2025-08-13T16:47:35.557+08:00] mouse click 640, 173
[2025-08-13T16:47:35.761+08:00] waitForNavigation begin
[2025-08-13T16:47:35.762+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:35.770+08:00] waitForNavigation end
[2025-08-13T16:47:35.770+08:00] screenshotBase64 begin
[2025-08-13T16:47:35.823+08:00] screenshotBase64 end, cost: 53ms
[2025-08-13T16:47:38.080+08:00] waitForNavigation begin
[2025-08-13T16:47:38.080+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:38.081+08:00] waitForNavigation begin
[2025-08-13T16:47:38.081+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:38.093+08:00] waitForNavigation end
[2025-08-13T16:47:38.093+08:00] screenshotBase64 begin
[2025-08-13T16:47:38.095+08:00] waitForNavigation end
[2025-08-13T16:47:38.095+08:00] evaluate function begin
[2025-08-13T16:47:38.110+08:00] evaluate function end
[2025-08-13T16:47:38.110+08:00] getElementsNodeTree end, cost: 15ms
[2025-08-13T16:47:38.167+08:00] screenshotBase64 end, cost: 74ms
[2025-08-13T16:47:42.310+08:00] waitForNavigation begin
[2025-08-13T16:47:42.310+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:42.317+08:00] waitForNavigation end
[2025-08-13T16:47:42.317+08:00] screenshotBase64 begin
[2025-08-13T16:47:42.373+08:00] screenshotBase64 end, cost: 56ms
[2025-08-13T16:47:42.373+08:00] keyboard type standard_user
[2025-08-13T16:47:43.775+08:00] waitForNavigation begin
[2025-08-13T16:47:43.775+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:43.781+08:00] waitForNavigation end
[2025-08-13T16:47:43.782+08:00] screenshotBase64 begin
[2025-08-13T16:47:43.839+08:00] screenshotBase64 end, cost: 57ms
[2025-08-13T16:47:45.790+08:00] waitForNavigation begin
[2025-08-13T16:47:45.790+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:45.791+08:00] waitForNavigation begin
[2025-08-13T16:47:45.791+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:45.799+08:00] waitForNavigation end
[2025-08-13T16:47:45.799+08:00] screenshotBase64 begin
[2025-08-13T16:47:45.801+08:00] waitForNavigation end
[2025-08-13T16:47:45.801+08:00] evaluate function begin
[2025-08-13T16:47:45.815+08:00] evaluate function end
[2025-08-13T16:47:45.816+08:00] getElementsNodeTree end, cost: 15ms
[2025-08-13T16:47:45.863+08:00] screenshotBase64 end, cost: 64ms
[2025-08-13T16:47:52.035+08:00] waitForNavigation begin
[2025-08-13T16:47:52.035+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:52.036+08:00] waitForNavigation begin
[2025-08-13T16:47:52.036+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:52.044+08:00] waitForNavigation end
[2025-08-13T16:47:52.044+08:00] screenshotBase64 begin
[2025-08-13T16:47:52.047+08:00] waitForNavigation end
[2025-08-13T16:47:52.047+08:00] evaluate function begin
[2025-08-13T16:47:52.063+08:00] evaluate function end
[2025-08-13T16:47:52.063+08:00] getElementsNodeTree end, cost: 16ms
[2025-08-13T16:47:52.112+08:00] screenshotBase64 end, cost: 68ms
[2025-08-13T16:47:52.115+08:00] waitForNavigation begin
[2025-08-13T16:47:52.115+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:52.121+08:00] waitForNavigation end
[2025-08-13T16:47:52.122+08:00] screenshotBase64 begin
[2025-08-13T16:47:52.181+08:00] screenshotBase64 end, cost: 59ms
[2025-08-13T16:47:52.181+08:00] mouse move to 640, 227
[2025-08-13T16:47:52.190+08:00] mouse click 640, 227
[2025-08-13T16:47:52.403+08:00] waitForNavigation begin
[2025-08-13T16:47:52.403+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:52.411+08:00] waitForNavigation end
[2025-08-13T16:47:52.411+08:00] screenshotBase64 begin
[2025-08-13T16:47:52.463+08:00] screenshotBase64 end, cost: 52ms
[2025-08-13T16:47:54.382+08:00] waitForNavigation begin
[2025-08-13T16:47:54.382+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:54.382+08:00] waitForNavigation begin
[2025-08-13T16:47:54.382+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:54.392+08:00] waitForNavigation end
[2025-08-13T16:47:54.392+08:00] screenshotBase64 begin
[2025-08-13T16:47:54.393+08:00] waitForNavigation end
[2025-08-13T16:47:54.393+08:00] evaluate function begin
[2025-08-13T16:47:54.410+08:00] evaluate function end
[2025-08-13T16:47:54.410+08:00] getElementsNodeTree end, cost: 17ms
[2025-08-13T16:47:54.454+08:00] screenshotBase64 end, cost: 62ms
[2025-08-13T16:47:58.668+08:00] waitForNavigation begin
[2025-08-13T16:47:58.668+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:47:58.676+08:00] waitForNavigation end
[2025-08-13T16:47:58.676+08:00] screenshotBase64 begin
[2025-08-13T16:47:58.729+08:00] screenshotBase64 end, cost: 53ms
[2025-08-13T16:47:58.729+08:00] keyboard type secret_sauce
[2025-08-13T16:48:00.058+08:00] waitForNavigation begin
[2025-08-13T16:48:00.058+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:00.064+08:00] waitForNavigation end
[2025-08-13T16:48:00.064+08:00] screenshotBase64 begin
[2025-08-13T16:48:00.113+08:00] screenshotBase64 end, cost: 49ms
[2025-08-13T16:48:02.133+08:00] waitForNavigation begin
[2025-08-13T16:48:02.133+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:02.133+08:00] waitForNavigation begin
[2025-08-13T16:48:02.134+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:02.141+08:00] waitForNavigation end
[2025-08-13T16:48:02.141+08:00] screenshotBase64 begin
[2025-08-13T16:48:02.142+08:00] waitForNavigation end
[2025-08-13T16:48:02.142+08:00] evaluate function begin
[2025-08-13T16:48:02.161+08:00] evaluate function end
[2025-08-13T16:48:02.162+08:00] getElementsNodeTree end, cost: 20ms
[2025-08-13T16:48:02.214+08:00] screenshotBase64 end, cost: 73ms
[2025-08-13T16:48:08.168+08:00] waitForNavigation begin
[2025-08-13T16:48:08.168+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:08.168+08:00] waitForNavigation begin
[2025-08-13T16:48:08.168+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:08.176+08:00] waitForNavigation end
[2025-08-13T16:48:08.176+08:00] screenshotBase64 begin
[2025-08-13T16:48:08.177+08:00] waitForNavigation end
[2025-08-13T16:48:08.178+08:00] evaluate function begin
[2025-08-13T16:48:08.191+08:00] evaluate function end
[2025-08-13T16:48:08.191+08:00] getElementsNodeTree end, cost: 13ms
[2025-08-13T16:48:08.236+08:00] screenshotBase64 end, cost: 60ms
[2025-08-13T16:48:08.237+08:00] waitForNavigation begin
[2025-08-13T16:48:08.237+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:08.243+08:00] waitForNavigation end
[2025-08-13T16:48:08.243+08:00] screenshotBase64 begin
[2025-08-13T16:48:08.297+08:00] screenshotBase64 end, cost: 54ms
[2025-08-13T16:48:08.297+08:00] mouse move to 640, 326
[2025-08-13T16:48:08.306+08:00] mouse click 640, 326
[2025-08-13T16:48:08.512+08:00] waitForNavigation begin
[2025-08-13T16:48:08.512+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:08.522+08:00] waitForNavigation end
[2025-08-13T16:48:08.522+08:00] screenshotBase64 begin
[2025-08-13T16:48:08.619+08:00] screenshotBase64 end, cost: 97ms
[2025-08-13T16:48:10.498+08:00] waitForNavigation begin
[2025-08-13T16:48:10.498+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:10.499+08:00] waitForNavigation begin
[2025-08-13T16:48:10.499+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:10.508+08:00] waitForNavigation end
[2025-08-13T16:48:10.508+08:00] screenshotBase64 begin
[2025-08-13T16:48:10.509+08:00] waitForNavigation end
[2025-08-13T16:48:10.509+08:00] evaluate function begin
[2025-08-13T16:48:10.541+08:00] evaluate function end
[2025-08-13T16:48:10.541+08:00] getElementsNodeTree end, cost: 32ms
[2025-08-13T16:48:10.618+08:00] screenshotBase64 end, cost: 110ms
[2025-08-13T16:48:15.100+08:00] waitForNavigation begin
[2025-08-13T16:48:15.101+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:15.107+08:00] waitForNavigation end
[2025-08-13T16:48:15.107+08:00] screenshotBase64 begin
[2025-08-13T16:48:15.158+08:00] screenshotBase64 end, cost: 51ms
[2025-08-13T16:48:15.363+08:00] waitForNavigation begin
[2025-08-13T16:48:15.363+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:15.373+08:00] waitForNavigation end
[2025-08-13T16:48:15.374+08:00] screenshotBase64 begin
[2025-08-13T16:48:15.441+08:00] screenshotBase64 end, cost: 67ms
[2025-08-13T16:48:15.560+08:00] waitForNavigation begin
[2025-08-13T16:48:15.560+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:15.571+08:00] waitForNavigation end
[2025-08-13T16:48:15.571+08:00] screenshotBase64 begin
[2025-08-13T16:48:15.624+08:00] screenshotBase64 end, cost: 53ms
[2025-08-13T16:48:17.520+08:00] waitForNavigation begin
[2025-08-13T16:48:17.520+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:17.520+08:00] waitForNavigation begin
[2025-08-13T16:48:17.520+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:17.527+08:00] waitForNavigation end
[2025-08-13T16:48:17.527+08:00] screenshotBase64 begin
[2025-08-13T16:48:17.529+08:00] waitForNavigation end
[2025-08-13T16:48:17.529+08:00] evaluate function begin
[2025-08-13T16:48:17.560+08:00] evaluate function end
[2025-08-13T16:48:17.561+08:00] getElementsNodeTree end, cost: 32ms
[2025-08-13T16:48:17.620+08:00] screenshotBase64 end, cost: 93ms
[2025-08-13T16:48:19.779+08:00] waitForNavigation begin
[2025-08-13T16:48:19.779+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:19.780+08:00] waitForNavigation begin
[2025-08-13T16:48:19.780+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:19.787+08:00] waitForNavigation end
[2025-08-13T16:48:19.787+08:00] screenshotBase64 begin
[2025-08-13T16:48:19.789+08:00] waitForNavigation end
[2025-08-13T16:48:19.789+08:00] evaluate function begin
[2025-08-13T16:48:19.828+08:00] evaluate function end
[2025-08-13T16:48:19.828+08:00] getElementsNodeTree end, cost: 39ms
[2025-08-13T16:48:19.881+08:00] screenshotBase64 end, cost: 94ms
[2025-08-13T16:48:37.463+08:00] waitForNavigation begin
[2025-08-13T16:48:37.463+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:37.464+08:00] waitForNavigation begin
[2025-08-13T16:48:37.464+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:37.472+08:00] waitForNavigation end
[2025-08-13T16:48:37.472+08:00] screenshotBase64 begin
[2025-08-13T16:48:37.474+08:00] waitForNavigation end
[2025-08-13T16:48:37.474+08:00] evaluate function begin
[2025-08-13T16:48:37.505+08:00] evaluate function end
[2025-08-13T16:48:37.505+08:00] getElementsNodeTree end, cost: 31ms
[2025-08-13T16:48:37.566+08:00] screenshotBase64 end, cost: 94ms
[2025-08-13T16:48:44.225+08:00] waitForNavigation begin
[2025-08-13T16:48:44.225+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:44.226+08:00] waitForNavigation begin
[2025-08-13T16:48:44.226+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:44.233+08:00] waitForNavigation end
[2025-08-13T16:48:44.233+08:00] screenshotBase64 begin
[2025-08-13T16:48:44.235+08:00] waitForNavigation end
[2025-08-13T16:48:44.235+08:00] evaluate function begin
[2025-08-13T16:48:44.264+08:00] evaluate function end
[2025-08-13T16:48:44.264+08:00] getElementsNodeTree end, cost: 29ms
[2025-08-13T16:48:44.331+08:00] screenshotBase64 end, cost: 98ms
[2025-08-13T16:48:44.332+08:00] waitForNavigation begin
[2025-08-13T16:48:44.332+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:44.339+08:00] waitForNavigation end
[2025-08-13T16:48:44.339+08:00] screenshotBase64 begin
[2025-08-13T16:48:44.414+08:00] screenshotBase64 end, cost: 75ms
[2025-08-13T16:48:44.414+08:00] mouse move to 1048, 356
[2025-08-13T16:48:44.420+08:00] mouse click 1048, 356
[2025-08-13T16:48:44.635+08:00] waitForNavigation begin
[2025-08-13T16:48:44.635+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:44.641+08:00] waitForNavigation end
[2025-08-13T16:48:44.641+08:00] screenshotBase64 begin
[2025-08-13T16:48:44.696+08:00] screenshotBase64 end, cost: 55ms
[2025-08-13T16:48:46.687+08:00] waitForNavigation begin
[2025-08-13T16:48:46.687+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:46.688+08:00] waitForNavigation begin
[2025-08-13T16:48:46.688+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:46.699+08:00] waitForNavigation end
[2025-08-13T16:48:46.700+08:00] screenshotBase64 begin
[2025-08-13T16:48:46.702+08:00] waitForNavigation end
[2025-08-13T16:48:46.702+08:00] evaluate function begin
[2025-08-13T16:48:46.740+08:00] evaluate function end
[2025-08-13T16:48:46.740+08:00] getElementsNodeTree end, cost: 38ms
[2025-08-13T16:48:46.805+08:00] screenshotBase64 end, cost: 105ms
[2025-08-13T16:48:53.242+08:00] waitForNavigation begin
[2025-08-13T16:48:53.242+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:53.252+08:00] waitForNavigation end
[2025-08-13T16:48:53.252+08:00] screenshotBase64 begin
[2025-08-13T16:48:53.313+08:00] screenshotBase64 end, cost: 61ms
[2025-08-13T16:48:53.513+08:00] waitForNavigation begin
[2025-08-13T16:48:53.513+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:53.518+08:00] waitForNavigation end
[2025-08-13T16:48:53.518+08:00] screenshotBase64 begin
[2025-08-13T16:48:53.572+08:00] screenshotBase64 end, cost: 54ms
[2025-08-13T16:48:55.631+08:00] waitForNavigation begin
[2025-08-13T16:48:55.631+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:55.632+08:00] waitForNavigation begin
[2025-08-13T16:48:55.632+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:48:55.640+08:00] waitForNavigation end
[2025-08-13T16:48:55.640+08:00] screenshotBase64 begin
[2025-08-13T16:48:55.641+08:00] waitForNavigation end
[2025-08-13T16:48:55.641+08:00] evaluate function begin
[2025-08-13T16:48:55.668+08:00] evaluate function end
[2025-08-13T16:48:55.668+08:00] getElementsNodeTree end, cost: 27ms
[2025-08-13T16:48:55.721+08:00] screenshotBase64 end, cost: 81ms
[2025-08-13T16:49:03.429+08:00] waitForNavigation begin
[2025-08-13T16:49:03.429+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:03.429+08:00] waitForNavigation begin
[2025-08-13T16:49:03.429+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:03.436+08:00] waitForNavigation end
[2025-08-13T16:49:03.437+08:00] screenshotBase64 begin
[2025-08-13T16:49:03.439+08:00] waitForNavigation end
[2025-08-13T16:49:03.439+08:00] evaluate function begin
[2025-08-13T16:49:03.468+08:00] evaluate function end
[2025-08-13T16:49:03.468+08:00] getElementsNodeTree end, cost: 29ms
[2025-08-13T16:49:03.530+08:00] screenshotBase64 end, cost: 93ms
[2025-08-13T16:49:03.533+08:00] waitForNavigation begin
[2025-08-13T16:49:03.533+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:03.541+08:00] waitForNavigation end
[2025-08-13T16:49:03.541+08:00] screenshotBase64 begin
[2025-08-13T16:49:03.598+08:00] screenshotBase64 end, cost: 57ms
[2025-08-13T16:49:03.598+08:00] mouse move to 493, 608
[2025-08-13T16:49:03.603+08:00] mouse click 493, 608
[2025-08-13T16:49:03.804+08:00] waitForNavigation begin
[2025-08-13T16:49:03.804+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:03.811+08:00] waitForNavigation end
[2025-08-13T16:49:03.811+08:00] screenshotBase64 begin
[2025-08-13T16:49:03.864+08:00] screenshotBase64 end, cost: 53ms
[2025-08-13T16:49:05.672+08:00] waitForNavigation begin
[2025-08-13T16:49:05.672+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:05.672+08:00] waitForNavigation begin
[2025-08-13T16:49:05.672+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:05.683+08:00] waitForNavigation end
[2025-08-13T16:49:05.683+08:00] screenshotBase64 begin
[2025-08-13T16:49:05.686+08:00] waitForNavigation end
[2025-08-13T16:49:05.686+08:00] evaluate function begin
[2025-08-13T16:49:05.724+08:00] evaluate function end
[2025-08-13T16:49:05.724+08:00] getElementsNodeTree end, cost: 38ms
[2025-08-13T16:49:05.788+08:00] screenshotBase64 end, cost: 105ms
[2025-08-13T16:49:10.000+08:00] waitForNavigation begin
[2025-08-13T16:49:10.000+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:10.010+08:00] waitForNavigation end
[2025-08-13T16:49:10.010+08:00] screenshotBase64 begin
[2025-08-13T16:49:10.078+08:00] screenshotBase64 end, cost: 68ms
[2025-08-13T16:49:10.290+08:00] waitForNavigation begin
[2025-08-13T16:49:10.290+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:10.298+08:00] waitForNavigation end
[2025-08-13T16:49:10.298+08:00] screenshotBase64 begin
[2025-08-13T16:49:10.353+08:00] screenshotBase64 end, cost: 55ms
[2025-08-13T16:49:12.905+08:00] waitForNavigation begin
[2025-08-13T16:49:12.905+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:12.905+08:00] waitForNavigation begin
[2025-08-13T16:49:12.905+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:12.913+08:00] waitForNavigation end
[2025-08-13T16:49:12.913+08:00] screenshotBase64 begin
[2025-08-13T16:49:12.915+08:00] waitForNavigation end
[2025-08-13T16:49:12.915+08:00] evaluate function begin
[2025-08-13T16:49:12.942+08:00] evaluate function end
[2025-08-13T16:49:12.942+08:00] getElementsNodeTree end, cost: 27ms
[2025-08-13T16:49:12.997+08:00] screenshotBase64 end, cost: 84ms
[2025-08-13T16:49:19.971+08:00] waitForNavigation begin
[2025-08-13T16:49:19.971+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:19.972+08:00] waitForNavigation begin
[2025-08-13T16:49:19.972+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:19.979+08:00] waitForNavigation end
[2025-08-13T16:49:19.979+08:00] screenshotBase64 begin
[2025-08-13T16:49:19.981+08:00] waitForNavigation end
[2025-08-13T16:49:19.981+08:00] evaluate function begin
[2025-08-13T16:49:20.015+08:00] evaluate function end
[2025-08-13T16:49:20.016+08:00] getElementsNodeTree end, cost: 35ms
[2025-08-13T16:49:20.082+08:00] screenshotBase64 end, cost: 103ms
[2025-08-13T16:49:20.084+08:00] waitForNavigation begin
[2025-08-13T16:49:20.084+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:20.093+08:00] waitForNavigation end
[2025-08-13T16:49:20.093+08:00] screenshotBase64 begin
[2025-08-13T16:49:20.150+08:00] screenshotBase64 end, cost: 57ms
[2025-08-13T16:49:20.150+08:00] mouse move to 1225, 30
[2025-08-13T16:49:20.153+08:00] mouse click 1225, 30
[2025-08-13T16:49:20.362+08:00] waitForNavigation begin
[2025-08-13T16:49:20.362+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:20.371+08:00] waitForNavigation end
[2025-08-13T16:49:20.371+08:00] screenshotBase64 begin
[2025-08-13T16:49:20.428+08:00] screenshotBase64 end, cost: 57ms
[2025-08-13T16:49:22.678+08:00] waitForNavigation begin
[2025-08-13T16:49:22.678+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:22.678+08:00] waitForNavigation begin
[2025-08-13T16:49:22.679+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:22.695+08:00] waitForNavigation end
[2025-08-13T16:49:22.696+08:00] screenshotBase64 begin
[2025-08-13T16:49:22.709+08:00] waitForNavigation end
[2025-08-13T16:49:22.710+08:00] evaluate function begin
[2025-08-13T16:49:22.740+08:00] evaluate function end
[2025-08-13T16:49:22.740+08:00] getElementsNodeTree end, cost: 30ms
[2025-08-13T16:49:22.788+08:00] screenshotBase64 end, cost: 92ms
[2025-08-13T16:49:26.460+08:00] waitForNavigation begin
[2025-08-13T16:49:26.460+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:26.466+08:00] waitForNavigation end
[2025-08-13T16:49:26.466+08:00] screenshotBase64 begin
[2025-08-13T16:49:26.517+08:00] screenshotBase64 end, cost: 51ms
[2025-08-13T16:49:26.729+08:00] waitForNavigation begin
[2025-08-13T16:49:26.730+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:26.739+08:00] waitForNavigation end
[2025-08-13T16:49:26.739+08:00] screenshotBase64 begin
[2025-08-13T16:49:26.794+08:00] screenshotBase64 end, cost: 55ms
[2025-08-13T16:49:29.066+08:00] waitForNavigation begin
[2025-08-13T16:49:29.066+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:29.067+08:00] waitForNavigation begin
[2025-08-13T16:49:29.067+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:29.074+08:00] waitForNavigation end
[2025-08-13T16:49:29.075+08:00] screenshotBase64 begin
[2025-08-13T16:49:29.076+08:00] waitForNavigation end
[2025-08-13T16:49:29.076+08:00] evaluate function begin
[2025-08-13T16:49:29.094+08:00] evaluate function end
[2025-08-13T16:49:29.094+08:00] getElementsNodeTree end, cost: 18ms
[2025-08-13T16:49:29.141+08:00] screenshotBase64 end, cost: 66ms
[2025-08-13T16:49:35.005+08:00] waitForNavigation begin
[2025-08-13T16:49:35.005+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:35.005+08:00] waitForNavigation begin
[2025-08-13T16:49:35.005+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:35.012+08:00] waitForNavigation end
[2025-08-13T16:49:35.012+08:00] screenshotBase64 begin
[2025-08-13T16:49:35.014+08:00] waitForNavigation end
[2025-08-13T16:49:35.015+08:00] evaluate function begin
[2025-08-13T16:49:35.034+08:00] evaluate function end
[2025-08-13T16:49:35.034+08:00] getElementsNodeTree end, cost: 19ms
[2025-08-13T16:49:35.090+08:00] screenshotBase64 end, cost: 78ms
[2025-08-13T16:49:35.091+08:00] waitForNavigation begin
[2025-08-13T16:49:35.091+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:35.097+08:00] waitForNavigation end
[2025-08-13T16:49:35.097+08:00] screenshotBase64 begin
[2025-08-13T16:49:35.153+08:00] screenshotBase64 end, cost: 56ms
[2025-08-13T16:49:35.153+08:00] mouse move to 1140, 622
[2025-08-13T16:49:35.161+08:00] mouse click 1140, 622
[2025-08-13T16:49:35.377+08:00] waitForNavigation begin
[2025-08-13T16:49:35.377+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:35.386+08:00] waitForNavigation end
[2025-08-13T16:49:35.386+08:00] screenshotBase64 begin
[2025-08-13T16:49:35.435+08:00] screenshotBase64 end, cost: 49ms
[2025-08-13T16:49:37.438+08:00] waitForNavigation begin
[2025-08-13T16:49:37.438+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:37.439+08:00] waitForNavigation begin
[2025-08-13T16:49:37.439+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:37.449+08:00] waitForNavigation end
[2025-08-13T16:49:37.449+08:00] screenshotBase64 begin
[2025-08-13T16:49:37.452+08:00] waitForNavigation end
[2025-08-13T16:49:37.452+08:00] evaluate function begin
[2025-08-13T16:49:37.479+08:00] evaluate function end
[2025-08-13T16:49:37.479+08:00] getElementsNodeTree end, cost: 27ms
[2025-08-13T16:49:37.526+08:00] screenshotBase64 end, cost: 77ms
[2025-08-13T16:49:41.226+08:00] waitForNavigation begin
[2025-08-13T16:49:41.226+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:41.233+08:00] waitForNavigation end
[2025-08-13T16:49:41.233+08:00] screenshotBase64 begin
[2025-08-13T16:49:41.285+08:00] screenshotBase64 end, cost: 52ms
[2025-08-13T16:49:41.496+08:00] waitForNavigation begin
[2025-08-13T16:49:41.496+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:41.503+08:00] waitForNavigation end
[2025-08-13T16:49:41.503+08:00] screenshotBase64 begin
[2025-08-13T16:49:41.550+08:00] screenshotBase64 end, cost: 47ms
[2025-08-13T16:49:43.689+08:00] waitForNavigation begin
[2025-08-13T16:49:43.689+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:43.690+08:00] waitForNavigation begin
[2025-08-13T16:49:43.690+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:43.699+08:00] waitForNavigation end
[2025-08-13T16:49:43.699+08:00] screenshotBase64 begin
[2025-08-13T16:49:43.700+08:00] waitForNavigation end
[2025-08-13T16:49:43.700+08:00] evaluate function begin
[2025-08-13T16:49:43.716+08:00] evaluate function end
[2025-08-13T16:49:43.716+08:00] getElementsNodeTree end, cost: 16ms
[2025-08-13T16:49:43.786+08:00] screenshotBase64 end, cost: 87ms
[2025-08-13T16:49:50.451+08:00] waitForNavigation begin
[2025-08-13T16:49:50.451+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:50.452+08:00] waitForNavigation begin
[2025-08-13T16:49:50.452+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:50.463+08:00] waitForNavigation end
[2025-08-13T16:49:50.463+08:00] screenshotBase64 begin
[2025-08-13T16:49:50.464+08:00] waitForNavigation end
[2025-08-13T16:49:50.464+08:00] evaluate function begin
[2025-08-13T16:49:50.486+08:00] evaluate function end
[2025-08-13T16:49:50.486+08:00] getElementsNodeTree end, cost: 22ms
[2025-08-13T16:49:50.534+08:00] screenshotBase64 end, cost: 71ms
[2025-08-13T16:49:50.536+08:00] waitForNavigation begin
[2025-08-13T16:49:50.536+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:50.542+08:00] waitForNavigation end
[2025-08-13T16:49:50.542+08:00] screenshotBase64 begin
[2025-08-13T16:49:50.592+08:00] screenshotBase64 end, cost: 50ms
[2025-08-13T16:49:50.592+08:00] mouse move to 632, 274
[2025-08-13T16:49:50.603+08:00] mouse click 632, 274
[2025-08-13T16:49:50.814+08:00] waitForNavigation begin
[2025-08-13T16:49:50.814+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:50.821+08:00] waitForNavigation end
[2025-08-13T16:49:50.821+08:00] screenshotBase64 begin
[2025-08-13T16:49:50.875+08:00] screenshotBase64 end, cost: 54ms
[2025-08-13T16:49:52.732+08:00] waitForNavigation begin
[2025-08-13T16:49:52.732+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:52.733+08:00] waitForNavigation begin
[2025-08-13T16:49:52.733+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:52.745+08:00] waitForNavigation end
[2025-08-13T16:49:52.745+08:00] screenshotBase64 begin
[2025-08-13T16:49:52.748+08:00] waitForNavigation end
[2025-08-13T16:49:52.749+08:00] evaluate function begin
[2025-08-13T16:49:52.771+08:00] evaluate function end
[2025-08-13T16:49:52.771+08:00] getElementsNodeTree end, cost: 22ms
[2025-08-13T16:49:52.818+08:00] screenshotBase64 end, cost: 73ms
[2025-08-13T16:49:56.896+08:00] waitForNavigation begin
[2025-08-13T16:49:56.896+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:56.904+08:00] waitForNavigation end
[2025-08-13T16:49:56.904+08:00] screenshotBase64 begin
[2025-08-13T16:49:56.951+08:00] screenshotBase64 end, cost: 47ms
[2025-08-13T16:49:56.951+08:00] keyboard type zhu
[2025-08-13T16:49:57.433+08:00] waitForNavigation begin
[2025-08-13T16:49:57.434+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:57.439+08:00] waitForNavigation end
[2025-08-13T16:49:57.439+08:00] screenshotBase64 begin
[2025-08-13T16:49:57.494+08:00] screenshotBase64 end, cost: 55ms
[2025-08-13T16:49:59.291+08:00] waitForNavigation begin
[2025-08-13T16:49:59.291+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:59.291+08:00] waitForNavigation begin
[2025-08-13T16:49:59.292+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:49:59.301+08:00] waitForNavigation end
[2025-08-13T16:49:59.301+08:00] screenshotBase64 begin
[2025-08-13T16:49:59.305+08:00] waitForNavigation end
[2025-08-13T16:49:59.305+08:00] evaluate function begin
[2025-08-13T16:49:59.322+08:00] evaluate function end
[2025-08-13T16:49:59.322+08:00] getElementsNodeTree end, cost: 17ms
[2025-08-13T16:49:59.374+08:00] screenshotBase64 end, cost: 73ms
[2025-08-13T16:50:02.532+08:00] waitForNavigation begin
[2025-08-13T16:50:02.532+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:02.539+08:00] waitForNavigation end
[2025-08-13T16:50:02.539+08:00] screenshotBase64 begin
[2025-08-13T16:50:02.592+08:00] screenshotBase64 end, cost: 53ms
[2025-08-13T16:50:02.797+08:00] waitForNavigation begin
[2025-08-13T16:50:02.797+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:02.804+08:00] waitForNavigation end
[2025-08-13T16:50:02.804+08:00] screenshotBase64 begin
[2025-08-13T16:50:02.857+08:00] screenshotBase64 end, cost: 53ms
[2025-08-13T16:50:04.901+08:00] waitForNavigation begin
[2025-08-13T16:50:04.901+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:04.902+08:00] waitForNavigation begin
[2025-08-13T16:50:04.902+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:04.908+08:00] waitForNavigation end
[2025-08-13T16:50:04.909+08:00] screenshotBase64 begin
[2025-08-13T16:50:04.910+08:00] waitForNavigation end
[2025-08-13T16:50:04.910+08:00] evaluate function begin
[2025-08-13T16:50:04.926+08:00] evaluate function end
[2025-08-13T16:50:04.926+08:00] getElementsNodeTree end, cost: 16ms
[2025-08-13T16:50:04.976+08:00] screenshotBase64 end, cost: 67ms
[2025-08-13T16:50:17.533+08:00] waitForNavigation begin
[2025-08-13T16:50:17.534+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:17.534+08:00] waitForNavigation begin
[2025-08-13T16:50:17.534+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:17.541+08:00] waitForNavigation end
[2025-08-13T16:50:17.541+08:00] screenshotBase64 begin
[2025-08-13T16:50:17.543+08:00] waitForNavigation end
[2025-08-13T16:50:17.543+08:00] evaluate function begin
[2025-08-13T16:50:17.564+08:00] evaluate function end
[2025-08-13T16:50:17.564+08:00] getElementsNodeTree end, cost: 21ms
[2025-08-13T16:50:17.608+08:00] screenshotBase64 end, cost: 67ms
[2025-08-13T16:50:17.610+08:00] waitForNavigation begin
[2025-08-13T16:50:17.610+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:17.615+08:00] waitForNavigation end
[2025-08-13T16:50:17.615+08:00] screenshotBase64 begin
[2025-08-13T16:50:17.665+08:00] screenshotBase64 end, cost: 50ms
[2025-08-13T16:50:17.665+08:00] mouse move to 632, 328
[2025-08-13T16:50:17.669+08:00] mouse click 632, 328
[2025-08-13T16:50:17.875+08:00] waitForNavigation begin
[2025-08-13T16:50:17.876+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:17.883+08:00] waitForNavigation end
[2025-08-13T16:50:17.883+08:00] screenshotBase64 begin
[2025-08-13T16:50:17.932+08:00] screenshotBase64 end, cost: 49ms
[2025-08-13T16:50:25.442+08:00] waitForNavigation begin
[2025-08-13T16:50:25.442+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:25.442+08:00] waitForNavigation begin
[2025-08-13T16:50:25.442+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:25.450+08:00] waitForNavigation end
[2025-08-13T16:50:25.450+08:00] screenshotBase64 begin
[2025-08-13T16:50:25.451+08:00] waitForNavigation end
[2025-08-13T16:50:25.451+08:00] evaluate function begin
[2025-08-13T16:50:25.476+08:00] evaluate function end
[2025-08-13T16:50:25.476+08:00] getElementsNodeTree end, cost: 25ms
[2025-08-13T16:50:25.523+08:00] screenshotBase64 end, cost: 73ms
[2025-08-13T16:50:29.640+08:00] waitForNavigation begin
[2025-08-13T16:50:29.640+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:29.651+08:00] waitForNavigation end
[2025-08-13T16:50:29.651+08:00] screenshotBase64 begin
[2025-08-13T16:50:29.714+08:00] screenshotBase64 end, cost: 63ms
[2025-08-13T16:50:29.714+08:00] keyboard type zhu
[2025-08-13T16:50:30.202+08:00] waitForNavigation begin
[2025-08-13T16:50:30.202+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:30.211+08:00] waitForNavigation end
[2025-08-13T16:50:30.212+08:00] screenshotBase64 begin
[2025-08-13T16:50:30.267+08:00] screenshotBase64 end, cost: 55ms
[2025-08-13T16:50:33.265+08:00] waitForNavigation begin
[2025-08-13T16:50:33.265+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:33.266+08:00] waitForNavigation begin
[2025-08-13T16:50:33.266+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:33.271+08:00] waitForNavigation end
[2025-08-13T16:50:33.271+08:00] screenshotBase64 begin
[2025-08-13T16:50:33.274+08:00] waitForNavigation end
[2025-08-13T16:50:33.274+08:00] evaluate function begin
[2025-08-13T16:50:33.294+08:00] evaluate function end
[2025-08-13T16:50:33.294+08:00] getElementsNodeTree end, cost: 20ms
[2025-08-13T16:50:33.359+08:00] screenshotBase64 end, cost: 88ms
[2025-08-13T16:50:36.936+08:00] waitForNavigation begin
[2025-08-13T16:50:36.936+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:36.943+08:00] waitForNavigation end
[2025-08-13T16:50:36.943+08:00] screenshotBase64 begin
[2025-08-13T16:50:36.998+08:00] screenshotBase64 end, cost: 55ms
[2025-08-13T16:50:37.209+08:00] waitForNavigation begin
[2025-08-13T16:50:37.209+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:37.217+08:00] waitForNavigation end
[2025-08-13T16:50:37.217+08:00] screenshotBase64 begin
[2025-08-13T16:50:37.270+08:00] screenshotBase64 end, cost: 53ms
[2025-08-13T16:50:39.759+08:00] waitForNavigation begin
[2025-08-13T16:50:39.759+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:39.759+08:00] waitForNavigation begin
[2025-08-13T16:50:39.759+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:39.765+08:00] waitForNavigation end
[2025-08-13T16:50:39.765+08:00] screenshotBase64 begin
[2025-08-13T16:50:39.766+08:00] waitForNavigation end
[2025-08-13T16:50:39.766+08:00] evaluate function begin
[2025-08-13T16:50:39.785+08:00] evaluate function end
[2025-08-13T16:50:39.785+08:00] getElementsNodeTree end, cost: 19ms
[2025-08-13T16:50:39.839+08:00] screenshotBase64 end, cost: 74ms
[2025-08-13T16:50:45.901+08:00] waitForNavigation begin
[2025-08-13T16:50:45.901+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:45.902+08:00] waitForNavigation begin
[2025-08-13T16:50:45.902+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:45.907+08:00] waitForNavigation end
[2025-08-13T16:50:45.907+08:00] screenshotBase64 begin
[2025-08-13T16:50:45.908+08:00] waitForNavigation end
[2025-08-13T16:50:45.908+08:00] evaluate function begin
[2025-08-13T16:50:45.929+08:00] evaluate function end
[2025-08-13T16:50:45.929+08:00] getElementsNodeTree end, cost: 21ms
[2025-08-13T16:50:45.981+08:00] screenshotBase64 end, cost: 74ms
[2025-08-13T16:50:45.982+08:00] waitForNavigation begin
[2025-08-13T16:50:45.982+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:45.988+08:00] waitForNavigation end
[2025-08-13T16:50:45.988+08:00] screenshotBase64 begin
[2025-08-13T16:50:46.046+08:00] screenshotBase64 end, cost: 58ms
[2025-08-13T16:50:46.046+08:00] mouse move to 632, 382
[2025-08-13T16:50:46.054+08:00] mouse click 632, 382
[2025-08-13T16:50:46.265+08:00] waitForNavigation begin
[2025-08-13T16:50:46.265+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:46.271+08:00] waitForNavigation end
[2025-08-13T16:50:46.271+08:00] screenshotBase64 begin
[2025-08-13T16:50:46.320+08:00] screenshotBase64 end, cost: 49ms
[2025-08-13T16:50:48.239+08:00] waitForNavigation begin
[2025-08-13T16:50:48.239+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:48.239+08:00] waitForNavigation begin
[2025-08-13T16:50:48.239+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:48.247+08:00] waitForNavigation end
[2025-08-13T16:50:48.247+08:00] screenshotBase64 begin
[2025-08-13T16:50:48.249+08:00] waitForNavigation end
[2025-08-13T16:50:48.249+08:00] evaluate function begin
[2025-08-13T16:50:48.274+08:00] evaluate function end
[2025-08-13T16:50:48.274+08:00] getElementsNodeTree end, cost: 25ms
[2025-08-13T16:50:48.320+08:00] screenshotBase64 end, cost: 73ms
[2025-08-13T16:50:55.878+08:00] waitForNavigation begin
[2025-08-13T16:50:55.878+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:55.878+08:00] waitForNavigation begin
[2025-08-13T16:50:55.878+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:55.885+08:00] waitForNavigation end
[2025-08-13T16:50:55.885+08:00] screenshotBase64 begin
[2025-08-13T16:50:55.886+08:00] waitForNavigation end
[2025-08-13T16:50:55.886+08:00] evaluate function begin
[2025-08-13T16:50:55.909+08:00] evaluate function end
[2025-08-13T16:50:55.909+08:00] getElementsNodeTree end, cost: 23ms
[2025-08-13T16:50:55.973+08:00] screenshotBase64 end, cost: 88ms
[2025-08-13T16:50:55.974+08:00] waitForNavigation begin
[2025-08-13T16:50:55.974+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:55.981+08:00] waitForNavigation end
[2025-08-13T16:50:55.981+08:00] screenshotBase64 begin
[2025-08-13T16:50:56.036+08:00] screenshotBase64 end, cost: 55ms
[2025-08-13T16:50:56.037+08:00] mouse move to 632, 382
[2025-08-13T16:50:56.047+08:00] mouse click 632, 382
[2025-08-13T16:50:56.263+08:00] waitForNavigation begin
[2025-08-13T16:50:56.263+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:56.269+08:00] waitForNavigation end
[2025-08-13T16:50:56.269+08:00] screenshotBase64 begin
[2025-08-13T16:50:56.319+08:00] screenshotBase64 end, cost: 50ms
[2025-08-13T16:50:58.265+08:00] waitForNavigation begin
[2025-08-13T16:50:58.265+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:58.265+08:00] waitForNavigation begin
[2025-08-13T16:50:58.265+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:50:58.272+08:00] waitForNavigation end
[2025-08-13T16:50:58.272+08:00] screenshotBase64 begin
[2025-08-13T16:50:58.272+08:00] waitForNavigation end
[2025-08-13T16:50:58.272+08:00] evaluate function begin
[2025-08-13T16:50:58.290+08:00] evaluate function end
[2025-08-13T16:50:58.290+08:00] getElementsNodeTree end, cost: 18ms
[2025-08-13T16:50:58.337+08:00] screenshotBase64 end, cost: 65ms
[2025-08-13T16:51:05.384+08:00] waitForNavigation begin
[2025-08-13T16:51:05.384+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:05.385+08:00] waitForNavigation begin
[2025-08-13T16:51:05.385+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:05.391+08:00] waitForNavigation end
[2025-08-13T16:51:05.391+08:00] screenshotBase64 begin
[2025-08-13T16:51:05.392+08:00] waitForNavigation end
[2025-08-13T16:51:05.392+08:00] evaluate function begin
[2025-08-13T16:51:05.412+08:00] evaluate function end
[2025-08-13T16:51:05.412+08:00] getElementsNodeTree end, cost: 20ms
[2025-08-13T16:51:05.462+08:00] screenshotBase64 end, cost: 71ms
[2025-08-13T16:51:05.463+08:00] waitForNavigation begin
[2025-08-13T16:51:05.463+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:05.469+08:00] waitForNavigation end
[2025-08-13T16:51:05.469+08:00] screenshotBase64 begin
[2025-08-13T16:51:05.519+08:00] screenshotBase64 end, cost: 50ms
[2025-08-13T16:51:05.519+08:00] mouse move to 632, 382
[2025-08-13T16:51:05.530+08:00] mouse click 632, 382
[2025-08-13T16:51:05.739+08:00] waitForNavigation begin
[2025-08-13T16:51:05.739+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:05.744+08:00] waitForNavigation end
[2025-08-13T16:51:05.744+08:00] screenshotBase64 begin
[2025-08-13T16:51:05.796+08:00] screenshotBase64 end, cost: 52ms
[2025-08-13T16:51:07.765+08:00] waitForNavigation begin
[2025-08-13T16:51:07.765+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:07.765+08:00] waitForNavigation begin
[2025-08-13T16:51:07.765+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:07.773+08:00] waitForNavigation end
[2025-08-13T16:51:07.773+08:00] screenshotBase64 begin
[2025-08-13T16:51:07.775+08:00] waitForNavigation end
[2025-08-13T16:51:07.775+08:00] evaluate function begin
[2025-08-13T16:51:07.798+08:00] evaluate function end
[2025-08-13T16:51:07.799+08:00] getElementsNodeTree end, cost: 24ms
[2025-08-13T16:51:07.847+08:00] screenshotBase64 end, cost: 74ms
[2025-08-13T16:51:16.575+08:00] waitForNavigation begin
[2025-08-13T16:51:16.575+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:16.575+08:00] waitForNavigation begin
[2025-08-13T16:51:16.575+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:16.582+08:00] waitForNavigation end
[2025-08-13T16:51:16.582+08:00] screenshotBase64 begin
[2025-08-13T16:51:16.583+08:00] waitForNavigation end
[2025-08-13T16:51:16.583+08:00] evaluate function begin
[2025-08-13T16:51:16.603+08:00] evaluate function end
[2025-08-13T16:51:16.603+08:00] getElementsNodeTree end, cost: 20ms
[2025-08-13T16:51:16.653+08:00] screenshotBase64 end, cost: 71ms
[2025-08-13T16:51:16.655+08:00] waitForNavigation begin
[2025-08-13T16:51:16.655+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:16.663+08:00] waitForNavigation end
[2025-08-13T16:51:16.663+08:00] screenshotBase64 begin
[2025-08-13T16:51:16.722+08:00] screenshotBase64 end, cost: 59ms
[2025-08-13T16:51:16.722+08:00] mouse move to 492, 385
[2025-08-13T16:51:16.729+08:00] mouse click 492, 385
[2025-08-13T16:51:16.938+08:00] waitForNavigation begin
[2025-08-13T16:51:16.938+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:16.945+08:00] waitForNavigation end
[2025-08-13T16:51:16.945+08:00] screenshotBase64 begin
[2025-08-13T16:51:16.995+08:00] screenshotBase64 end, cost: 50ms
[2025-08-13T16:51:18.842+08:00] waitForNavigation begin
[2025-08-13T16:51:18.842+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:18.842+08:00] waitForNavigation begin
[2025-08-13T16:51:18.842+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:18.849+08:00] waitForNavigation end
[2025-08-13T16:51:18.849+08:00] screenshotBase64 begin
[2025-08-13T16:51:18.850+08:00] waitForNavigation end
[2025-08-13T16:51:18.850+08:00] evaluate function begin
[2025-08-13T16:51:18.873+08:00] evaluate function end
[2025-08-13T16:51:18.873+08:00] getElementsNodeTree end, cost: 23ms
[2025-08-13T16:51:18.919+08:00] screenshotBase64 end, cost: 70ms
[2025-08-13T16:51:24.144+08:00] waitForNavigation begin
[2025-08-13T16:51:24.144+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:24.150+08:00] waitForNavigation end
[2025-08-13T16:51:24.150+08:00] screenshotBase64 begin
[2025-08-13T16:51:24.203+08:00] screenshotBase64 end, cost: 53ms
[2025-08-13T16:51:24.203+08:00] keyboard type 123
[2025-08-13T16:51:24.690+08:00] waitForNavigation begin
[2025-08-13T16:51:24.690+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:24.696+08:00] waitForNavigation end
[2025-08-13T16:51:24.696+08:00] screenshotBase64 begin
[2025-08-13T16:51:24.743+08:00] screenshotBase64 end, cost: 47ms
[2025-08-13T16:51:26.683+08:00] waitForNavigation begin
[2025-08-13T16:51:26.683+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:26.683+08:00] waitForNavigation begin
[2025-08-13T16:51:26.683+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:26.691+08:00] waitForNavigation end
[2025-08-13T16:51:26.692+08:00] screenshotBase64 begin
[2025-08-13T16:51:26.693+08:00] waitForNavigation end
[2025-08-13T16:51:26.693+08:00] evaluate function begin
[2025-08-13T16:51:26.717+08:00] evaluate function end
[2025-08-13T16:51:26.717+08:00] getElementsNodeTree end, cost: 24ms
[2025-08-13T16:51:26.770+08:00] screenshotBase64 end, cost: 78ms
[2025-08-13T16:51:30.695+08:00] waitForNavigation begin
[2025-08-13T16:51:30.695+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:30.703+08:00] waitForNavigation end
[2025-08-13T16:51:30.703+08:00] screenshotBase64 begin
[2025-08-13T16:51:30.755+08:00] screenshotBase64 end, cost: 52ms
[2025-08-13T16:51:30.963+08:00] waitForNavigation begin
[2025-08-13T16:51:30.963+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:30.970+08:00] waitForNavigation end
[2025-08-13T16:51:30.970+08:00] screenshotBase64 begin
[2025-08-13T16:51:31.020+08:00] screenshotBase64 end, cost: 50ms
[2025-08-13T16:51:33.296+08:00] waitForNavigation begin
[2025-08-13T16:51:33.296+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:33.297+08:00] waitForNavigation begin
[2025-08-13T16:51:33.297+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:33.311+08:00] waitForNavigation end
[2025-08-13T16:51:33.311+08:00] screenshotBase64 begin
[2025-08-13T16:51:33.314+08:00] waitForNavigation end
[2025-08-13T16:51:33.314+08:00] evaluate function begin
[2025-08-13T16:51:33.341+08:00] evaluate function end
[2025-08-13T16:51:33.341+08:00] getElementsNodeTree end, cost: 27ms
[2025-08-13T16:51:33.387+08:00] screenshotBase64 end, cost: 76ms
[2025-08-13T16:51:47.826+08:00] waitForNavigation begin
[2025-08-13T16:51:47.826+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:47.827+08:00] waitForNavigation begin
[2025-08-13T16:51:47.827+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:47.833+08:00] waitForNavigation end
[2025-08-13T16:51:47.833+08:00] screenshotBase64 begin
[2025-08-13T16:51:47.835+08:00] waitForNavigation end
[2025-08-13T16:51:47.835+08:00] evaluate function begin
[2025-08-13T16:51:47.856+08:00] evaluate function end
[2025-08-13T16:51:47.856+08:00] getElementsNodeTree end, cost: 21ms
[2025-08-13T16:51:47.904+08:00] screenshotBase64 end, cost: 71ms
[2025-08-13T16:51:47.905+08:00] waitForNavigation begin
[2025-08-13T16:51:47.905+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:47.911+08:00] waitForNavigation end
[2025-08-13T16:51:47.911+08:00] screenshotBase64 begin
[2025-08-13T16:51:47.960+08:00] screenshotBase64 end, cost: 49ms
[2025-08-13T16:51:47.960+08:00] mouse move to 1140, 619
[2025-08-13T16:51:47.971+08:00] mouse click 1140, 619
[2025-08-13T16:51:48.185+08:00] waitForNavigation begin
[2025-08-13T16:51:48.185+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:48.192+08:00] waitForNavigation end
[2025-08-13T16:51:48.192+08:00] screenshotBase64 begin
[2025-08-13T16:51:48.246+08:00] screenshotBase64 end, cost: 54ms
[2025-08-13T16:51:50.098+08:00] waitForNavigation begin
[2025-08-13T16:51:50.098+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:50.098+08:00] waitForNavigation begin
[2025-08-13T16:51:50.098+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:50.106+08:00] waitForNavigation end
[2025-08-13T16:51:50.106+08:00] screenshotBase64 begin
[2025-08-13T16:51:50.107+08:00] waitForNavigation end
[2025-08-13T16:51:50.107+08:00] evaluate function begin
[2025-08-13T16:51:50.137+08:00] evaluate function end
[2025-08-13T16:51:50.137+08:00] getElementsNodeTree end, cost: 30ms
[2025-08-13T16:51:50.187+08:00] screenshotBase64 end, cost: 81ms
[2025-08-13T16:51:54.070+08:00] waitForNavigation begin
[2025-08-13T16:51:54.070+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:54.076+08:00] waitForNavigation end
[2025-08-13T16:51:54.076+08:00] screenshotBase64 begin
[2025-08-13T16:51:54.132+08:00] screenshotBase64 end, cost: 56ms
[2025-08-13T16:51:54.334+08:00] waitForNavigation begin
[2025-08-13T16:51:54.334+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:54.342+08:00] waitForNavigation end
[2025-08-13T16:51:54.342+08:00] screenshotBase64 begin
[2025-08-13T16:51:54.397+08:00] screenshotBase64 end, cost: 55ms
[2025-08-13T16:51:54.537+08:00] waitForNavigation begin
[2025-08-13T16:51:54.537+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:54.547+08:00] waitForNavigation end
[2025-08-13T16:51:54.547+08:00] screenshotBase64 begin
[2025-08-13T16:51:54.611+08:00] screenshotBase64 end, cost: 64ms
[2025-08-13T16:51:56.610+08:00] waitForNavigation begin
[2025-08-13T16:51:56.610+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:56.610+08:00] waitForNavigation begin
[2025-08-13T16:51:56.610+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:56.616+08:00] waitForNavigation end
[2025-08-13T16:51:56.616+08:00] screenshotBase64 begin
[2025-08-13T16:51:56.619+08:00] waitForNavigation end
[2025-08-13T16:51:56.619+08:00] evaluate function begin
[2025-08-13T16:51:56.651+08:00] evaluate function end
[2025-08-13T16:51:56.651+08:00] getElementsNodeTree end, cost: 32ms
[2025-08-13T16:51:56.706+08:00] screenshotBase64 end, cost: 90ms
[2025-08-13T16:51:58.696+08:00] waitForNavigation begin
[2025-08-13T16:51:58.696+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:58.696+08:00] waitForNavigation begin
[2025-08-13T16:51:58.696+08:00] waitForNavigation timeout: 5000
[2025-08-13T16:51:58.702+08:00] waitForNavigation end
[2025-08-13T16:51:58.702+08:00] screenshotBase64 begin
[2025-08-13T16:51:58.703+08:00] waitForNavigation end
[2025-08-13T16:51:58.703+08:00] evaluate function begin
[2025-08-13T16:51:58.724+08:00] evaluate function end
[2025-08-13T16:51:58.725+08:00] getElementsNodeTree end, cost: 22ms
[2025-08-13T16:51:58.780+08:00] screenshotBase64 end, cost: 78ms
