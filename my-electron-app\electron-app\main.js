const { app, BrowserWindow, ipcMain, dialog } = require("electron");
const path = require("path");
const fs = require("fs").promises;

function createWindow() {
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, "preload.js"), //预加载脚本，作为主进程与渲染进程的安全桥梁
      nodeIntegration: false,
      contextIsolation: true,
      webviewTag: true, // 启用webview标签
    },
  });

  win.loadFile("index.html");

  // 开发环境打开开发者工具
  if (process.env.NODE_ENV === "development") {
    win.webContents.openDevTools();
  }
}

app.whenReady().then(createWindow);

app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) createWindow();
});

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") app.quit();
});

// SDK方法实现
// 读取文件方法
ipcMain.handle("read-file", async (event, options) => {
  try {
    const { filePath, range } = options;
    const [start, end] = range;

    // 验证参数
    // 检查文件路径
    if (!filePath || typeof filePath !== "string") {
      return {
        result: { code: 1, msg: "文件路径无效" },
      };
    }

    // 检查范围
    if (
      !Array.isArray(range) ||
      range.length !== 2 ||
      start < 0 ||
      end < start
    ) {
      return {
        result: { code: 2, msg: "读取范围无效" },
      };
    }

    // 检查单次读取大小不超过1M
    const maxSize = 1024 * 1024; // 1MB
    if (end - start > maxSize) {
      return {
        result: { code: 3, msg: "单次读取大小不能超过1MB" },
      };
    }

    // 构建完整文件路径
    const fullPath = path.resolve(filePath);

    // 检查文件是否存在
    try {
      await fs.access(fullPath);
    } catch (error) {
      return {
        result: { code: 4, msg: "文件不存在或无法访问" },
      };
    }

    // 获取文件信息
    const stats = await fs.stat(fullPath);
    const fileSize = stats.size;

    // 调整读取范围
    const actualEnd = Math.min(end, fileSize);
    const actualStart = Math.min(start, fileSize);

    // 读取文件内容
    const fileHandle = await fs.open(fullPath, "r");
    const buffer = Buffer.alloc(actualEnd - actualStart);
    await fileHandle.read(buffer, 0, actualEnd - actualStart, actualStart);
    await fileHandle.close();

    // 获取文件扩展名作为mimeType
    const ext = path.extname(fullPath).toLowerCase();
    const mimeTypeMap = {
      ".txt": "text/plain",
      ".json": "application/json",
      ".html": "text/html",
      ".css": "text/css",
      ".js": "application/javascript",
      ".png": "image/png",
      ".jpg": "image/jpeg",
      ".jpeg": "image/jpeg",
      ".gif": "image/gif",
      ".pdf": "application/pdf",
    };
    const mimeType = mimeTypeMap[ext] || null;

    return {
      result: { code: 0, msg: "读取成功" },
      data: {
        mimeType,
        arrayBuffer: buffer.buffer.slice(
          buffer.byteOffset,
          buffer.byteOffset + buffer.byteLength
        ),
        finished: actualEnd >= fileSize,
      },
    };
  } catch (error) {
    console.error("读取文件错误:", error);
    return {
      result: { code: 5, msg: `读取文件失败: ${error.message}` },
    };
  }
});

// 打开URL方法
ipcMain.handle("open-url", async (event, options) => {
  try {
    const { url, options: urlOptions } = options;

    // 参数验证
    if (!url || typeof url !== "string") {
      return { code: 1, msg: "URL无效" };
    }

    if (!urlOptions || !urlOptions.type) {
      return { code: 2, msg: "打开方式未指定" };
    }

    const { type } = urlOptions;

    if (type === "browserwindow") {
      // 创建新的浏览器窗口
      const newWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
        },
      });

      await newWindow.loadURL(url);
      return { code: 0, msg: "在新窗口中打开成功" };
    } else if (type === "webview") {
      // webview方式打开(通过IPC通知渲染进程)
      const mainWindow = BrowserWindow.getAllWindows()[0];
      if (mainWindow) {
        mainWindow.webContents.send("open-in-webview", url);
        return { code: 0, msg: "在webview中打开成功" };
      } else {
        return { code: 3, msg: "主窗口不存在" };
      }
    } else {
      return { code: 4, msg: "不支持的打开方式" };
    }
  } catch (error) {
    console.error("打开URL错误:", error);
    return { code: 5, msg: `打开URL失败: ${error.message}` };
  }
});

ipcMain.handle("file-dialog:open-file", async () => {
  const result = await dialog.showOpenDialog({
    properties: ["openFile"],
  });

  return {
    canceled: result.canceled,
    filePaths: result.filePaths,
  };
});
