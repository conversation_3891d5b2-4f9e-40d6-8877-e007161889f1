# Todo应用测试用例文档

## 组件测试

### TodoStats.test.js 测试要点

- 基本数据渲染
  - 验证统计数据正确显示
  - 确保所有统计指标都正确展示
- 默认值处理
  - 当没有提供stats属性时应显示默认值
  - 没有待办项时不应显示完成率
- 按钮状态
  - 没有已完成项时"清除已完成"按钮应禁用
  - 有已完成项时按钮应可用
- 事件处理
  - 点击"清除已完成"按钮应调用传入的回调函数

### TodoInput.test.js 测试要点

- 默认值与自定义值
  - 验证默认占位符和按钮文本
  - 测试自定义占位符和按钮文本
- 输入处理
  - 文本输入应正确更新状态
  - 实施最大长度限制
- 表单验证
  - 输入为空时提交按钮应禁用
  - 输入有效时按钮应启用
- 事件处理
  - 点击添加按钮应调用onSubmit并清空输入
  - Enter键应提交表单
  - Escape键应取消并清空输入
- 模拟依赖
  - 模拟todoUtils中的validateTodoText函数以控制测试行为

### TodoItem.test.js 测试要点

- 基本渲染测试
  - 验证待办项内容正确显示
  - 验证复选框状态与待办项完成状态一致
  - 验证完成的待办项样式正确应用
- 日期显示测试
  - 当showDate为true时应显示日期
  - 日期格式化功能正常工作
  - 当showDate为false时不应显示日期
- 用户交互测试
  - 点击复选框应调用onToggle回调函数并传递正确的id
  - 点击编辑按钮应调用onEdit回调函数并传递整个待办项对象
  - 点击删除按钮应调用onDelete回调函数并传递正确的id
  - 点击整个待办项也应触发状态切换
- 组件属性测试
  - 验证默认props的行为
  - 验证可选props的正确应用

### TodoApp测试用例说明

- 完整依赖模拟
  - 模拟了useTodoStore hook，以控制测试数据和行为
  - 模拟了todoUtils中的工具函数，但保留了基本逻辑以确保测试的真实性
- 组件渲染测试
  - 验证标题、过滤器和输入框的正确渲染
  - 确保待办项列表正确显示
- 用户交互测试
  - 测试添加新待办项的流程
  - 测试切换待办项状态
  - 测试删除待办项
  - 测试清除已完成待办项
- 过滤功能测试
  - 测试"全部"/"进行中"/"已完成"三个过滤器
  - 验证过滤器点击后调用了正确的函数
- 空状态测试
  - 测试没有待办项时的空状态
  - 测试没有进行中待办项时的空状态
  - 测试没有已完成待办项时的空状态

## 功能测试

### useTodoStore.test.js 测试要点

- 初始化测试
  - 验证初始状态为空数组
  - 验证初始计数器都为0
- 待办项操作测试
  - 添加待办项测试
    - 验证能成功添加有效待办项
    - 验证添加空待办项会失败
    - 验证新增待办项的结构和默认值
  - 待办项切换状态测试
    - 验证能正确切换待办项的完成状态
    - 验证切换状态后计数器正确更新
  - 待办项删除测试
    - 验证能正确删除指定待办项
    - 验证删除后剩余待办项正确
  - 待办项更新测试
    - 验证能正确更新待办项内容
  - 清除已完成测试
    - 验证能正确清除所有已完成待办项
    - 验证清除后只保留未完成项
- 计数器测试
  - 验证完成项计数器准确性
  - 验证未完成项计数器准确性
  - 验证总数计数器准确性
- 过滤功能测试
  - 验证"全部"过滤器返回所有待办项
  - 验证"进行中"过滤器只返回未完成待办项
  - 验证"已完成"过滤器只返回已完成待办项

### todoUtils.test.js 测试要点

- validateTodoText 函数测试
  - 验证空字符串应返回false
  - 验证纯空格字符串应返回false
  - 验证null值应返回false
  - 验证非字符串类型应返回false
  - 验证有效文本应返回true
- generateTodoId 函数测试
  - 验证生成的ID是否唯一
  - 验证生成的ID是数字类型
- formatTodoDate 函数测试
  - 验证日期格式化结果是否包含年份信息
  - 验证格式化结果是字符串类型
  - 验证处理null值时返回空字符串
- filterTodos 函数测试
  - 验证"全部"过滤器返回所有待办项
  - 验证"进行中"过滤器只返回未完成待办项
  - 验证"已完成"过滤器只返回已完成待办项
- sortTodos 函数测试
  - 验证按创建时间排序（默认最新的在前面）
  - 验证按文本内容字母顺序排序
  - 验证按完成状态排序（未完成的在前面）
- getTodoStats 函数测试
  - 验证总数统计准确性
  - 验证已完成项统计准确性
  - 验证未完成项统计准确性
  - 验证完成百分比计算准确性