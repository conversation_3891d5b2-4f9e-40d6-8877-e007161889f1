import type { ButtonComponent, ButtonVariant, ButtonSize } from './button/type'
import { PrimaryButton, IconButton } from './button/buttons'

type ButtonOptions = {
  text: string
  variant?: ButtonVariant
  size?: ButtonSize
  disabled?: boolean
  icon?: string
}

// 按钮工厂
export class ButtonFactory {
  static createButton(options: ButtonOptions): ButtonComponent {
    const { text, variant = 'primary', size = 'md', disabled = false, icon } = options

    if (icon) {
      return new IconButton(text, icon, size, disabled, variant)
    }

    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'danger':
        return new PrimaryButton(text, size, disabled, variant)
      case 'text':
        return new PrimaryButton(text, size, disabled, 'text')
      default:
        throw new Error(`Invalid button variant: ${variant}`)
    }
  }
}
