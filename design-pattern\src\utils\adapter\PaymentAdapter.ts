// 实现支付适配器
import type { ModernPaymentSystem } from './types'
import { LegacyPaymentService } from './types'

export class PaymentAdapter implements ModernPaymentSystem {
  private legacyService: LegacyPaymentService

  constructor() {
    this.legacyService = new LegacyPaymentService()
  }

  async pay(amount: number) {
    return new Promise<{ success: boolean; transactionId: string }>((resolve) => {
      this.legacyService.makePayment(amount, (success) => {
        resolve({
          success,
          transactionId: `txn_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        })
      })
    })
  }

  async refund(transactionId: string) {
    return new Promise<boolean>((resolve) => {
      // 模拟异步操作
      setTimeout(() => {
        const success = this.legacyService.issueRefund(transactionId.split('_')[1])
        resolve(success)
      }, 800)
    })
  }
}
