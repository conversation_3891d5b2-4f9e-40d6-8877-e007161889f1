import React from 'react'
import { formatTodoDate } from '../utils/todoUtils.js'
import './TodoItem.css'

const TodoItem = ({ 
  todo, 
  showDate = false,
  onToggle,
  onDelete,
  onEdit
}) => {
  const handleToggle = () => {
    onToggle(todo.id)
  }

  const handleDelete = (e) => {
    e.stopPropagation()
    onDelete(todo.id)
  }

  const handleEdit = (e) => {
    e.stopPropagation()
    onEdit(todo)
  }

  const formatDate = (date) => {
    return formatTodoDate(date)
  }

  return (
    <div
      className={`todo-item ${todo.completed ? 'completed' : ''}`}
      onClick={handleToggle}
    >
      <div className="todo-content">
        <input
          type="checkbox"
          checked={todo.completed}
          onChange={handleToggle}
          onClick={(e) => e.stopPropagation()}
          className="todo-checkbox"
        />
        <span className="todo-text">{todo.text}</span>
        {showDate && (
          <span className="todo-date">
            {formatDate(todo.createdAt)}
          </span>
        )}
      </div>
      <div className="todo-actions">
        <button 
          onClick={handleEdit}
          className="todo-edit-btn"
          title="编辑"
        >
          ✏️
        </button>
        <button 
          onClick={handleDelete}
          className="todo-delete-btn"
          title="删除"
        >
          🗑️
        </button>
      </div>
    </div>
  )
}

export default TodoItem 