<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <div id="app"></div>
    <script>
      class BaseButton {
        constructor(config = {}) {
          // 核心属性初始化
          this.text = config.text || "按钮";
          this.size = config.size || "medium";
          this.state = config.state || "normal";
          this.className = config.className || "";

          // 创建button DOM元素
          this.element = document.createElement("button");

          // 调用渲染方法
          this.render();
        }

        render() {
          // 更新按钮文本
          this.element.textContent = this.text;

          // 构建class名称：基础类名 + 尺寸类名
          let classNames = ["btn", `btn-${this.size}`];
          if (this.className) {
            classNames.push(this.className);
          }
          this.element.className = classNames.join(" ");

          // 根据状态设置disabled属性和额外样式
          switch (this.state) {
            case "disabled":
              this.element.disabled = true;
              this.element.classList.add("btn-disabled");
              break;
            case "loading":
              this.element.disabled = true;
              this.element.classList.add("btn-loading");
              break;
            case "normal":
            default:
              this.element.disabled = false;
              this.element.classList.remove("btn-disabled", "btn-loading");
              break;
          }
        }

        on(eventType, callback) {
          // 绑定事件监听器
          this.element.addEventListener(eventType, callback);

          // 返回当前实例，实现链式调用
          return this;
        }

        setState(newState) {
          // 验证状态值
          const validStates = ["normal", "disabled", "loading"];
          if (!validStates.includes(newState)) {
            return `错误：无效的状态值 "${newState}"，有效值为：${validStates.join(
              ", "
            )}`;
          }

          const oldState = this.state;
          this.state = newState;

          // 调用render方法更新显示
          this.render();

          return `状态已从 "${oldState}" 切换为 "${newState}"`;
        }

        getElement() {
          // 返回当前按钮的DOM元素对象
          return this.element;
        }
      }

      // 扩展按钮类，继承自 BaseButton
      class AdvancedButton extends BaseButton {
        constructor(config = {}) {
          super(config);

          // 新增属性
          this.icon = config.icon || "";
          this.loadingText = config.loadingText || "加载中...";

          // 重新渲染以应用新属性
          this.render();
        }

        render() {
          // 根据状态选择显示文本
          const displayText =
            this.state === "loading" ? this.loadingText : this.text;

          // 清空按钮内容
          this.element.innerHTML = "";

          // 如果有图标，添加图标元素
          if (this.icon) {
            const iconElement = document.createElement("i");
            iconElement.className = this.icon;
            this.element.appendChild(iconElement);

            // 如果有文本，在图标后添加空格
            if (displayText) {
              this.element.appendChild(
                document.createTextNode(" " + displayText)
              );
            }
          } else {
            // 没有图标时直接设置文本
            this.element.textContent = displayText;
          }

          // 构建class名称：基础类名 + 尺寸类名 + 自定义类名
          let classNames = ["btn", `btn-${this.size}`];
          if (this.className) {
            classNames.push(this.className);
          }
          this.element.className = classNames.join(" ");

          // 根据状态设置disabled属性和额外样式
          switch (this.state) {
            case "disabled":
              this.element.disabled = true;
              this.element.classList.add("btn-disabled");
              break;
            case "loading":
              this.element.disabled = true;
              this.element.classList.add("btn-loading");
              break;
            case "normal":
            default:
              this.element.disabled = false;
              this.element.classList.remove("btn-disabled", "btn-loading");
              break;
          }
        }

        toggleLoading(isLoading) {
          // 根据参数快速切换loading状态
          const newState = isLoading ? "loading" : "normal";
          this.setState(newState);
          return this.state;
        }
      }

      // 按钮组类
      class ButtonGroup {
        constructor(buttons = [], layout = "horizontal") {
          // 初始化属性
          this.buttons = [...buttons]; // 复制数组
          this.layout = layout;

          // 创建容器元素
          this.container = document.createElement("div");

          // 渲染按钮组
          this.render();
        }

        addButton(button) {
          // 验证传入的是否为按钮实例
          if (!(button instanceof BaseButton)) {
            return "错误：只能添加 BaseButton 或其子类的实例";
          }

          // 添加到数组
          this.buttons.push(button);

          // 重新渲染
          this.render();

          return `成功添加按钮："${button.text}"`;
        }

        removeButton(button) {
          // 查找按钮在数组中的索引
          const index = this.buttons.indexOf(button);

          if (index === -1) {
            return "错误：未找到要移除的按钮";
          }

          // 从数组中移除
          this.buttons.splice(index, 1);

          // 重新渲染
          this.render();

          return `成功移除按钮："${button.text}"`;
        }

        render() {
          // 清空容器
          this.container.innerHTML = "";

          // 设置容器的class
          this.container.className = `btn-group btn-group-${this.layout}`;

          // 将所有按钮元素添加到容器中
          this.buttons.forEach((button) => {
            this.container.appendChild(button.getElement());
          });
        }

        mount(parentElement) {
          // 将容器添加到父元素中
          if (parentElement && parentElement.appendChild) {
            parentElement.appendChild(this.container);
            return "按钮组已成功挂载到页面";
          } else {
            return "错误：无效的父元素";
          }
        }
      }

      // 测试代码
      window.addEventListener("DOMContentLoaded", function () {
        //当浏览器完全加载 HTML 并构建完 DOM 树后触发
        // 创建 2 个基础按钮实例
        const btn1 = new BaseButton({
          text: "按钮1",
          size: "small",
        });

        const btn2 = new BaseButton({
          text: "按钮2",
          size: "large",
        });

        // 为第一个按钮绑定点击事件
        btn1.on("click", function () {
          console.log("点击按钮1");
        });

        // 创建 1 个扩展按钮实例
        const advBtn = new AdvancedButton({
          text: "搜索",
          icon: "icon-search",
          loadingText: "搜索中...",
          size: "medium",
        });

        // 测试 toggleLoading 方法
        console.log("切换加载状态：", advBtn.toggleLoading(true));
        setTimeout(() => {
          console.log("恢复正常状态：", advBtn.toggleLoading(false));
        }, 2000);

        // 创建按钮组实例，设置垂直布局
        const buttonGroup = new ButtonGroup([btn1, btn2, advBtn], "vertical");

        // 挂载到页面 body
        console.log(buttonGroup.mount(document.body));

        // 移除一个基础按钮
        setTimeout(() => {
          console.log(buttonGroup.removeButton(btn2));

          // 添加一个新的扩展按钮
          const newAdvBtn = new AdvancedButton({
            text: "扩展按钮",
            icon: "icon-delete",
            size: "small",
            className: "danger-btn",
          });

          console.log(buttonGroup.addButton(newAdvBtn));
        }, 3000);

        // 测试禁用状态
        setTimeout(() => {
          console.log("切换按钮1为禁用状态：", btn1.setState("disabled"));
        }, 5000);
      });
    </script>
  </body>
</html>
