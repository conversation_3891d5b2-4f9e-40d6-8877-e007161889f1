import { useState } from "react";
import "./MobilePage.css";
import LoginForm from "@/components/common/forms/LoginForm";
import ConsentModal from "@/components/common/consent/ConsentModal";
import MoreLogin from "@/components/moreLogin/MoreLogin";
import AccountSelect from "@/components/common/account/AccountSelect";
import type { User } from "@/types/user";

// 移除空接口，直接使用React.FC
const MobilePage: React.FC = () => {
  const [step, setStep] = useState<"form" | "account-select" | "more-login">(
    "form"
  );
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [showModal, setShowModal] = useState(false);

  // 处理登录成功
  const handleLoginSuccess = (
    usersList: User[],
    defaultSelectedIds: number[]
  ) => {
    setUsers(usersList);
    setSelectedUserIds(defaultSelectedIds);
    setStep("account-select");
  };

  // 处理登录方式点击
  const handleLoginMethodClick = (method: string) => {
    // 如果没有同意隐私协议，则显示弹窗
    if (!agreeTerms) {
      setShowModal(true);
      // 记录当前点击的方法，以便在同意隐私协议后跳转到对应页面
      sessionStorage.setItem("lastClickedMethod", method);
    } else {
      // console.log(`使用 ${method} 登录`);
      // 如果点击的是"更多"且已同意隐私协议，则显示更多登录页面
      if (method === "more") {
        setStep("more-login");
      }
    }
  };

  // 处理确认登录
  const handleConfirmLogin = (selectedIds: number[]) => {
    // console.log("确认登录账号:", selectedIds);
    alert(selectedIds + "登录成功");
  };

  // 处理返回主登录页的回调函数
  const handleBackToMain = () => {
    setStep("form");
  };

  // 账号选择页面
  if (step === "account-select") {
    return (
      <div className="mobile-page">
        <AccountSelect
          users={users}
          initialSelectedIds={selectedUserIds}
          onBack={handleBackToMain}
          onConfirm={handleConfirmLogin}
          isMobile={true}
        />
      </div>
    );
  }

  // 更多登录页面
  if (step === "more-login") {
    return (
      <div className="mobile-page">
        <MoreLogin onBack={handleBackToMain} />
      </div>
    );
  }

  // 主登录页面
  return (
    <div className="mobile-page-wrapper">
      <div className="mobile-page">
        <div className="mobile-logo"></div>

        <div className="mobile-login-container">
          <LoginForm
            onLoginSuccess={handleLoginSuccess}
            showBackButton={false}
            showAutoLogin={true}
            title="短信验证码登录"
            subtitle="使用金山办公在线服务账号登录"
            isMobile={true}
            agreeTerms={agreeTerms}
            setAgreeTerms={setAgreeTerms}
          />

          <div className="other-login-options">
            <div className="other-login-options-text">或</div>
            <div className="other-login-methods">
              <div
                className="login-method"
                onClick={() => handleLoginMethodClick("qq")}
              >
                <div className="login-method-icon-mobile qq"></div>
              </div>
              <div
                className="login-method"
                onClick={() => handleLoginMethodClick("wechat")}
              >
                <div className="login-method-icon-mobile wechat"></div>
              </div>
              <div
                className="login-method"
                onClick={() => handleLoginMethodClick("company")}
              >
                <div className="login-method-icon-mobile company"></div>
              </div>
              <div
                className="login-method"
                onClick={() => handleLoginMethodClick("apple")}
              >
                <div className="login-method-icon-mobile apple"></div>
              </div>
              <div
                className="login-method"
                onClick={() => handleLoginMethodClick("more")}
              >
                <div className="login-method-icon-mobile icon-more"></div>
              </div>
            </div>
          </div>
        </div>

        <div className="checkbox-item-mobile">
          <input
            type="checkbox"
            id="agree-terms"
            className="checkbox-mobile"
            checked={agreeTerms}
            onChange={(e) => setAgreeTerms(e.target.checked)}
          />
          <label htmlFor="agree-terms" className="checkbox-label-moblie">
            已阅读并同意
            <a href="#"> 隐私政策</a> 和<a href="#"> 在线服务协议</a>
          </label>
        </div>
      </div>

      <ConsentModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onAgree={() => {
          setAgreeTerms(true);
          setShowModal(false);

          // 根据上次点击的方法跳转到对应页面
          const lastMethod = sessionStorage.getItem("lastClickedMethod");
          if (lastMethod === "more") {
            setStep("more-login");
          }

          // 清除记录
          sessionStorage.removeItem("lastClickedMethod");
        }}
        onCancel={() => setShowModal(false)}
      />
    </div>
  );
};

export default MobilePage;
