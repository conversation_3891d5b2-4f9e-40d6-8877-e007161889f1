import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import PhoneLogin from "../PhoneLogin";
import type { User } from "@/types/user";

// 模拟依赖组件
vi.mock("@/components/common/forms/LoginForm", () => ({
  default: ({ onBack, onLoginSuccess }) => (
    <div data-testid="mock-login-form">
      <button onClick={onBack}>返回</button>
      <button
        onClick={() => {
          const mockUsers: User[] = [
            {
              userid: 1,
              nickname: "测试用户1",
              company_id: 0,
              avatar_url: "avatar1.png",
              is_login: false,
              status: 1,
            },
            {
              userid: 2,
              nickname: "测试用户2",
              company_id: 123,
              company_name: "测试公司",
              company_logo: "company_logo.png",
              avatar_url: "avatar2.png",
              is_login: true,
              status: 1,
            },
          ];
          onLoginSuccess(mockUsers, [2]);
        }}
      >
        模拟登录成功
      </button>
    </div>
  ),
}));

vi.mock("@/components/common/account/AccountSelect", () => ({
  default: ({ users, initialSelectedIds, onBack, onConfirm }) => (
    <div data-testid="mock-account-select">
      <span>账户数量: {users.length}</span>
      <span>已选账户: {initialSelectedIds.join(",")}</span>
      <button onClick={onBack}>返回登录表单</button>
      <button onClick={() => onConfirm([...initialSelectedIds, 1])}>
        确认登录
      </button>
    </div>
  ),
}));

describe("手机号登录组件 (PhoneLogin)", () => {
  // 模拟回调函数
  const mockOnBack = vi.fn();

  // 每个测试前重置模拟函数
  beforeEach(() => {
    mockOnBack.mockReset();
    vi.spyOn(window, "alert").mockImplementation(() => {});
  });

  it("初始时应该渲染登录表单", () => {
    render(<PhoneLogin onBack={mockOnBack} />);

    expect(screen.getByTestId("mock-login-form")).toBeInTheDocument();
    expect(screen.queryByTestId("mock-account-select")).not.toBeInTheDocument();
  });

  it("点击登录表单的返回按钮时应调用 onBack 回调", () => {
    render(<PhoneLogin onBack={mockOnBack} />);

    const backButton = screen.getByText("返回");
    fireEvent.click(backButton);

    expect(mockOnBack).toHaveBeenCalledTimes(1);
  });

  it("登录成功后应显示账户选择界面", async () => {
    render(<PhoneLogin onBack={mockOnBack} />);

    // 点击模拟登录成功按钮
    const loginButton = screen.getByText("模拟登录成功");
    fireEvent.click(loginButton);

    // 验证是否切换到账户选择界面
    await waitFor(() => {
      expect(screen.getByTestId("mock-account-select")).toBeInTheDocument();
    });

    // 验证账户信息是否正确传递
    expect(screen.getByText("账户数量: 2")).toBeInTheDocument();
    expect(screen.getByText("已选账户: 2")).toBeInTheDocument();
  });

  it("在账户选择界面点击返回按钮应返回登录表单", async () => {
    render(<PhoneLogin onBack={mockOnBack} />);

    // 先切换到账户选择界面
    fireEvent.click(screen.getByText("模拟登录成功"));

    await waitFor(() => {
      expect(screen.getByTestId("mock-account-select")).toBeInTheDocument();
    });

    // 点击返回按钮
    fireEvent.click(screen.getByText("返回登录表单"));

    // 应该返回登录表单
    await waitFor(() => {
      expect(screen.getByTestId("mock-login-form")).toBeInTheDocument();
      expect(
        screen.queryByTestId("mock-account-select")
      ).not.toBeInTheDocument();
    });
  });

  it("在账户选择界面确认登录后应调用正确的处理函数", async () => {
    render(<PhoneLogin onBack={mockOnBack} />);

    // 先切换到账户选择界面
    fireEvent.click(screen.getByText("模拟登录成功"));

    await waitFor(() => {
      expect(screen.getByTestId("mock-account-select")).toBeInTheDocument();
    });

    // 点击确认登录按钮
    fireEvent.click(screen.getByText("确认登录"));

    // 应该调用alert函数，显示登录成功信息
    expect(window.alert).toHaveBeenCalledWith("2,1 登录成功");
  });
});
