import MyArray from '../src/myArray';
import { describe, it, expect } from '@jest/globals';
describe('MyArray', () => {
    it('should initialize with given elements', () => {
        const arr = new MyArray(1, 2, 3);
        expect(arr.length).toBe(3);
        expect(arr.toString()).toBe('1,2,3');
    });

    it('should push elements and return new length', () => {
        const arr = new MyArray();
        expect(arr.push(10)).toBe(1);
        expect(arr.push(20)).toBe(2);
        expect(arr.toString()).toBe('10,20');
    });

    it('should pop elements and return the last element', () => {
        const arr = new MyArray(1, 2, 3);
        expect(arr.pop()).toBe(3);
        expect(arr.length).toBe(2);
        expect(arr.pop()).toBe(2);
        expect(arr.pop()).toBe(1);
        expect(arr.pop()).toBeUndefined();
    });

    it('should iterate with forEach', () => {
        const arr = new MyArray('a', 'b', 'c');
        const result: string[] = [];
        arr.forEach((item, idx) => {
            result.push(`${idx}:${item}`);
        });
        expect(result).toEqual(['0:a', '1:b', '2:c']);
    });

    it('should map elements to a new MyArray', () => {
        const arr = new MyArray(1, 2, 3);
        const mapped = arr.map(x => x * 2);
        expect(mapped).toBeInstanceOf(MyArray);
        expect(mapped.length).toBe(3);
        expect(mapped.toString()).toBe('2,4,6');
    });

    it('should filter elements to a new MyArray', () => {
        const arr = new MyArray(1, 2, 3, 4);
        const filtered = arr.filter(x => x % 2 === 0);
        expect(filtered).toBeInstanceOf(MyArray);
        expect(filtered.length).toBe(2);
        expect(filtered.toString()).toBe('2,4');
    });

    it('should return correct length', () => {
        const arr = new MyArray();
        expect(arr.length).toBe(0);
        arr.push(1);
        expect(arr.length).toBe(1);
        arr.pop();
        expect(arr.length).toBe(0);
    });

    it('should convert to string correctly', () => {
        const arr = new MyArray('x', 'y', 'z');
        expect(arr.toString()).toBe('x,y,z');
    });
});