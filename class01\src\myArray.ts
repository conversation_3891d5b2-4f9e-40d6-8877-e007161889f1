class MyArray {
    private items: any[];
    constructor(...elements: any[]) {
        this.items = [...elements];
    }

    push(element: any) {
        this.items[this.items.length] = element;
        return this.items.length;
    }

    pop() {
        if (this.items.length === 0) return undefined;
        const last = this.items[this.items.length - 1];
        this.items.length = this.items.length - 1;
        return last;
    }

    forEach(callback: (arg0: any, arg1: number, arg2: any[]) => void) {
        for (let i = 0; i < this.items.length; i++) {
            callback(this.items[i], i, this.items);
        }
    }

    map(callback: (arg0: any, arg1: number, arg2: any[]) => any) {
        const result = new MyArray();
        for (let i = 0; i < this.items.length; i++) {
            result.push(callback(this.items[i], i, this.items));
        }
        return result;
    }

    filter(callback: (arg0: any, arg1: number, arg2: any[]) => boolean) {
        const result = new MyArray();
        for (let i = 0; i < this.items.length; i++) {
            if (callback(this.items[i], i, this.items)) {
                result.push(this.items[i]);
            }
        }
        return result;
    }

    get length() {
        return this.items.length;
    }

    toString() {
        return this.items.toString();
    }
}

export default MyArray;