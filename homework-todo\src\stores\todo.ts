import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export interface Todo {
  id: string
  text: string
  completed: boolean
  dueDate?: string
  time?: string
}

const STORAGE_KEY = 'vue-todo-app'

// Helper functions for localStorage
const loadTodos = (): Todo[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error('Error loading todos from localStorage:', error)
    return []
  }
}

const saveTodos = (todos: Todo[]) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(todos))
  } catch (error) {
    console.error('Error saving todos to localStorage:', error)
  }
}

const generateId = (): string => {
  return Math.random().toString(36).substring(2, 10) + Date.now().toString(36)
}

export const useTodoStore = defineStore('todo', () => {
  // State
  const todos = ref<Todo[]>(loadTodos())
  const showCompleted = ref(false)

  // Computed
  const activeTodos = computed(() => todos.value.filter((todo) => !todo.completed))

  const completedTodos = computed(() => todos.value.filter((todo) => todo.completed))

  const visibleTodos = computed(() => (showCompleted.value ? todos.value : activeTodos.value))

  const todoCount = computed(() => todos.value.length)
  const activeCount = computed(() => activeTodos.value.length)
  const completedCount = computed(() => completedTodos.value.length)

  // Actions
  const addTodo = (todo: Omit<Todo, 'id' | 'completed'>) => {
    const newTodo: Todo = {
      id: generateId(),
      text: todo.text,
      dueDate: todo.dueDate,
      time: todo.time,
      completed: false,
    }
    todos.value.push(newTodo)
    saveTodos(todos.value)
  }

  const deleteTodo = (id: string) => {
    const index = todos.value.findIndex((todo) => todo.id === id)
    if (index > -1) {
      todos.value.splice(index, 1)
      saveTodos(todos.value)
    }
  }

  const toggleTodo = (id: string) => {
    const todo = todos.value.find((t) => t.id === id)
    if (todo) {
      todo.completed = !todo.completed
      saveTodos(todos.value)
    }
  }

  const updateTodo = (id: string, updates: Partial<Omit<Todo, 'id'>>) => {
    const todo = todos.value.find((t) => t.id === id)
    if (todo) {
      Object.assign(todo, updates)
      saveTodos(todos.value)
    }
  }

  const toggleShowCompleted = () => {
    showCompleted.value = !showCompleted.value
  }

  const clearCompleted = () => {
    todos.value = todos.value.filter((todo) => !todo.completed)
    saveTodos(todos.value)
  }

  const clearAll = () => {
    todos.value = []
    saveTodos(todos.value)
  }

  // Initialize store with saved data
  const init = () => {
    todos.value = loadTodos()
  }

  return {
    // State
    todos,
    showCompleted,

    // Computed
    activeTodos,
    completedTodos,
    visibleTodos,
    todoCount,
    activeCount,
    completedCount,

    // Actions
    addTodo,
    deleteTodo,
    toggleTodo,
    updateTodo,
    toggleShowCompleted,
    clearCompleted,
    clearAll,
    init,
  }
})
