import { useState, useEffect } from "react";
import instance from "@/utils/request";
import "./LoginForm.css";
import ConsentModal from "../consent/ConsentModal";
import type {
  LoginStep,
  CaptchaState,
  LoginFormProps,
  LoginResponse,
  UserListResponse,
} from "@/types/user";

const LoginForm: React.FC<LoginFormProps> = ({
  onLoginSuccess,
  onBack,
  showAutoLogin = true,
  showBackButton = true,
  title = "短信验证码登录",
  subtitle = "使用金山办公在线服务账号登录",
  isMobile = false,
  agreeTerms = false,
  setAgreeTerms,
}) => {
  const [step, setStep] = useState<LoginStep>("phone");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [countryCode, setCountryCode] = useState("+86");
  const [captchaState, setCaptchaState] = useState<CaptchaState>("idle");
  const [verificationCode, setVerificationCode] = useState("");
  const [countdown, setCountdown] = useState(0);
  const [errorMessage, setErrorMessage] = useState("");
  const [autoLogin, setAutoLogin] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [pendingLogin, setPendingLogin] = useState(false);

  // 处理倒计时逻辑
  useEffect(() => {
    let timer: any = null;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [countdown]);

  // 输入框变化时清除错误信息
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPhoneNumber(e.target.value);
    setErrorMessage("");
  };

  const handleVerificationCodeChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setVerificationCode(e.target.value);
    setErrorMessage("");
  };

  const handleCaptchaClick = () => {
    // 如果是移动端且没有同意隐私协议，显示弹窗
    if (isMobile && !agreeTerms) {
      setShowModal(true);
      return;
    }

    if (captchaState === "idle" && phoneNumber.length > 0) {
      setCaptchaState("loading");
      setTimeout(() => {
        setCaptchaState("completed");
        setStep("verification");
      }, 2000);
    }
  };

  const handleSendCode = () => {
    // 如果是移动端且没有同意隐私协议，显示弹窗
    if (isMobile && !agreeTerms) {
      setShowModal(true);
      return;
    }

    // 确保只有在倒计时结束后才能再次发送
    if (countdown === 0) {
      // 模拟发送验证码
      // console.log("发送验证码到:", countryCode + phoneNumber);
      // 开始倒计时
      setCountdown(60);
    }
  };

  const handleLogin = async () => {
    // 如果是移动端且没有同意隐私协议，显示弹窗
    if (isMobile && !agreeTerms) {
      setPendingLogin(true);
      setShowModal(true);
      return;
    }

    if (verificationCode.length > 0) {
      try {
        // 清除之前的错误信息
        setErrorMessage("");

        // 使用封装的post方法
        const params = {
          phone: countryCode + phoneNumber,
          smscode: verificationCode,
        };

        const verifyResponse = await instance.post<LoginResponse>(
          "/api/v3/sms/verify",
          params
        );

        if (verifyResponse.result === "ok" && verifyResponse.ssid) {
          const usersParams = {
            ssid: verifyResponse.ssid,
            filter_rule: "normal",
            check_rule: "second_phone",
            _: phoneNumber,
          };

          // usersResponse的类型现在是UserListResponse
          const usersResponse = await instance.get<UserListResponse>(
            "/api/v3/login/users",
            {
              params: usersParams,
            }
          );

          // 直接访问.users
          if (usersResponse.users && onLoginSuccess) {
            const defaultSelectedIds = usersResponse.users
              .filter((user) => user.default_select)
              .map((user) => user.userid);
            onLoginSuccess(usersResponse.users, defaultSelectedIds);
          }
        } else {
          // 根据不同的错误类型显示不同的错误信息
          switch (verifyResponse?.result) {
            case "InvalidSMSCode":
              setErrorMessage("验证码不正确，请重新输入");
              break;
            case "InvalidPhone":
              setErrorMessage("手机号格式不正确");
              break;
            default:
              setErrorMessage(verifyResponse?.msg || "验证失败，请稍后再试");
              break;
          }
        }
      } catch (error: unknown) {
        // 处理错误响应
        if (error && typeof error === "object" && "response" in error) {
          const errorObj = error as { response?: { data?: { msg?: string } } };
          setErrorMessage(
            errorObj.response?.data?.msg || "验证失败，请稍后再试"
          );
        } else {
          setErrorMessage("验证失败，请稍后再试");
        }
      }
    }
  };

  // 处理隐私协议同意
  const handleAgreePrivacy = () => {
    if (setAgreeTerms) {
      setAgreeTerms(true);
    }
    setShowModal(false);

    // 如果有待处理的登录请求，继续执行
    if (pendingLogin) {
      setPendingLogin(false);
      // 延迟执行，确保状态已更新
      setTimeout(handleLogin, 0);
    } else if (captchaState === "idle" && phoneNumber.length > 0) {
      // 如果是点击验证按钮触发的
      setCaptchaState("loading");
      setTimeout(() => {
        setCaptchaState("completed");
        setStep("verification");
      }, 2000);
    } else if (countdown === 0 && step === "verification") {
      // 如果是发送验证码触发的
      // console.log("发送验证码到:", countryCode + phoneNumber);
      setCountdown(60);
    }
  };

  return (
    <div className={`login-form-container ${isMobile ? "mobile" : ""}`}>
      {showBackButton && step === "phone" && (
        <div className="login-form-header">
          <button className="back-button" onClick={onBack}>
            <span style={{ lineHeight: "1" }}>返回</span>
          </button>
          <h2 className="login-form-title">{title}</h2>
        </div>
      )}

      <div className="login-form-content">
        {subtitle && <p className="login-form-subtitle">{subtitle}</p>}

        <div className="form-fields">
          <div className="phone-input-group">
            <select
              className="country-select"
              value={countryCode}
              onChange={(e) => setCountryCode(e.target.value)}
            >
              <option value="+86">+86</option>
              <option value="+1">+1</option>
              <option value="+44">+44</option>
              <option value="+81">+81</option>
            </select>
            <input
              type="tel"
              placeholder="手机号码"
              className="phone-input"
              value={phoneNumber}
              onChange={handlePhoneChange}
            />
          </div>

          {step === "phone" && (
            <button
              className={`captcha-button ${captchaState}`}
              onClick={handleCaptchaClick}
              disabled={captchaState === "loading" || phoneNumber.length === 0}
            >
              <div className="captcha-icon">
                <span>🛡️</span>
              </div>
              <span className="captcha-text">
                {captchaState === "loading"
                  ? "智能检测中..."
                  : "点击按钮开始智能验证"}
              </span>
            </button>
          )}

          {step === "verification" && (
            <div className="verification-input-group">
              <input
                type="text"
                placeholder="短信验证码"
                className="verification-input"
                value={verificationCode}
                onChange={handleVerificationCodeChange}
              />
              <button
                className="send-code-btn"
                onClick={handleSendCode}
                disabled={countdown > 0}
              >
                {countdown > 0 ? `${countdown}秒后重发` : "发送验证码"}
              </button>
            </div>
          )}

          {/* 错误信息显示 */}
          {errorMessage && <div className="error-message">{errorMessage}</div>}

          {/* 自动登录选项 */}
          {showAutoLogin && (
            <div className="auto-login-container">
              <label className="auto-login-label">
                <input
                  type="checkbox"
                  checked={autoLogin}
                  onChange={() => setAutoLogin(!autoLogin)}
                  className="auto-login-checkbox"
                />
                <span className="auto-login-text">自动登录</span>
              </label>
            </div>
          )}

          <button
            className={`submit-button ${
              verificationCode.length > 0 || step === "phone" ? "" : "disabled"
            }`}
            onClick={handleLogin}
            disabled={step === "verification" && verificationCode.length === 0}
          >
            立即登录/注册
          </button>

          <p className="footer-text">如手机未绑定账号将注册账号并登录</p>
        </div>
      </div>

      {/* 隐私协议同意弹窗 */}
      <ConsentModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onAgree={handleAgreePrivacy}
        onCancel={() => {
          setShowModal(false);
          setPendingLogin(false);
        }}
      />
    </div>
  );
};

export default LoginForm;
