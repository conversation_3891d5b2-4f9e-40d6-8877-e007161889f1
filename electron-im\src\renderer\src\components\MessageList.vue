<!-- 消息列表组件 -->
<template>
  <div class="flex-1 overflow-y-auto p-4" ref="messagesArea">
    <div
      v-if="!messages.length"
      class="flex items-center justify-center h-full text-gray-500 text-sm"
    >
      {{ currentContact ? '暂无消息' : '请选择一个聊天开始对话' }}
    </div>
    <div v-else>
      <div v-for="message in messages" :key="message.id" class="mb-4">
        <!-- 判断是否为当前用户发送的消息 -->
        <div v-if="isCurrentUserMessage(message)" class="flex items-start gap-3 justify-end">
          <!-- 当前用户消息：内容在左，头像在右 -->
          <div class="flex-1 flex flex-col items-end">
            <div class="flex items-center gap-2 mb-1">
              <span class="text-xs text-gray-500">
                {{ formatMessageTime(message.timestamp) }}
              </span>
              <span class="font-medium text-sm text-[#5f5f5f]">{{ message.senderName }}</span>
            </div>
            <div class="bg-blue-500 text-white rounded-lg p-3 max-w-lg shadow-sm">
              <div class="text-sm leading-relaxed" v-html="message.content"></div>
            </div>
          </div>
          <UserAvatar :name="message.senderName" size="medium" />
        </div>

        <!-- 其他用户消息：头像在左，内容在右 -->
        <div v-else class="flex items-start gap-3">
          <UserAvatar :name="message.senderName" size="medium" />
          <div class="flex-1">
            <div class="flex items-center gap-2 mb-1">
              <span class="font-medium text-sm text-[#5f5f5f]">{{ message.senderName }}</span>
              <span class="text-xs text-gray-500">
                {{ formatMessageTime(message.timestamp) }}
              </span>
            </div>
            <div class="bg-white border border-gray-200 rounded-lg p-3 max-w-lg shadow-sm">
              <div class="text-sm text-gray-900 leading-relaxed" v-html="message.content"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import UserAvatar from './UserAvatar.vue'
import { useUserStore } from '../store/user'
import type { User } from '../api'

interface Message {
  id: string
  senderId: string
  senderName: string
  content: string
  timestamp: Date
}

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
}

interface Props {
  messages: Message[]
  currentContact: Contact | null
}

const props = defineProps<Props>()

// 用户状态管理
const userStore = useUserStore()

const messagesArea = ref<HTMLElement>()

// 判断是否为当前用户发送的消息
const isCurrentUserMessage = (message: Message) => {
  return message.senderId === 'current' || message.senderId === userStore.currentUser.value?.id
}

// 滚动到底部
const scrollToBottom = () => {
  if (messagesArea.value) {
    messagesArea.value.scrollTop = messagesArea.value.scrollHeight
  }
}

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  () => {
    nextTick(() => {
      scrollToBottom()
    })
  },
  { deep: true }
)

// 时间格式化函数
const formatMessageTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 暴露滚动方法给父组件
defineExpose({
  scrollToBottom
})
</script>
