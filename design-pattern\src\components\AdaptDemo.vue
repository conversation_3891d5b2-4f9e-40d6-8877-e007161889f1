<template>
  <div class="adapter-demo">
    <h2>适配器模式演示 - 支付系统升级</h2>

    <div class="payment-section">
      <h3>支付操作</h3>
      <input type="number" v-model.number="paymentAmount" placeholder="输入金额" min="1" />
      <button @click="handlePayment" :disabled="isProcessing">
        {{ isProcessing ? '处理中...' : '支付' }}
      </button>

      <div v-if="paymentResult" class="result" :class="{ success: paymentResult.success }">
        <p v-if="paymentResult.success">支付成功! 交易ID: {{ paymentResult.transactionId }}</p>
        <p v-else>支付失败，请重试</p>
      </div>
    </div>

    <div class="refund-section" v-if="paymentResult?.success">
      <h3>退款操作</h3>
      <button @click="handleRefund" :disabled="isProcessing">
        {{ isProcessing ? '处理中...' : '申请退款' }}
      </button>

      <div v-if="refundResult !== null" class="result" :class="{ success: refundResult }">
        <p v-if="refundResult">退款成功!</p>
        <p v-else>退款失败，请联系客服</p>
      </div>
    </div>

    <div class="system-info">
      <h3>系统信息</h3>
      <p>当前使用: {{ usingLegacy ? '旧支付系统' : '新支付系统' }}</p>
      <button @click="toggleSystem">切换到 {{ usingLegacy ? '新系统' : '旧系统' }}</button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { PaymentAdapter } from '../utils/adapter/PaymentAdapter'
import type { ModernPaymentSystem } from '../utils/adapter/types'

// 模拟新支付系统
class NewPaymentSystem implements ModernPaymentSystem {
  async pay(amount: number) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: Math.random() > 0.2, // 80%成功率
          transactionId: `new_txn_${Date.now()}`,
        })
      }, 500)
    })
  }

  async refund(transactionId: string) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(Math.random() > 0.6) // 60%退款成功率
      }, 600)
    })
  }
}

export default defineComponent({
  name: 'AdapterDemo',
  setup() {
    const paymentAmount = ref<number>(100)
    const paymentResult = ref<{ success: boolean; transactionId: string } | null>(null)
    const refundResult = ref<boolean | null>(null)
    const isProcessing = ref(false)
    const usingLegacy = ref(true)

    // 使用适配器作为默认系统
    let paymentSystem: ModernPaymentSystem = new PaymentAdapter()

    const toggleSystem = () => {
      usingLegacy.value = !usingLegacy.value
      paymentSystem = usingLegacy.value ? new PaymentAdapter() : new NewPaymentSystem()
      paymentResult.value = null
      refundResult.value = null
    }

    const handlePayment = async () => {
      if (paymentAmount.value <= 0) return

      isProcessing.value = true
      paymentResult.value = null

      try {
        paymentResult.value = await paymentSystem.pay(paymentAmount.value)
      } finally {
        isProcessing.value = false
      }
    }

    const handleRefund = async () => {
      if (!paymentResult.value?.success) return

      isProcessing.value = true
      refundResult.value = null

      try {
        refundResult.value = await paymentSystem.refund(paymentResult.value.transactionId)
      } finally {
        isProcessing.value = false
      }
    }

    return {
      paymentAmount,
      paymentResult,
      refundResult,
      isProcessing,
      usingLegacy,
      handlePayment,
      handleRefund,
      toggleSystem,
    }
  },
})
</script>

<style scoped>
.adapter-demo {
  font-family: Arial, sans-serif;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.payment-section,
.refund-section,
.system-info {
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f8f8;
  border-radius: 4px;
}

h3 {
  margin-top: 0;
  color: #42b983;
}

input {
  padding: 8px 12px;
  margin-right: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 120px;
}

button {
  padding: 8px 16px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

button:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.result {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
}

.result.success {
  background: #e8f5e9;
  border: 1px solid #a5d6a7;
}

.result:not(.success) {
  background: #ffebee;
  border: 1px solid #ef9a9a;
}
</style>
