<template>
  <div class="todo-input">
    <input
      ref="inputRef"
      type="text"
      v-model="inputValue"
      @keyup.enter="handleSubmit"
      @keyup.esc="handleCancel"
      :placeholder="placeholder"
      :maxlength="maxLength"
      class="todo-input-field"
    />
    <button 
      @click="handleSubmit"
      :disabled="!isValid"
      class="todo-input-button"
    >
      {{ buttonText }}
    </button>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { validateTodoText } from '../utils/todoUtils.js'

const props = defineProps({
  placeholder: {
    type: String,
    default: 'Add a new todo...'
  },
  buttonText: {
    type: String,
    default: 'Add'
  },
  maxLength: {
    type: Number,
    default: 200
  }
})

const emit = defineEmits(['submit', 'cancel'])

const inputValue = ref('')
const inputRef = ref(null)

const isValid = computed(() => {
  return validateTodoText(inputValue.value)
})

const handleSubmit = () => {
  if (isValid.value) {
    emit('submit', inputValue.value.trim())
    inputValue.value = ''
  }
}

const handleCancel = () => {
  inputValue.value = ''
  emit('cancel')
}

// 暴露方法给父组件
defineExpose({
  focus: () => {
    nextTick(() => {
      inputRef.value?.focus()
    })
  },
  clear: () => {
    inputValue.value = ''
  }
})
</script>

<style scoped>
.todo-input {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.todo-input-field {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.todo-input-field:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.todo-input-button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.todo-input-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.todo-input-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}
</style> 