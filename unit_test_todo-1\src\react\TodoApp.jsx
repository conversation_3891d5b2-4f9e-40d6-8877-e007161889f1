import React, { useState, useMemo, useEffect, useRef } from "react";
import { useTodoStore } from "./hooks/useTodoStore.js";
import { filterTodos, sortTodos, getTodoStats } from "./utils/todoUtils.js";
import TodoInput from "./components/TodoInput.jsx";
import TodoItem from "./components/TodoItem.jsx";
import TodoStats from "./components/TodoStats.jsx";
import "./TodoApp.css";

const TodoApp = () => {
  const {
    todos,
    addTodo,
    toggleTodo,
    deleteTodo,
    clearCompleted,
    updateTodo,
    uncompletedCount,
    completedCount,
    totalCount,
  } = useTodoStore();

  const [currentFilter, setCurrentFilter] = useState("all");
  const [showDate, setShowDate] = useState(false);
  const inputRef = useRef(null);

  const filters = [
    { label: "全部", value: "all" },
    { label: "进行中", value: "active" },
    { label: "已完成", value: "completed" },
  ];

  const filteredTodos = useMemo(() => {
    const filtered = filterTodos(todos, currentFilter);
    return sortTodos(filtered, "created");
  }, [todos, currentFilter]);

  const stats = useMemo(() => {
    return getTodoStats(todos);
  }, [todos]);

  const handleAddTodo = (text) => {
    const success = addTodo(text);
    if (success) {
      // 可以添加成功提示
    }
  };

  const handleCancel = () => {
    // 处理取消操作
  };

  const handleToggleTodo = (id) => {
    toggleTodo(id);
  };

  const handleDeleteTodo = (id) => {
    const success = deleteTodo(id);
    if (success) {
      // 可以添加删除成功提示
    }
  };

  const handleEditTodo = (todo) => {
    // 实现编辑功能
    console.log("编辑待办项:", todo);
  };

  const handleClearCompleted = () => {
    const clearedCount = clearCompleted();
    if (clearedCount > 0) {
      // 可以添加清除成功提示
    }
  };

  const getEmptyStateMessage = () => {
    switch (currentFilter) {
      case "active":
        return "没有进行中的待办项";
      case "completed":
        return "没有已完成的待办项";
      default:
        return "还没有待办项，开始添加吧！";
    }
  };

  useEffect(() => {
    // 自动聚焦到输入框
    inputRef.current?.focus();
  }, []);

  return (
    <div className="todo-app">
      <h1>TODO List</h1>

      <TodoInput
        onSubmit={handleAddTodo}
        onCancel={handleCancel}
        ref={inputRef}
      />

      <div className="todo-filters">
        {filters.map((filter) => (
          <button
            key={filter.value}
            onClick={() => setCurrentFilter(filter.value)}
            className={`filter-btn ${
              currentFilter === filter.value ? "active" : ""
            }`}
          >
            {filter.label}
          </button>
        ))}
      </div>

      <div className="todo-list">
        {filteredTodos.map((todo) => (
          <TodoItem
            key={todo.id}
            todo={todo}
            showDate={showDate}
            onToggle={handleToggleTodo}
            onDelete={handleDeleteTodo}
            onEdit={handleEditTodo}
          />
        ))}
        {filteredTodos.length === 0 && (
          <div className="empty-state">{getEmptyStateMessage()}</div>
        )}
      </div>

      <TodoStats stats={stats} onClearCompleted={handleClearCompleted} />
    </div>
  );
};

export default TodoApp;
