import type { FormConfig } from "@/types/form";
import { FormMode } from "@/types/form";
import { useForm } from "@/hooks/useForm";
import { TextField, SelectField, RadioField, ImageUploadField } from "./fields";

interface ConfigurableFormProps {
  config: FormConfig;
  renderMode?: FormMode;
  onFieldClick?: (fieldId: string | null) => void;
  selectedFieldId?: string | null;
}

export function ConfigurableForm({
  config,
  renderMode,
  onFieldClick,
  selectedFieldId,
}: ConfigurableFormProps) {
  const {
    formData,
    errors,
    isSubmitting,
    schema,
    handleFieldChange,
    handleSubmit,
  } = useForm(config);

  if (!schema) {
    return <div>表单加载中...</div>;
  }

  // 根据字段类型渲染字段组件
  const renderField = (field: FormConfig["fields"][0]) => {
    const value = formData[field.id] || "";
    const error = errors[field.id];
    const onChange = (newValue: string) =>
      handleFieldChange(field.id, newValue);

    switch (field.type) {
      case "text":
        return (
          <TextField
            field={field}
            value={value}
            error={error}
            onChange={onChange}
          />
        );
      case "select":
        return (
          <SelectField
            field={field}
            value={value}
            error={error}
            onChange={onChange}
          />
        );
      case "radio":
        return (
          <RadioField
            field={field}
            value={value}
            error={error}
            onChange={onChange}
          />
        );
      case "imageUpload":
        return (
          <ImageUploadField
            field={field}
            value={value}
            error={error}
            onChange={onChange}
          />
        );
      default:
        return null;
    }
  };

  // 获取字段容器样式
  const getFieldContainerClass = (field: FormConfig["fields"][0]) => {
    const baseClass = "space-y-2";

    switch (renderMode) {
      case FormMode.BUILDER: {
        const isSelected = selectedFieldId === field.id;
        return `${baseClass} p-3 rounded-md border-2 cursor-pointer transition-colors ${
          isSelected
            ? "border-blue-500 bg-blue-50"
            : "border-gray-200 hover:border-gray-300"
        }`;
      }
      case FormMode.PREVIEW:
        return `${baseClass} p-2 rounded border border-gray-100`;
      case FormMode.READONLY:
        return `${baseClass} opacity-75`;
      default:
        return baseClass;
    }
  };

  // 获取标签样式
  const getLabelClass = () => {
    switch (renderMode) {
      case FormMode.PREVIEW:
        return "text-sm font-medium text-gray-600 mb-1 block";
      case FormMode.READONLY:
        return "text-base font-medium text-gray-500 mb-1 block";
      default:
        return "text-base font-medium text-gray-700 mb-2 block";
    }
  };

  // 获取表单容器样式
  const getFormContainerClass = () => {
    const baseClass =
      "flex min-h-screen items-center justify-center py-10 px-4 sm:px-6 lg:px-8";

    switch (renderMode) {
      case FormMode.PREVIEW:
        return "max-w-sm mx-auto p-4 bg-white border rounded-lg shadow-sm scale-90";
      case FormMode.READONLY:
        return `${baseClass} bg-gray-100`;
      default:
        return `${baseClass} bg-gray-50`;
    }
  };

  // 获取表单内容容器样式
  const getFormContentClass = () => {
    switch (renderMode) {
      case FormMode.PREVIEW:
        return "";
      case FormMode.READONLY:
        return "w-full max-w-md space-y-6 rounded-lg bg-white p-8 shadow border";
      default:
        return "w-full max-w-md space-y-8 rounded-lg bg-white p-8 shadow-lg";
    }
  };

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleSubmit(e);
  };

  const handleFieldClick = (field: FormConfig["fields"][0]) => {
    if (renderMode === FormMode.BUILDER && onFieldClick) {
      onFieldClick(field.id);
    }
  };

  const handleFormClick = () => {
    if (renderMode === FormMode.BUILDER && onFieldClick) {
      onFieldClick(null);
    }
  };

  return (
    <div className={getFormContainerClass()} onClick={handleFormClick}>
      <div className={getFormContentClass()}>
        <div>
          <h2 className="mt-4 text-center text-2xl font-bold tracking-tight text-gray-900">
            {config.title}
          </h2>
        </div>

        <form onSubmit={onSubmit} className="mt-4 space-y-4">
          {config.fields.map((field) => (
            <div
              key={field.id}
              className={getFieldContainerClass(field)}
              onClick={(e) => {
                e.stopPropagation();
                handleFieldClick(field);
              }}
            >
              <label className={getLabelClass()}>
                {field.label}
                {field.required && (
                  <span
                    className={
                      renderMode === FormMode.PREVIEW
                        ? "text-red-400 text-xs"
                        : "text-red-500"
                    }
                  >
                    *
                  </span>
                )}
              </label>
              {renderField(field)}
            </div>
          ))}

          {renderMode === FormMode.NORMAL && (
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isSubmitting ? "提交中..." : config.submitButtonText}
            </button>
          )}
        </form>
      </div>
    </div>
  );
}
