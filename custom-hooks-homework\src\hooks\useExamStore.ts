import { useState, useEffect } from "react";
import examData from "../exam.json";

export interface ExamQuestion {
  question: string;
  options: string[];
  answer: string;
}

// 定义考试状态类型
interface ExamState {
  questions: ExamQuestion[];
  userAnswers: Record<number, string>;
  submitted: boolean;
  score: number;
}

/**
 * 自定义钩子，用于管理考试状态并实现持久化
 */
const useExamStore = () => {
  // 初始状态
  const initialState: ExamState = {
    questions: examData as ExamQuestion[],
    userAnswers: {},
    submitted: false,
    score: 0,
  };

  // 从localStorage中获取状态，如果不存在则使用初始状态
  const getStoredState = (): ExamState => {
    try {
      const storedState = localStorage.getItem("examState");
      return storedState ? JSON.parse(storedState) : initialState;
    } catch (error) {
      console.error("Error loading exam state from localStorage:", error);
      return initialState;
    }
  };

  const [examState, setExamState] = useState<ExamState>(() => getStoredState());

  // 当状态变化时，保存到localStorage
  useEffect(() => {
    try {
      localStorage.setItem("examState", JSON.stringify(examState));
    } catch (error) {
      console.error("Error saving exam state to localStorage:", error);
    }
  }, [examState]);

  /**
   * 选择答案
   * @param questionIndex 问题索引
   * @param option 选择的选项
   */
  const selectAnswer = (questionIndex: number, option: string) => {
    setExamState((prevState) => ({
      ...prevState,
      userAnswers: {
        ...prevState.userAnswers,
        [questionIndex]: option,
      },
    }));
  };

  /**
   * 提交考试
   */
  const submitExam = () => {
    let correctCount = 0;

    // 计算得分
    examState.questions.forEach((question, index) => {
      if (examState.userAnswers[index] === question.answer) {
        correctCount++;
      }
    });

    setExamState((prevState) => ({
      ...prevState,
      submitted: true,
      score: correctCount,
    }));
  };

  /**
   * 重置考试
   */
  const resetExam = () => {
    localStorage.removeItem("examState");
    setExamState(initialState);
  };

  return {
    examState,
    selectAnswer,
    submitExam,
    resetExam,
  };
};

export default useExamStore;
