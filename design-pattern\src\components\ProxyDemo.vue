<!-- src/components/ProxyDemo.vue -->
<template>
  <div class="proxy-demo">
    <h2>代理模式演示 - 图片加载系统</h2>

    <div class="image-gallery">
      <div
        v-for="image in images"
        :key="image.filename"
        class="image-item"
        @click="selectImage(image)"
      >
        <div class="image-container">
          <div v-if="image.loading" class="loading-spinner"></div>
          <img
            v-else
            :src="image.thumbnail"
            :alt="image.filename"
            :class="{ selected: selectedImage === image }"
          />
          <p>{{ image.filename }}</p>
        </div>
      </div>
    </div>

    <div class="image-preview">
      <h3>图片预览</h3>
      <div v-if="selectedImage" class="preview-content">
        <div v-if="selectedImage.loading" class="loading-spinner large"></div>
        <img
          v-else
          :src="selectedImage.proxy.getImageData()"
          :alt="selectedImage.filename"
          class="full-image"
        />
        <div class="image-info">
          <p><strong>文件名:</strong> {{ selectedImage.filename }}</p>
          <p><strong>状态:</strong> {{ selectedImage.loading ? '加载中...' : '已加载' }}</p>
          <button @click="loadImage(selectedImage)">
            {{ selectedImage.loading ? '正在加载...' : '加载高清图' }}
          </button>
        </div>
      </div>
      <div v-else class="placeholder">请从左侧选择图片</div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { ImageProxy } from '@/utils/proxy/ImageProxy'

interface GalleryImage {
  filename: string
  thumbnail: string
  proxy: ImageProxy
  loading: boolean
}

export default defineComponent({
  name: 'ProxyDemo',
  setup() {
    const images = ref<GalleryImage[]>([
      {
        filename: 'nature.jpg',
        thumbnail: '../image/nature.jpg',
        proxy: new ImageProxy('nature.jpg'),
        loading: false,
      },
      {
        filename: 'cityscape.jpg',
        thumbnail: '../image/cityscape.jpg',
        proxy: new ImageProxy('cityscape.jpg'),
        loading: false,
      },
    ])

    const selectedImage = ref<GalleryImage | null>(null)

    const selectImage = (image: GalleryImage) => {
      selectedImage.value = image
    }

    const loadImage = async (image: GalleryImage) => {
      if (!image.loading) {
        image.loading = true
        try {
          await image.proxy.display()
          console.log(`加载完成: ${image.filename}`)
        } finally {
          image.loading = false
        }
      }
    }

    return {
      images,
      selectedImage,
      selectImage,
      loadImage,
    }
  },
})
</script>

<style scoped>
.proxy-demo {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
  font-family: Arial, sans-serif;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
}

.image-item {
  cursor: pointer;
  transition: all 0.3s;
}

.image-item:hover {
  transform: scale(1.05);
}

.image-container {
  position: relative;
  border: 1px solid #eee;
  padding: 10px;
  border-radius: 4px;
}

.image-container img {
  width: 100%;
  height: auto;
  border-radius: 3px;
}

.image-container .selected {
  border: 2px solid #42b983;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #42b983;
  animation: spin 1s ease-in-out infinite;
}

.loading-spinner.large {
  width: 80px;
  height: 80px;
  margin: 50px auto;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.image-preview {
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.full-image {
  max-width: 100%;
  max-height: 400px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.image-info {
  width: 100%;
  padding: 15px;
  background: #f8f8f8;
  border-radius: 4px;
}

.image-info p {
  margin: 8px 0;
}

button {
  padding: 8px 15px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

button:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.placeholder {
  color: #999;
  text-align: center;
  padding: 50px 0;
}
</style>
