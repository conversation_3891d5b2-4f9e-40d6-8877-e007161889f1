# 在线考试应用

## 项目结构

```
custom-hooks-homework/
├── src/
│   ├── components/
│   │   ├── ExamContainer.tsx  # 考试主容器组件
│   │   └── Question.tsx       # 单个问题组件
│   ├── hooks/
│   │   └── useExamStore.ts    # 状态管理自定义Hook
│   ├── assets/
│   ├── App.tsx                # 应用主入口
│   ├── main.tsx
│   └── exam.json              # 考试数据
└── ...
```

## 核心逻辑 - `useExamStore.ts`

这是项目中最核心的部分，一个自定义 Hook，封装了所有与考试状态相关的功能。

-   **`useEffect`**: 监听 `examState` 的变化，当状态更新时，自动将其同步到 `localStorage`，实现持久化。
-   **`selectAnswer(questionIndex, option)`**: 用户选择答案，更新 `userAnswers` 状态。
-   **`submitExam()`**: 计算用户得分，并将考试状态标记为“已提交”。
-   **`resetExam()`**: 清除 `localStorage` 中的记录并重置所有状态，重新开始考试。

## 组件说明

-   **`ExamContainer.tsx`**:
    -   作为考试页面的主容器。
    -   调用 `useExamStore` Hook 来获取状态和操作函数。
    -   渲染题目列表、提交按钮和结果展示。
-   **`Question.tsx`**:
    -   渲染单个问题的题干和所有选项。
    -   用户选择选项时，调用从父组件传入的回调函数 `onSelectOption` (即 `selectAnswer`) 函数来更新状态。