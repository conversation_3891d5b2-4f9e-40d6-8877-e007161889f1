
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import App from './App'
import { registerMicroApps, start } from 'qiankun';
// 注册微前端应用
registerMicroApps([
  {
    name: 'react-sub',
    container: '#react-sub',  // 宿主应用中的挂载点
    entry: 'http://localhost:3011',  // 子应用地址
    activeRule: '/react-sub',  // 路由规则
  },
  {
    name: 'vue-sub',
    container: '#vue-sub',  // 宿主应用中的挂载点
    entry: 'http://localhost:3012',  // 子应用地址
    activeRule: '/vue-sub',  // 路由规则
  },
]);

// 启动微前端应用
start({
  sandbox: {
    experimentalStyleIsolation: true,  // 启用样式隔离
  },
});

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
