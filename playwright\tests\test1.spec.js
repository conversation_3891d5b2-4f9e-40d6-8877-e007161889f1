const { test, expect } = require("@playwright/test");
const path = require("path");

test("单知识库管理：新增知识库", async ({ page, context }) => {
  await context.addCookies([
    {
      name: "wps_sid",
      value: "V02SwU1fFDt59JxOhyoAkU--rKmEmMk00a2a00910066152e2f",
      domain: ".kdocs.cn",
      path: "/",
      httpOnly: true,
      secure: true,
    },
  ]);

  // 等待+导航
  await page.goto("https://365.kdocs.cn/kmwiki");
  await page
    .locator('.wiki-new-team-button-corp:has-text("新建知识库")')
    .waitFor({ timeout: 30000 });

  // 结合文本和class定位新建知识库按钮
  const newRepoButton = page.locator(
    '.wiki-new-team-button-corp:has-text("新建知识库")'
  );
  await newRepoButton.click();
  // 验证对话框是否成功打开
  const dialogHeader = page.locator(
    '.kdv-dialog__header:has-text("新建知识库")'
  );
  await expect(dialogHeader).toBeVisible();

  // 定位输入框并输入名称
  const newRepoName = "我的知识库";
  const nameInput = page.locator('input[placeholder="请输入知识库名称"]');
  await nameInput.fill(newRepoName);

  // 验证输入的值
  await expect(nameInput).toHaveValue(newRepoName);

  //点击创建
  const dialog = page.locator(".kdv-dialog.wiki-new-team-dialog-corp");
  const createBtn = dialog.getByRole("button", { name: /^创建$/ });
  await createBtn.waitFor({ state: "attached" }); // 按钮存在
  await expect(createBtn).toBeEnabled(); // 等到启用
  await createBtn.click();

  // 监听新标签页
  const newPagePromise = context.waitForEvent("page");
  const newPage = await newPagePromise;
  await newPage.waitForLoadState("domcontentloaded");
  console.log("新标签页URL:", newPage.url());
  // 关闭新标签页
  await newPage.close();
});

test("知识库列表交互：筛选、检索", async ({ page, context }) => {
  await context.addCookies([
    {
      name: "wps_sid",
      value: "V02SwU1fFDt59JxOhyoAkU--rKmEmMk00a2a00910066152e2f",
      domain: ".kdocs.cn",
      path: "/",
      httpOnly: true,
      secure: true,
    },
  ]);

  // 智能等待+导航
  await page.goto("https://365.kdocs.cn/kmwiki");
  await page
    .locator('.wiki-new-team-button-corp:has-text("新建知识库")')
    .waitFor({ timeout: 30000 });

  // 断言列表中包含新建的知识库
  const newRepoName = "我的知识库";
  await page.locator(".kdv-navigation-item__txt", { hasText: "全部" }).click();
  await expect(
    page.locator(".enterprise-card .title", { hasText: newRepoName }).first()
  ).toBeVisible();
  console.log("全部列表包含新建的知识库");

  await page
    .locator(".kdv-navigation-item__txt", { hasText: "我管理的" })
    .click();
  await expect(
    page.locator(".enterprise-card .title", { hasText: newRepoName }).first()
  ).toBeVisible();
  console.log("我管理的列表包含新建的知识库");
  await page
    .locator(".kdv-navigation-item__txt", { hasText: "我加入的" })
    .click();
  await expect(
    page.locator(".enterprise-card .title", { hasText: newRepoName }).first()
  ).toBeHidden();
  console.log("我加入的列表不包含新建的知识库");

  await page
    .locator(".kdv-navigation-item__txt", { hasText: "公开知识" })
    .click();
  await expect(
    page.locator(".enterprise-card .title", { hasText: newRepoName }).first()
  ).toBeHidden();
  console.log("公开知识列表不包含新建的知识库");

  await page.locator(".kdv-navigation-item__txt", { hasText: "全部" }).click();
  // 定位并点击放大镜图标（icon）
  await page
    .locator("div.search-container > button.kdv-button.search-btn")
    .click();
  // 等待搜索输入框出现
  const searchInput = page.locator(
    'input.kdv-input__inner[placeholder="搜索知识库"]'
  );
  await searchInput.waitFor({ state: "visible" });
  // 输入关键词进行搜索
  await searchInput.fill(newRepoName);
  await expect(
    page.locator(".enterprise-card .title", { hasText: newRepoName }).first()
  ).toBeVisible();
  console.log("搜索结果列表包含新建的知识库");
});

test("文件列表增删改", async ({ page, context }) => {
  await context.addCookies([
    {
      name: "wps_sid",
      value: "V02SwU1fFDt59JxOhyoAkU--rKmEmMk00a2a00910066152e2f",
      domain: ".kdocs.cn",
      path: "/",
      httpOnly: true,
      secure: true,
    },
  ]);

  // 智能等待+导航
  await page.goto("https://365.kdocs.cn/kmwiki");
  await page
    .locator('.wiki-new-team-button-corp:has-text("新建知识库")')
    .waitFor({ timeout: 30000 });

  // 断言列表中包含新建的知识库
  const newRepoName = "我的知识库";
  await page
    .locator(".kdv-navigation-item__txt", { hasText: "我管理的" })
    .click();

  const card = page
    .locator(".enterprise-card", {
      has: page.locator(".title", { hasText: newRepoName }),
    })
    .first();

  await expect(card).toBeVisible();
  await card.click();

  const newPagePromise = context.waitForEvent("page");
  const newPage = await newPagePromise;
  await newPage.waitForLoadState("domcontentloaded");
  console.log("新标签页URL:", newPage.url());

  // 新建文件popover弹窗
  await newPage.locator(".tree-menu-header-operate-item button").click();

  await newPage
    .locator('.menu-item__content:has(.text-color:has-text("文字"))')
    .click();

  // 等待 iframe 出现
  const frameElement = await newPage.waitForSelector("iframe#office-iframe");

  // 获取 iframe 对应的 Frame 对象
  const frame = await frameElement.contentFrame();
  // 等待 iframe 内body加载完
  await frame.waitForSelector("body");

  // 定位对应的 span.title，且文本或 title 为“未命名文字文稿”
  const targetNode = newPage.locator('div[draggable="true"] span.title', {
    hasText: "未命名文字文稿",
  });

  await expect(targetNode).toBeVisible();

  console.log("知识库新建文档成功");

  // 右键点击这个节点（展开右键菜单）
  await targetNode.click({ button: "right" });

  // 定位右键菜单里的“删除”按钮
  const deleteButton = newPage.locator("li.kdv-menu-item >> text=删除");

  // 等待删除按钮可点击
  await deleteButton.waitFor({ state: "visible", timeout: 50000 });
  await expect(deleteButton).toBeEnabled({ timeout: 50000 });

  // 点击删除按钮
  await deleteButton.click();
  await newPage.waitForTimeout(1000);
  // 定位删除对话框标题元素，判断它是否可见
  const dialogTitle = newPage.locator(
    ".kdv-dialog__header .kdv-dialog__title",
    {
      hasText: "删除文档",
    }
  );

  await dialogTitle.waitFor({ state: "visible", timeout: 30000 });
  await expect(dialogTitle).toBeVisible();

  // 点击删除
  await newPage
    .locator(
      "button.kdv-button.kdv-button--primary.kdv-button--large.is-danger",
      {
        hasText: "删除",
      }
    )
    .click();
  // 删除成功
  await newPage
    .locator("p.kdv-message__content", { hasText: "删除成功" })
    .waitFor();
  console.log("知识库删除文档成功");

  // 上传本地文件
  await newPage.locator(".tree-menu-header-operate-item button").click();
  const importMenu = newPage.locator('.menu-item__content:has-text("导入")');
  await importMenu.hover();
  await newPage.waitForTimeout(5000); // 等待菜单动画
  // 等待“上传本地文件”按钮显示
  const uploadMenu = newPage
    .locator('.menu-item__content:has-text("上传本地文件")')
    .first();
  await uploadMenu.waitFor({ state: "visible", timeout: 10000 });
  const [fileChooser] = await Promise.all([
    newPage.waitForEvent("filechooser", { timeout: 30000 }),
    uploadMenu.click(),
  ]);
  const filePath = path.resolve(__dirname, "dzfp.pdf");
  await fileChooser.setFiles(filePath);
  console.log("文件已设置到上传控件");
  await newPage.waitForTimeout(3000);
  const uploadCompleteText = newPage.locator(
    ".panel-header-status-text-header",
    {
      hasText: "全部上传完成",
    }
  );
  await uploadCompleteText.waitFor({ state: "visible", timeout: 50000 });
  console.log("知识库上传本地文档成功");

  // 关闭新标签页
  await newPage.close();
});

test("API请求拦截", async ({ page, context }) => {
  await context.addCookies([
    {
      name: "wps_sid",
      value: "V02SwU1fFDt59JxOhyoAkU--rKmEmMk00a2a00910066152e2f",
      domain: ".kdocs.cn",
      path: "/",
      httpOnly: true,
      secure: true,
    },
  ]);

  // 智能等待+导航
  await page.goto("https://365.kdocs.cn/kmwiki");
  await page
    .locator('.wiki-new-team-button-corp:has-text("新建知识库")')
    .waitFor({ timeout: 30000 });
  await page.locator(".kdv-navigation-item__txt", { hasText: "全部" }).click();
  await page.route(
    "https://365.kdocs.cn/wiki/api/km/space/list?page_size=100&classify=manager&permission_key=view_space_setting&page_token=&with_more=true",
    async (route) => {
      console.log("路由拦截触发！");
      // 先拿到原始响应
      const response = await route.fetch({
        headers: { "Cache-Control": "no-cache" }, // 强制不缓存
      });
      let body = await response.json();
      console.log("原始数据:", JSON.stringify(body, null, 2));
      // 修改返回数据
      if (body?.data?.list?.length > 0) {
        // body.data.list[0].space_name =
        //   "这是一个超级超级超级超级超级超级超级超级超级超级超级超级长的名字";
        body.data.list[0].space_name = ""; // 改为空名称
      }
      // 返回修改后的数据
      route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify(body),
      });
    }
  );
  await page
    .locator(".kdv-navigation-item__txt", { hasText: "我管理的" })
    .click();
  await expect(page.locator(".enterprise-card .title").first()).toHaveText("", {
    timeout: 10000,
  });
});

test("智能抽取的准确性", async ({ page, context }) => {
  await context.addCookies([
    {
      name: "wps_sid",
      value: "V02SwU1fFDt59JxOhyoAkU--rKmEmMk00a2a00910066152e2f",
      domain: ".kdocs.cn",
      path: "/",
      httpOnly: true,
      secure: true,
    },
  ]);

  // 智能等待+导航
  await page.goto("https://365.kdocs.cn/kmwiki");
  await page
    .locator('.wiki-new-team-button-corp:has-text("新建知识库")')
    .waitFor({ timeout: 30000 });

  // 断言列表中包含新建的知识库
  const newRepoName = "我的知识库";
  await page
    .locator(".kdv-navigation-item__txt", { hasText: "我管理的" })
    .click();

  const card = page
    .locator(".enterprise-card", {
      has: page.locator(".title", { hasText: newRepoName }),
    })
    .first();

  await expect(card).toBeVisible();
  await card.click();

  const newPagePromise = context.waitForEvent("page");
  const newPage = await newPagePromise;
  await newPage.waitForLoadState("domcontentloaded");
  console.log("新标签页URL:", newPage.url());

  // 等待 iframe 出现
  const frameElement = await newPage.waitForSelector("iframe#office-iframe");

  // 获取 iframe 对应的 Frame 对象
  const frame = await frameElement.contentFrame();
  // 等待 iframe 内body加载完
  await frame.waitForSelector("body");

  // 点击 智能抽取
  const morebutton = newPage.locator(".operate-item.kdv-popover__reference");
  await morebutton.hover();
  await newPage.waitForTimeout(500); // 等待菜单动画

  const menuItem = newPage.locator(
    '.kdv-menu-item__content:has-text("内容智能抽取")'
  );
  await menuItem.click();

  await expect(newPage.locator(".ExtractTargetNew__title-left")).toContainText(
    /已添加/,
    { timeout: 60000 }
  );

  await newPage.waitForSelector('span.iconText:has-text("已添加")', {
    timeout: 80000,
  });

  const input = newPage.locator("input.InputWithTags__field--input");
  const fields = ["名称", "纳税人识别号", "合计"];

  for (const field of fields) {
    await input.fill(field);
    await input.press("Enter");
  }

  const extractButton = newPage.locator('button:has-text("开始抽取")');

  // 等待按钮可用
  await expect(extractButton).toBeEnabled({ timeout: 10000 });

  // 点击按钮
  await extractButton.click();

  await newPage.waitForSelector(
    'p.kdv-message__content:has-text("抽取已完成")',
    {
      timeout: 120000,
    }
  );
});
