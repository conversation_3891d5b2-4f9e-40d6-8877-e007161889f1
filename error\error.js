function parseUserData(str) {
  try {
    return JSON.parse(str);
  } catch {
    return { name: "", age: 0 };
  }
}
class ValidationError extends Error {
  constructor(message, code, field) {
    super(message);
    this.name = "ValidationError";
    this.code = code;
    this.field = field;
    // 保证 instanceof 正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ValidationError);
    }
  }
}

try {
  const user = parseUserData('{"name":123}'); // 假设 age 缺失或 name 类型错误
  if (typeof user.name !== "string") {
    throw new ValidationError("用户名格式错误", "INVALID_NAME", "name");
  }
} catch (err) {
  if (err instanceof ValidationError) {
    console.error(`校验错误：${err.message} [${err.field}]`);
  } else {
    console.error("其他错误:", err);
  }
} //校验错误：用户名格式错误 [name]

async function safeFetch(url: string): Promise<{ data: any, error: any }> {
  try {
    const response = await fetch(url);

    if (!response.ok) {
      // HTTP 状态码错误（如 404、500）
      return {
        data: null,
        error: {
          message: `HTTP error: ${response.status}`,
          status: response.status,
        },
      };
    }

    const data = await response.json(); // 可能抛出解析错误
    return { data, error: null };
  } catch (err) {
    // 网络错误或 JSON 解析错误
    return {
      data: null,
      error: {
        message: err instanceof Error ? err.message : String(err),
        raw: err,
      },
    };
  }
}

const { data, error } = await safeFetch("http://invalid-host"); // 不存在的域名

console.log(error); // 输出网络错误信息

const { data, error } = await safeFetch("/not-found-endpoint");

console.log(error); // 输出 { message: 'HTTP error: 404', status: 404 }
