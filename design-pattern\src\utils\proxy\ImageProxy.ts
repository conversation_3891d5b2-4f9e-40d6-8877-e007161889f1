import type { ImageService } from './types'
import { HighResolutionImage } from './types'

export class ImageProxy implements ImageService {
  private realImage: HighResolutionImage | null = null
  private imageLoaded = false

  constructor(private filename: string) {}

  private async checkAndLoad(): Promise<void> {
    if (!this.realImage) {
      this.realImage = new HighResolutionImage(this.filename)
      await this.realImage.load() // 假设HighResolutionImage有异步load方法
      this.imageLoaded = true
    }
  }

  async display(): Promise<void> {
    if (!this.imageLoaded) {
      console.log(`显示缩略图: ${this.filename}`)
      await this.checkAndLoad()
    }
    this.realImage!.display()
  }

  async getImageData(): Promise<string> {
    await this.checkAndLoad()
    return this.realImage!.getImageData()
  }
}
