export interface FileSystemComponent {
  id: string
  name: string
  type: 'file' | 'folder'
  size?: number
  children?: FileSystemComponent[]
  parentId: string | null
  createdAt: Date
}

// 基础组件类
export abstract class Component implements FileSystemComponent {
  constructor(
    public readonly id: string,
    public name: string,
    public readonly type: 'file' | 'folder',
    public parentId: string | null = null,
  ) {}

  abstract get size(): number
  abstract display(indent?: string): string

  // 共享方法
  get path(): string {
    return this.parentId ? `${this.parent?.path}/${this.name}` : this.name
  }
}
