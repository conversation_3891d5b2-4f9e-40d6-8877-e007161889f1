.todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background-color: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.todo-item:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.todo-item.completed {
  background-color: #d4edda;
  opacity: 0.8;
}

.todo-content {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 10px;
}

.todo-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.todo-text {
  flex: 1;
  font-size: 16px;
  word-break: break-word;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  color: #6c757d;
}

.todo-date {
  font-size: 12px;
  color: #6c757d;
  white-space: nowrap;
}

.todo-actions {
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.2s;
}

.todo-item:hover .todo-actions {
  opacity: 1;
}

.todo-edit-btn,
.todo-delete-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.todo-edit-btn {
  background-color: #ffc107;
  color: #212529;
}

.todo-edit-btn:hover {
  background-color: #e0a800;
}

.todo-delete-btn {
  background-color: #dc3545;
  color: white;
}

.todo-delete-btn:hover {
  background-color: #c82333;
} 