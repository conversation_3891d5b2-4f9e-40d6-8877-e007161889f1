{"name": "homework-1", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "webpack serve --mode development", "build": "webpack  --mode production", "proxy": "w2 add --force", "lint": "eslint . --ext .ts", "lint:fix": "eslint --fix"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@eslint/js": "^9.31.0", "@types/quill": "^2.0.14", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "css-loader": "^7.1.2", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "html-webpack-plugin": "^5.6.3", "mini-css-extract-plugin": "^2.9.2", "prettier": "^3.6.2", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "webpack": "^5.100.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "dependencies": {"axios": "^1.10.0", "html-loader": "^5.1.0", "qs": "^6.14.0", "quill": "^2.0.3", "quill-mention": "^6.1.1"}}