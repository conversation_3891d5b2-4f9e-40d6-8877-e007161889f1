export const fileTypeSVGs: Record<string, string> = {
  otl: `<svg width="36" height="36" viewBox="0 0 24 24" fill="none"><path d="M0 3C0 1.34315 1.34315 0 3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3Z" fill="#9152FF"></path><path d="M0 18.375H24V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V18.375Z" fill="#7C3DEA"></path><path d="M21 1.14742e-07C22.6569 1.87166e-07 24 1.34315 24 3L24 21.0968C24 22.7002 22.7002 24 21.0968 24C19.5936 24 18.375 22.7814 18.375 21.2782L18.375 0L21 1.14742e-07Z" fill="#A470FF"></path><path d="M18.375 21.2782C18.375 22.7814 19.5936 24 21.0968 24C22.7002 24 24 22.7002 24 21.0968C24 19.5936 22.7814 18.375 21.2782 18.375H18.375V21.2782Z" fill="#9152FF"></path><path d="M4.03125 4.78125H14.3438V6.65625H4.03125V4.78125Z" fill="white"></path><path d="M4.03125 8.25H14.3438V10.125H4.03125V8.25Z" fill="white"></path><path d="M10.4062 11.7188H4.03125V13.5938H10.4062V11.7188Z" fill="white"></path></svg>`,
  wps: `<svg width="36" height="36" viewBox="0 0 24 24" fill="none"><path d="M0 3C0 1.34315 1.34315 0 3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3Z" fill="#1D70F5"></path><path d="M0 18.375H24V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V18.375Z" fill="#0059E8"></path><path d="M21 1.14742e-07C22.6569 1.87166e-07 24 1.34315 24 3L24 21.0968C24 22.7002 22.7002 24 21.0968 24C19.5936 24 18.375 22.7814 18.375 21.2782L18.375 0L21 1.14742e-07Z" fill="#3C87FF"></path><path d="M18.375 21.2782C18.375 22.7814 19.5936 24 21.0968 24C22.7002 24 24 22.7002 24 21.0968C24 19.5936 22.7814 18.375 21.2782 18.375H18.375V21.2782Z" fill="#1D70F5"></path><path d="M5.75921 13.4063L3.5625 4.59375H5.32534L6.89431 11.5926L8.35714 4.94932H9.9414L11.5599 11.6474L12.9608 4.59375H14.7188L12.7213 13.4063H10.5142L9.18988 8.06607L7.94836 13.4063H5.75921Z" fill="white"></path></svg>`,
  pdf: '<svg width="36" height="36" viewBox="0 0 24 24" fill="none"><path d="M0 3C0 1.34315 1.34315 0 3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3Z" fill="#EB2F3B"></path><path d="M0 18.375H24V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V18.375Z" fill="#CE1E29"></path><path d="M21 1.14742e-07C22.6569 1.87166e-07 24 1.34315 24 3L24 21.0968C24 22.7002 22.7002 24 21.0968 24C19.5936 24 18.375 22.7814 18.375 21.2782L18.375 0L21 1.14742e-07Z" fill="#FF4F5A"></path><path d="M18.375 21.2782C18.375 22.7814 19.5936 24 21.0968 24C22.7002 24 24 22.7002 24 21.0968C24 19.5936 22.7814 18.375 21.2782 18.375H18.375V21.2782Z" fill="#EB2F3B"></path><g clip-path="url(#clip0_913_637)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.875 3.375H11.7188C13.6345 3.375 15.1875 4.92801 15.1875 6.84375C15.1875 8.75949 13.6345 10.3125 11.7188 10.3125H9.75V15H6.46875C4.65657 15 3.1875 13.5309 3.1875 11.7188C3.1875 9.90657 4.65657 8.4375 6.46875 8.4375H7.875V3.375ZM7.875 10.3125H6.46875C5.6921 10.3125 5.0625 10.9421 5.0625 11.7188C5.0625 12.4954 5.6921 13.125 6.46875 13.125H7.875V10.3125ZM9.75 8.4375H11.7188C12.599 8.4375 13.3125 7.72395 13.3125 6.84375C13.3125 5.96355 12.599 5.25 11.7188 5.25H9.75V8.4375Z" fill="white"></path></g><defs><clipPath id="clip0_913_637"><rect width="12.375" height="12.375" fill="white" transform="translate(3 3)"></rect></clipPath></defs></svg>',
  wpp: '<svg width="36" height="36" viewBox="0 0 24 24" fill="none"><path d="M0 3C0 1.34315 1.34315 0 3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3Z" fill="#F5670F"></path><path d="M0 18.375H24V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V18.375Z" fill="#DF4D00"></path><path d="M21 1.14742e-07C22.6569 1.87166e-07 24 1.34315 24 3L24 21.0968C24 22.7002 22.7002 24 21.0968 24C19.5936 24 18.375 22.7814 18.375 21.2782L18.375 0L21 1.14742e-07Z" fill="#FF8940"></path><path d="M18.375 21.2782C18.375 22.7814 19.5936 24 21.0968 24C22.7002 24 24 22.7002 24 21.0968C24 19.5936 22.7814 18.375 21.2782 18.375H18.375V21.2782Z" fill="#F5670F"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M4.6875 3.5625H10.3125C12.3836 3.5625 14.0625 5.24143 14.0625 7.3125C14.0625 9.38357 12.3836 11.0625 10.3125 11.0625H6.5625V14.8125H4.6875V3.5625ZM6.5625 9.1875H10.3125C11.348 9.1875 12.1875 8.34803 12.1875 7.3125C12.1875 6.27697 11.348 5.4375 10.3125 5.4375H6.5625V9.1875Z" fill="white"></path></svg>',
  et: '<svg width="36" height="36" viewBox="0 0 24 24" fill="none"><path d="M0 3C0 1.34315 1.34315 0 3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3Z" fill="#1EA623"></path><path d="M0 18.375H24V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V18.375Z" fill="#148F19"></path><path d="M21 1.14742e-07C22.6569 1.87166e-07 24 1.34315 24 3L24 21.0968C24 22.7002 22.7002 24 21.0968 24C19.5936 24 18.375 22.7814 18.375 21.2782L18.375 0L21 1.14742e-07Z" fill="#2ABF30"></path><path d="M18.375 21.2782C18.375 22.7814 19.5936 24 21.0968 24C22.7002 24 24 22.7002 24 21.0968C24 19.5936 22.7814 18.375 21.2782 18.375H18.375V21.2782Z" fill="#1EA623"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M3.65625 4.03125V14.3438H14.7188V4.03125H3.65625ZM12.8438 5.90625H10.125V8.25H12.8438V5.90625ZM12.8438 10.125H10.125V12.4688H12.8438V10.125ZM8.25 8.25V5.90625H5.53125V8.25H8.25ZM5.53125 10.125H8.25V12.4688H5.53125V10.125Z" fill="white"></path></svg>',
}
export function formatTime(timestamp: number): string {
  const date = new Date(timestamp * 1000) // 接口是秒级时间戳
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}
