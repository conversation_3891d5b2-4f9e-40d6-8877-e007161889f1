import "./public-path";
import ReactDOM from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import App from "./App";

const isQiankun = !!window.__POWERED_BY_QIANKUN__;

console.log(isQiankun);

let root: ReactDOM.Root;

function render(props: Record<string, any> = {}) {
  const { container } = props;
  root = ReactDOM.createRoot( container ? container.querySelector("#root") : document.querySelector("#root") );
  root.render(
    <BrowserRouter basename={container ? "/react-sub" : "/"}>
      <App />
    </BrowserRouter>
  );
}

// 独立运行，直接调用 createRoot函数 render
if (!isQiankun) {
  render();
}

export async function bootstrap() {
  // console.log("app1 bootstraped 加载");
}

export async function mount(props: Record<string, any>) {
  // console.log("app1 mount 加载，主应用 prop: ", props);
  render(props);
}

// lifecycle => 卸载
export async function unmount(_props: any) {
  // console.log("app1 unmount 卸载", _props);
  root.unmount();
}
