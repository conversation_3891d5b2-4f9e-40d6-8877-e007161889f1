import { useState, useEffect, useRef, useCallback } from "react";
import { useMindMapStore } from "../store";

const EDIT_CONFIG = {
  INPUT: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 100,
  },
};

export function useMenuBar(nodeId: string) {
  const [showMenuBar, setShowMenuBar] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState("");

  const nodeRef = useRef<SVGGElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const menuBarRef = useRef<HTMLDivElement>(null);

  const { editingNodeId, setEditingNode, updateNodeText } = useMindMapStore();

  // 开始编辑
  const startEditing = useCallback(
    (text: string) => {
      // 如果当前有其他节点在编辑，先清除
      const currentEditingId = useMindMapStore.getState().editingNodeId;
      if (currentEditingId && currentEditingId !== nodeId) {
        setEditingNode(null);
      }

      // 延迟开始编辑，确保状态清理完成
      setTimeout(
        () => {
          setIsEditing(true);
          setShowMenuBar(true);
          setEditText(text);
          setEditingNode(nodeId);
        },
        currentEditingId && currentEditingId !== nodeId ? 50 : 0
      );
    },
    [nodeId, setEditingNode]
  );

  // 结束编辑
  const stopEditing = useCallback(
    (saveText: boolean = true) => {
      setIsEditing(false);
      setShowMenuBar(false);
      setEditingNode(null);

      // 清除输入框的选中状态和焦点
      if (inputRef.current) {
        inputRef.current.blur();
        inputRef.current.setSelectionRange(0, 0);
      }

      if (saveText) {
        const trimmedText = editText.trim();

        if (
          trimmedText.length < EDIT_CONFIG.INPUT.MIN_LENGTH ||
          trimmedText.length > EDIT_CONFIG.INPUT.MAX_LENGTH
        ) {
          setEditText("");
          return;
        }

        updateNodeText(nodeId, trimmedText);
      }

      // 确保清理输入框状态
      setEditText("");
    },
    [editText, nodeId, setEditingNode, updateNodeText]
  );

  // 监听编辑状态变化
  useEffect(() => {
    // 如果全局编辑状态为空，或者编辑的不是当前节点，则停止编辑
    if ((!editingNodeId || editingNodeId !== nodeId) && isEditing) {
      setIsEditing(false);
      setShowMenuBar(false);
      setEditText("");

      // 清除输入框的选中状态
      if (inputRef.current) {
        inputRef.current.blur();
        inputRef.current.setSelectionRange(0, 0);
      }
    }
  }, [editingNodeId, nodeId, isEditing]);

  // 全局点击监听
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      if (isEditing && showMenuBar) {
        const target = e.target as Node;

        // 检查点击是否在相关元素内
        const isClickInsideNode = nodeRef.current?.contains(target);
        const isClickInsideInput = inputRef.current?.contains(target);
        const isClickInsideMenuBar = menuBarRef.current?.contains(target);

        // 检查是否点击在ColorPicker的下拉内容中
        const isClickInsideColorPicker =
          (target as Element).closest("[data-radix-dropdown-content]") !==
            null ||
          (target as Element).closest(".react-colorful") !== null ||
          (target as Element).closest(".sketch-picker") !== null;

        // 如果点击在相关元素外，则隐藏MenuBar并清除错误
        if (
          !isClickInsideNode &&
          !isClickInsideInput &&
          !isClickInsideMenuBar &&
          !isClickInsideColorPicker
        ) {
          // 清除输入框的错误状态
          if (inputRef.current) {
            inputRef.current.style.border = "none";
          }
          stopEditing(true);
        }
      }
    };

    document.addEventListener("mousedown", handleGlobalClick);

    return () => {
      document.removeEventListener("mousedown", handleGlobalClick);
    };
  }, [isEditing, showMenuBar, stopEditing]);

  // 处理键盘事件
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      e.stopPropagation();

      if (e.key === "Enter") {
        stopEditing(true);
      }
      if (e.key === "Escape") {
        stopEditing(false);
      }
    },
    [stopEditing]
  );

  // 处理输入框失去焦点
  const handleInputBlur = useCallback(
    (e: React.FocusEvent) => {
      // 延迟处理，避免与MenuBar点击冲突
      setTimeout(() => {
        if (
          menuBarRef.current &&
          menuBarRef.current.contains(e.relatedTarget as Node)
        ) {
          return;
        }
        stopEditing(true);
      }, 100);
    },
    [stopEditing]
  );

  return {
    showMenuBar,
    isEditing,
    editText,
    setEditText,
    nodeRef,
    inputRef,
    menuBarRef,
    startEditing,
    stopEditing,
    handleKeyDown,
    handleInputBlur,
  };
}
