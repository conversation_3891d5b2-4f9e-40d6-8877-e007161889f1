// 目标接口 - 新系统期望的接口
export interface ModernPaymentSystem {
  pay(amount: number): Promise<{ success: boolean; transactionId: string }>
  refund(transactionId: string): Promise<boolean>
}

// 被适配者 - 旧支付系统
export class LegacyPaymentService {
  makePayment(amount: number, callback: (success: boolean) => void): void {
    console.log(`旧系统处理支付: $${amount}`)
    setTimeout(() => {
      callback(Math.random() > 0.3) // 模拟70%成功率
    }, 1000)
  }

  issueRefund(receiptNumber: string): boolean {
    console.log(`旧系统处理退款: ${receiptNumber}`)
    return Math.random() > 0.5 // 模拟50%退款成功率
  }
}
