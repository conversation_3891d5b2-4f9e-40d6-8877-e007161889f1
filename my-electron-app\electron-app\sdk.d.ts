// sdk.d.ts
import { webPreferences }
declare global {
  interface Window {
    sdk: SDK;
  }
}

export ReadFileOptions {
  filePath: string;         // 文件路径
  range: [number, number];  // 分片读取的开始和结束， 单次大小不超过1M
}
interface CommonRet {
   code: number;            // 0正常，其它异常可以自由设计
   msg?: string;            // 结果描述
}
export interface ReadFileResult {
  result: CommonRet;
  data?: {
    mimeType: string | null;            // 文件格式，后缀即可
    arrayBuffer: ArrayBuffer | null;    // 文件实体内容，为当前分片的内容
    finished: boolean;                  // 是否读完
  }
}
export interface OpenUrlOptions {
  url: string;                          // 打开的地址
  options: {
     type: 'webview' | 'browserwindow'  // 支持两种方式打开，browserwindow：独立窗口打开，webview: 在客户端主界面中以内嵌webview方式打开；
  } 
}

export interface SDK {
  readFile(options: ReadFileOptions): Promise<ReadFileResult>;
  openUrl(options: OpenUrlOptions): Promise<CommonRet>;
}