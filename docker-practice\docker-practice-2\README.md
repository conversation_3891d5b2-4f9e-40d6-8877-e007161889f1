## 本项目基于 qiankun 实现了一个典型的微前端架构，包含：

* 主应用（基座）：React 技术栈，负责注册和挂载子应用。

* 子应用 1：React 技术栈
* 子应用 2：Vue 技术栈

## 使用 Nginx + Docker Compose 实现统一部署，目录如下

```
qiankun-project/
├── docker-compose.yml
├── packages/
│   ├── react-host/      # React 主应用
│   │   └── Dockerfile
│   │   └── nginx.conf
│   ├── react-sub/       # React 子应用
│   │   └── Dockerfile
│   │   └── nginx.conf
│   └── vue-sub/         # Vue 子应用
│       └── Dockerfile
│       └── nginx.conf
```

## 快速启动

在根目录下执行：

docker-compose up --build

## 访问地址

主应用首页：[http://localhost:8083](https://)

React 子应用：[http://localhost:8083/react-sub](https://)

Vue 子应用：[http://localhost:8083/vue-sub](https://)

点击首页按钮可加载对应子应用，子应用由主应用动态加载并挂载。

## Nginx 配置说明（nginx.conf）

主应用

```server
listen 80;
server_name localhost;

location / {
root /usr/share/nginx/html;
try_files $uri /index.html;
}

```

子应用，需要设置跨域

```server
listen 80;
server_name localhost;

location / {
root /usr/share/nginx/html;
add_header Access-Control-Allow-Origin *;
add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
try_files $uri /index.html;
}

```

## 遇到的问题  

1. 子应用空白
   检查了 publicPath 配置；  
   检查了子应用路由的 base 是否与 主应用设置的路由 /react-sub 或 /vue-sub 匹配
2. qiankun生命周期
   微应用的生命周期boostrap、mount、unmount，在注册微应用只需要导出上面三个方法即可，分别对应微应用的注册、挂载、卸载。可看官方文档查看具体使用方法。
