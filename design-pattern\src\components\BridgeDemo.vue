<template>
  <div class="bridge-demo">
    <h2>桥接模式演示 - 设备遥控系统</h2>

    <div class="device-selection">
      <h3>选择设备</h3>
      <select v-model="selectedDeviceType">
        <option value="tv">电视</option>
        <option value="radio">收音机</option>
      </select>
      <button @click="initializeDevice">初始化设备</button>
    </div>

    <div class="remote-control" v-if="remoteControl">
      <h3>{{ remoteControlTitle }}</h3>

      <div class="device-status">
        <p>当前设备: {{ currentDeviceName }}</p>
        <p>音量: {{ currentVolume }}%</p>
        <p v-if="selectedDeviceType === 'tv'">频道: {{ currentChannel }}</p>
        <p v-else>频率: {{ currentChannel.toFixed(1) }} FM</p>
        <p>状态: {{ isPoweredOn ? '开启' : '关闭' }}</p>
      </div>

      <div class="basic-controls">
        <button @click="togglePower">电源 {{ isPoweredOn ? '关闭' : '开启' }}</button>
        <button @click="volumeUp" :disabled="!isPoweredOn">音量 +</button>
        <button @click="volumeDown" :disabled="!isPoweredOn">音量 -</button>
        <button @click="channelUp" :disabled="!isPoweredOn">频道 ↑</button>
        <button @click="channelDown" :disabled="!isPoweredOn">频道 ↓</button>
      </div>

      <div class="advanced-controls" v-if="isAdvancedRemote">
        <button @click="mute" :disabled="!isPoweredOn">静音</button>
        <button @click="setPresetChannel(3)" :disabled="!isPoweredOn">预设频道 3</button>
      </div>

      <div class="remote-type">
        <label> <input type="checkbox" v-model="useAdvancedRemote" /> 使用高级遥控器 </label>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { TV, Radio } from '../utils/bridge/devices'
import { BasicRemoteControl, AdvancedRemoteControl } from '../utils/bridge/devices'
import type { RemoteControl } from '../utils/bridge/types'

export default defineComponent({
  name: 'BridgeDemo',
  setup() {
    const selectedDeviceType = ref<'tv' | 'radio'>('tv')
    const useAdvancedRemote = ref(false)
    const device = ref<InstanceType<typeof TV | typeof Radio>>()
    const remoteControl = ref<RemoteControl>()

    const currentVolume = ref(0)
    const currentChannel = ref(0)
    const isPoweredOn = ref(false)

    const remoteControlTitle = computed(
      () =>
        `${useAdvancedRemote.value ? '高级' : '基础'}遥控器 - ${selectedDeviceType.value === 'tv' ? '电视' : '收音机'}`,
    )

    const currentDeviceName = computed(() => device.value?.getName() || '')

    const isAdvancedRemote = computed(
      () => useAdvancedRemote.value && remoteControl.value instanceof AdvancedRemoteControl,
    )

    const initializeDevice = () => {
      if (selectedDeviceType.value === 'tv') {
        device.value = new TV()
      } else {
        device.value = new Radio()
      }

      if (useAdvancedRemote.value) {
        remoteControl.value = new AdvancedRemoteControl(device.value)
      } else {
        remoteControl.value = new BasicRemoteControl(device.value)
      }

      // 初始化状态
      currentVolume.value = device.value.getVolume()
      currentChannel.value = device.value.getChannel()
      isPoweredOn.value = false
    }

    const updateStatus = () => {
      if (!device.value) return
      currentVolume.value = device.value.getVolume()
      currentChannel.value = device.value.getChannel()
    }

    const togglePower = () => {
      remoteControl.value?.togglePower()
      isPoweredOn.value = !isPoweredOn.value
      updateStatus()
    }

    const volumeUp = () => {
      remoteControl.value?.volumeUp()
      updateStatus()
    }

    const volumeDown = () => {
      remoteControl.value?.volumeDown()
      updateStatus()
    }

    const channelUp = () => {
      remoteControl.value?.channelUp()
      updateStatus()
    }

    const channelDown = () => {
      remoteControl.value?.channelDown()
      updateStatus()
    }

    const mute = () => {
      if (remoteControl.value instanceof AdvancedRemoteControl) {
        remoteControl.value.mute()
        updateStatus()
      }
    }

    const setPresetChannel = (channel: number) => {
      if (remoteControl.value instanceof AdvancedRemoteControl) {
        remoteControl.value.presetChannel(channel)
        updateStatus()
      }
    }

    return {
      selectedDeviceType,
      useAdvancedRemote,
      remoteControl,
      currentVolume,
      currentChannel,
      isPoweredOn,
      remoteControlTitle,
      currentDeviceName,
      isAdvancedRemote,
      initializeDevice,
      togglePower,
      volumeUp,
      volumeDown,
      channelUp,
      channelDown,
      mute,
      setPresetChannel,
    }
  },
})
</script>

<style scoped>
.bridge-demo {
  font-family: Arial, sans-serif;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.device-selection {
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f8f8;
  border-radius: 8px;
}

select {
  padding: 8px 12px;
  margin-right: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.remote-control {
  padding: 20px;
  background: #f0f7ff;
  border-radius: 8px;
  border: 1px solid #bbdefb;
}

.device-status {
  margin: 15px 0;
  padding: 10px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.basic-controls,
.advanced-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 15px 0;
}

button {
  padding: 8px 16px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.advanced-controls button {
  background: #2196f3;
}

.remote-type {
  margin-top: 15px;
}
</style>
