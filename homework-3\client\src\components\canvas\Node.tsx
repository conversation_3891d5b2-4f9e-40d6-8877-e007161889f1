import React, { useState, useEffect } from "react";
import { useMindMapStore } from "../../store/index";
import MenuBar from "../toolbars/MenuBar";
import NodeContextMenu from "../ui/ContextMenu";
import { useMenuBar } from "../../hooks/useMenuBar";

export interface NodeProps {
  id: string;
  x: number;
  y: number;
  text: string;
  selected?: boolean;
  level: number;
  collapsed?: boolean; // 添加展开收起属性
  fontColor?: string; // 添加字体颜色属性
  isBold?: boolean; // 添加粗体属性
  isItalic?: boolean; // 添加斜体属性
  borderWidth?: number; // 添加边框宽度属性
}

const EDIT_CONFIG = {
  INPUT: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 100,
  },
};

export function Node({
  id,
  x,
  y,
  text,
  selected,
  level,
  collapsed,
  fontColor,
  isBold,
  isItalic,
  borderWidth,
}: NodeProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [inputError, setInputError] = useState<string>("");
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(
    null
  );
  const [lastClickTime, setLastClickTime] = useState<number>(0);

  const {
    showMenuBar,
    isEditing,
    editText,
    setEditText,
    nodeRef,
    inputRef,
    menuBarRef,
    startEditing,
    handleKeyDown,
    handleInputBlur,
  } = useMenuBar(id);

  const {
    selectNode,
    clearSelection,
    addChildNode,
    toggleNodeCollapse,
    updateNodeFontColor,
    updateNodeBold,
    updateNodeItalic,
    nodes,
    updateNodeSize,
    editingNodeId,
    setEditingNode,
  } = useMindMapStore();

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  useEffect(() => {
    const style = getNodeStyle();
    const currentNode = nodes.find((n) => n.id === id);

    if (
      currentNode &&
      currentNode.width !== undefined &&
      Math.abs(currentNode.width - style.width) > 1
    ) {
      updateNodeSize(id, style.width, style.height);
    }
  }, [
    text,
    level,
    borderWidth,
    fontColor,
    isBold,
    isItalic,
    id,
    updateNodeSize,
    nodes,
  ]);

  const children = nodes.filter((n) => n.parentId === id);
  const showAddButton = children.length === 0 && level < 3;
  const showCollapseButton = children.length > 0 && level > 1; // 根节点不显示收起展开按钮
  const isAddButtonDisabled = level >= 3;

  const rootNode = nodes.find((n) => n.level === 1);
  const isLeftSide = rootNode && x < rootNode.x;

  // 文本宽度测量函数
  const measureTextWidth = (
    text: string,
    fontSize: number,
    fontWeight: string = "normal"
  ) => {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");
    if (!context) return text.length * fontSize * 0.6;

    context.font = `${fontWeight} ${fontSize}px Arial, sans-serif`;
    return context.measureText(text).width;
  };

  const getNodeStyle = () => {
    const baseWidth = level === 1 ? 120 : level === 2 ? 100 : 80;
    const fontSize = level === 1 ? 18 : level === 2 ? 16 : 14;
    const fontWeight = isBold ? "bold" : "normal";

    // 使用精确的文本宽度测量
    const textWidth = measureTextWidth(text, fontSize, fontWeight);
    const actualWidth = Math.max(baseWidth, textWidth + 32);

    const strokeColor =
      borderWidth && borderWidth > 0 ? "#333333" : "transparent";

    switch (level) {
      case 1:
        return {
          width: actualWidth,
          height: 50,
          rx: 8,
          fill: "#00a59a",
          stroke: strokeColor,
          strokeWidth: borderWidth || 0,
          textColor: fontColor || "white",
          fontSize: 18,
          fontWeight: isBold ? "bold" : "normal",
          fontStyle: isItalic ? "italic" : "normal",
        };
      case 2:
        return {
          width: actualWidth,
          height: 40,
          rx: 6,
          fill: "#eeeeee",
          stroke: strokeColor,
          strokeWidth: borderWidth || 0,
          textColor: fontColor || "black",
          fontSize: 16,
          fontWeight: isBold ? "bold" : "normal",
          fontStyle: isItalic ? "italic" : "normal",
        };
      default:
        return {
          width: actualWidth,
          height: 35,
          rx: 4,
          fill: "#ffffff",
          stroke: strokeColor,
          strokeWidth: borderWidth || 0,
          textColor: fontColor || "black",
          fontSize: 14,
          fontWeight: isBold ? "bold" : "normal",
          fontStyle: isItalic ? "italic" : "normal",
        };
    }
  };

  const style = getNodeStyle();
  const buttonXOffset = isLeftSide
    ? -(style.width / 2 + 15)
    : style.width / 2 + 15;

  const handleNodeClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    // 如果当前有其他节点在编辑状态，先结束编辑
    if (editingNodeId && editingNodeId !== id) {
      setEditingNode(null);
    }

    clearSelection();
    selectNode(id);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (e.button !== 0) return;
    if (isEditing) return;

    // 双击检测 - 如果是快速连续点击，不启动长按
    const now = Date.now();
    if (now - lastClickTime < 300) {
      // 可能是双击，不启动长按
      return;
    }
    setLastClickTime(now);
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 清除长按定时器
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }

    // 先选中节点，再开始编辑
    selectNode(id);

    // 使用 setTimeout 确保状态更新完成
    setTimeout(() => {
      startEditing(text);
    }, 0);
  };

  const handleInputMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleInputClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleAddChild = (e: React.MouseEvent) => {
    e.stopPropagation();
    addChildNode(id);
  };

  const handleToggleCollapse = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleNodeCollapse(id);
  };

  const handleFormatText = (format: string, value?: any) => {
    switch (format) {
      case "bold":
        updateNodeBold(id);
        break;
      case "italic":
        updateNodeItalic(id);
        break;
      case "fontColor":
        if (value) {
          updateNodeFontColor(id, value);
        }
        break;
      default:
        break;
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // 修改验证逻辑，包含空字符串的情况
    if (newValue.length < EDIT_CONFIG.INPUT.MIN_LENGTH) {
      setInputError(`文字大小最少${EDIT_CONFIG.INPUT.MIN_LENGTH}个字符`);
    } else if (newValue.length > EDIT_CONFIG.INPUT.MAX_LENGTH) {
      setInputError(`文字大小最多${EDIT_CONFIG.INPUT.MAX_LENGTH}个字符`);
      return; // 阻止输入超出限制的字符
    } else {
      setInputError("");
    }

    setEditText(newValue);
  };

  const handleLocalInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // 清除错误提示
    setInputError("");

    // 如果文本长度不符合要求，恢复原始文本
    const trimmedText = editText.trim();
    if (
      trimmedText.length < EDIT_CONFIG.INPUT.MIN_LENGTH ||
      trimmedText.length > EDIT_CONFIG.INPUT.MAX_LENGTH
    ) {
      setEditText(text);
    }

    // 调用原有的 blur 处理逻辑
    handleInputBlur(e);
  };

  // 监听编辑状态变化，当停止编辑时清除错误提示
  useEffect(() => {
    if (!isEditing) {
      setInputError("");
    }
  }, [isEditing]);

  return (
    <>
      <NodeContextMenu nodeId={id}>
        <g
          ref={nodeRef}
          transform={`translate(${x},${y})`}
          style={{ cursor: isEditing ? "default" : "pointer" }}
          onClick={handleNodeClick}
          onMouseDown={handleMouseDown}
          onDoubleClick={handleDoubleClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          data-node-id={id}
        >
          {/* 主节点 */}
          <rect
            x={-style.width / 2}
            y={-style.height / 2}
            width={style.width}
            height={style.height}
            rx={style.rx}
            fill={style.fill}
            stroke={style.stroke}
            strokeWidth={style.strokeWidth}
            filter="drop-shadow(0 2px 4px rgba(0,0,0,0.1))"
          />

          {/* 悬停框 */}
          {isHovered && !selected && !isEditing && (
            <rect
              x={-style.width / 2 - 2}
              y={-style.height / 2 - 2}
              width={style.width + 4}
              height={style.height + 4}
              rx={style.rx + 2}
              fill="none"
              stroke="#3b82f6"
              strokeWidth={2}
              strokeDasharray="5,5"
            />
          )}

          {/* 选中框 */}
          {selected && (
            <rect
              x={-style.width / 2 - 3}
              y={-style.height / 2 - 3}
              width={style.width + 6}
              height={style.height + 6}
              rx={style.rx + 3}
              fill="none"
              stroke="#3b82f6"
              strokeWidth={3}
            />
          )}

          {/* 文本内容 */}
          {isEditing ? (
            <foreignObject
              x={-style.width / 2}
              y={-style.height / 2}
              width={style.width}
              height={style.height}
              overflow="visible"
            >
              <div
                style={{
                  position: "relative",
                  width: "100%",
                  height: "100%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <input
                  ref={inputRef}
                  type="text"
                  value={editText}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  onMouseDown={handleInputMouseDown}
                  onClick={handleInputClick}
                  onBlur={handleLocalInputBlur}
                  style={{
                    width: "100%",
                    height: "100%",
                    border: inputError ? "1px solid #ef4444" : "none",
                    outline: "none",
                    fontSize: `${style.fontSize}px`,
                    textAlign: "center",
                    backgroundColor: "transparent",
                    color: style.textColor,
                    caretColor: style.textColor,
                    fontWeight: style.fontWeight,
                    fontStyle: style.fontStyle,
                  }}
                />
                {/* 只有在没有错误时才显示 MenuBar */}
                {showMenuBar && !inputError && (
                  <MenuBar ref={menuBarRef} onFormatText={handleFormatText} />
                )}
              </div>
            </foreignObject>
          ) : (
            <text
              x={0}
              y={0}
              textAnchor="middle"
              alignmentBaseline="middle"
              fontSize={style.fontSize}
              fill={style.textColor}
              fontWeight={style.fontWeight}
              fontStyle={style.fontStyle}
              pointerEvents="none"
            >
              {text}
            </text>
          )}

          {/* 错误提示 - 直接在SVG中渲染 */}
          {inputError && isEditing && (
            <foreignObject
              x={-style.width}
              y={style.height / 2 + 10}
              width={style.width * 2}
              height={30}
              overflow="visible"
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  width: "100%",
                  pointerEvents: "none",
                }}
              >
                <div
                  style={{
                    backgroundColor: "#ef4444",
                    color: "white",
                    padding: "4px 8px",
                    borderRadius: "4px",
                    fontSize: "12px",
                    whiteSpace: "nowrap",
                    boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                  }}
                >
                  {inputError}
                </div>
              </div>
            </foreignObject>
          )}

          {/* 添加子节点按钮 */}
          {showAddButton && (isHovered || selected) && !isEditing && (
            <g transform={`translate(${buttonXOffset}, 0)`}>
              <circle
                r={8}
                fill="#10b981"
                stroke="white"
                strokeWidth={2}
                onClick={handleAddChild}
                style={{ cursor: "pointer" }}
                opacity={isAddButtonDisabled ? 0.5 : 1}
                pointerEvents={isAddButtonDisabled ? "none" : "auto"}
              />
              <text
                x={0}
                y={0}
                textAnchor="middle"
                alignmentBaseline="middle"
                fontSize={12}
                fill="white"
                pointerEvents="none"
              >
                +
              </text>
            </g>
          )}

          {/* 收起/展开按钮 */}
          {showCollapseButton && (isHovered || selected) && !isEditing && (
            <g transform={`translate(${buttonXOffset}, 0)`}>
              <circle
                r={6}
                fill={collapsed ? "#ef4444" : "#10b981"}
                stroke="white"
                strokeWidth={1}
                onClick={handleToggleCollapse}
                style={{ cursor: "pointer" }}
              />
              <text
                x={0}
                y={0}
                textAnchor="middle"
                alignmentBaseline="middle"
                fontSize={8}
                fill="white"
                pointerEvents="none"
              >
                {collapsed ? "+" : "−"}
              </text>
            </g>
          )}
        </g>
      </NodeContextMenu>
    </>
  );
}
