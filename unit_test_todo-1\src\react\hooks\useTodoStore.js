import { useState, useCallback, useMemo } from "react";
import { generateTodoId } from "../utils/todoUtils";
export function useTodoStore() {
  const [todos, setTodos] = useState([]);

  const addTodo = useCallback((text) => {
    if (!text.trim()) return false;
    const newTodo = {
      id: generateTodoId(),
      text: text.trim(),
      completed: false,
      createdAt: new Date(),
    };
    setTodos((prevTodos) => [...prevTodos, newTodo]);
    return true;
  }, []);

  const toggleTodo = useCallback((id) => {
    setTodos((prevTodos) => {
      const updatedTodos = prevTodos.map((todo) =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      );
      return updatedTodos;
    });
    return true;
  }, []);

  const deleteTodo = useCallback((id) => {
    setTodos((prevTodos) => {
      const filteredTodos = prevTodos.filter((todo) => todo.id !== id);
      return filteredTodos;
    });
    return true;
  }, []);

  const clearCompleted = useCallback(() => {
    let completedCount = 0;
    setTodos((prevTodos) => {
      completedCount = prevTodos.filter((todo) => todo.completed).length;
      return prevTodos.filter((todo) => !todo.completed);
    });
    return completedCount;
  }, []);

  const updateTodo = useCallback((id, updates) => {
    setTodos((prevTodos) => {
      const updatedTodos = prevTodos.map((todo) =>
        todo.id === id ? { ...todo, ...updates } : todo
      );
      return updatedTodos;
    });
    return true;
  }, []);

  // Computed properties using useMemo
  const uncompletedCount = useMemo(() => {
    return todos.filter((todo) => !todo.completed).length;
  }, [todos]);

  const completedCount = useMemo(() => {
    return todos.filter((todo) => todo.completed).length;
  }, [todos]);

  const totalCount = useMemo(() => todos.length, [todos]);

  const filteredTodos = useMemo(() => {
    return (filter) => {
      switch (filter) {
        case "active":
          return todos.filter((todo) => !todo.completed);
        case "completed":
          return todos.filter((todo) => todo.completed);
        default:
          return todos;
      }
    };
  }, [todos]);

  return {
    todos,
    addTodo,
    toggleTodo,
    deleteTodo,
    clearCompleted,
    updateTodo,
    uncompletedCount,
    completedCount,
    totalCount,
    filteredTodos,
  };
}
