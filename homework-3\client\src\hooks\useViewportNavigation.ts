import React, { useState, useEffect, useCallback, useRef } from "react";
import { useMindMapStore } from "../store";
import { throttle } from "../utils/performance";

/*
 * 使用左键长按拖拽画布
 * 使用滚轮移动画布
 */
export function useViewportNavigation(
  canvasRef: React.RefObject<HTMLDivElement | null>
) {
  const viewport = useMindMapStore((s) => s.viewport);
  const setViewport = useMindMapStore((s) => s.setViewport);

  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, y: 0 });
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(
    null
  );
  const [isLongPress, setIsLongPress] = useState(false);
  const [isRightDrag, setIsRightDrag] = useState(false);
  const lastViewportRef = useRef(viewport);

  const LONG_PRESS_DELAY = 200;

  const handleMouseDown = (e: React.MouseEvent) => {
    // 如果ColorPicker打开，禁用拖拽
    if (document.body.hasAttribute("data-color-picker-open")) {
      return;
    }

    // 检查是否在编辑状态，如果是则禁用拖拽
    const { editingNodeId } = useMindMapStore.getState();
    if (editingNodeId) {
      return;
    }

    if (e.button === 0) {
      // 左键长按拖拽（只在节点上）
      const target = e.target as HTMLElement;
      const isNodeClick = target.closest("[data-node-id]");

      // 检查是否点击在输入框上
      const isInputClick =
        target.tagName === "INPUT" || target.closest("input");
      if (isInputClick) {
        return; // 如果点击在输入框上，不启动拖拽
      }

      if (isNodeClick) {
        e.preventDefault();

        const timer = setTimeout(() => {
          document.getSelection()?.removeAllRanges();
          setIsLongPress(true);
          setIsPanning(true);
          setPanStart({ x: e.clientX, y: e.clientY });
          lastViewportRef.current = viewport;
        }, LONG_PRESS_DELAY);

        setLongPressTimer(timer);
      }
    } else if (e.button === 2) {
      // 右键立即开始拖拽
      e.preventDefault();
      setIsRightDrag(true);
      setIsPanning(true);
      setPanStart({ x: e.clientX, y: e.clientY });
      lastViewportRef.current = viewport;
    }
  };

  const handleMouseUp = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
    setIsPanning(false);
    setIsLongPress(false);
    setIsRightDrag(false);
  };

  const handleMouseMove = useCallback(
    throttle((e: React.MouseEvent) => {
      // 检查是否在编辑状态，如果是则禁用拖拽移动
      const { editingNodeId } = useMindMapStore.getState();
      if (editingNodeId) {
        return;
      }

      if (isPanning && (isLongPress || isRightDrag)) {
        e.preventDefault();
        e.stopPropagation();

        const dx = e.clientX - panStart.x;
        const dy = e.clientY - panStart.y;

        const newViewport = {
          ...lastViewportRef.current,
          x: lastViewportRef.current.x + dx / viewport.zoom,
          y: lastViewportRef.current.y + dy / viewport.zoom,
        };
        setViewport(newViewport);
      }
    }, 8),
    [isPanning, isLongPress, isRightDrag, panStart, setViewport, viewport.zoom]
  );

  // // 使用节流优化滚轮缩放和滚动
  // const handleWheel = useCallback(
  //   throttle((e: React.WheelEvent) => {
  //     console.log("滚轮事件触发:", { ctrlKey: e.ctrlKey, deltaY: e.deltaY });

  //     if (e.ctrlKey) {
  //       // Ctrl + 滚轮：缩放功能
  //       e.preventDefault();
  //       e.stopPropagation();
  //       const delta = e.deltaY > 0 ? 0.9 : 1.1;
  //       const newZoom = Math.max(0.1, Math.min(3, viewport.zoom * delta));
  //       const rect = canvasRef.current?.getBoundingClientRect();
  //       if (rect) {
  //         const mouseX = e.clientX - rect.left;
  //         const mouseY = e.clientY - rect.top;

  //         // 修复：缩放时以鼠标位置为中心点进行缩放
  //         const scaleFactor = delta;
  //         const newViewport = {
  //           zoom: newZoom,
  //           x: viewport.x - (mouseX - viewport.x) * (scaleFactor - 1),
  //           y: viewport.y - (mouseY - viewport.y) * (scaleFactor - 1),
  //         };
  //         console.log("缩放中:", { delta, newZoom, newViewport });
  //         setViewport(newViewport);
  //       }
  //     }
  //   }, 16), // 使用更短的节流时间以获得更流畅的体验
  //   [viewport, setViewport, canvasRef]
  // );

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (
        e.key === "Delete" ||
        e.key === "Backspace" ||
        e.key === "Enter" ||
        e.key === "Tab"
      ) {
        if (longPressTimer) {
          clearTimeout(longPressTimer);
          setLongPressTimer(null);
        }
        setIsPanning(false);
        setIsLongPress(false);
        setIsRightDrag(false);
      }
    };

    const handleMouseDownTyped = (e: Event) => {
      handleMouseDown(e as unknown as React.MouseEvent);
    };

    const handleMouseMoveTyped = (e: Event) => {
      handleMouseMove(e as unknown as React.MouseEvent);
    };

    const handleMouseUpTyped = () => handleMouseUp();

    canvas.addEventListener("mousedown", handleMouseDownTyped, {
      passive: false,
    });
    canvas.addEventListener("mousemove", handleMouseMoveTyped, {
      passive: false,
    });
    window.addEventListener("mouseup", handleMouseUpTyped);
    document.addEventListener("keydown", handleKeyDown);

    return () => {
      canvas.removeEventListener("mousedown", handleMouseDownTyped);
      canvas.removeEventListener("mousemove", handleMouseMoveTyped);
      window.removeEventListener("mouseup", handleMouseUpTyped);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [canvasRef, handleMouseMove, longPressTimer]);

  return { isPanning, isLongPress, isRightDrag };
}
