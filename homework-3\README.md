# 思维导图项目

## 技术栈

### 前端技术栈
- **React 18** + **TypeScript** + **Vite**
- **Zustand**
- **Radix UI + tailwind**
- **SVG + HTML**

### 后端技术栈
- **Node.js + Koa.js**

## 启动项目

### 前端启动
```bash
cd client
pnpm install
pnpm dev
```

### 后端启动  
```bash
cd server
pnpm install
pnpm start
```

## 项目目录结构
```
homework-3/
├── client/                     # 前端项目 (React + TypeScript)
│   ├── src/
│   │   ├── components/         # 组件目录
│   │   │   ├── canvas/         # 画布相关组件
│   │   │   │   ├── Canvas.tsx  # 主画布组件，负责渲染节点和连线
│   │   │   │   ├── Node.tsx    # 节点组件，支持编辑、样式设置等
│   │   │   │   └── Edge.tsx    # 连线组件，支持直线和折线样式
│   │   │   ├── toolbars/       # 工具栏组件
│   │   │   │   ├── Toolbar.tsx       # 主工具栏容器
│   │   │   │   ├── StartToolbar.tsx  # "开始"工具栏，基础操作功能
│   │   │   │   ├── StyleToolbar.tsx  # "样式"工具栏，样式设置功能
│   │   │   │   └── MenuBar.tsx       # 节点编辑菜单栏，双击节点时显示
│   │   │   ├── ui/             # 通用UI组件
│   │   │   │   ├── ContextMenu.tsx       # 右键节点菜单
│   │   │   │   ├── ColorPicker.tsx       # 颜色选择器
│   │   │   │   ├── BorderWidthDropdown.tsx # 边框宽度选择器
│   │   │   │   ├── AIProgressCard.tsx    # AI生成进度卡片
│   │   │   │   └── Tooltip.tsx           # 提示
│   │   │   └── icons/          # 图标组件
│   │   │       └── SvgIcons.tsx    # 统一的SVG图标组件库
│   │   ├── hooks/              # 自定义hooks
│   │   │   ├── useCanvasInteractions.ts  # 画布交互逻辑
│   │   │   ├── useViewportNavigation.ts  # 视口导航（拖拽、缩放）
│   │   │   ├── useSvgAutosize.ts         # SVG画布自适应大小
│   │   │   ├── useMenuBar.ts             # 菜单栏状态管理
│   │   │   ├── useTextFormatting.ts      # 文本格式修改
│   │   │   ├── useShortcuts.ts           # 键盘快捷键
│   │   │   └── useZoom.ts                # 缩放
│   │   ├── store/              # 状态管理 (Zustand)
│   │   │   └── index.ts        # 主要状态管理，包含节点、连线、AI等所有状态
│   │   ├── utils/              # 工具函数
│   │   │   └── performance.ts  # 性能优化工具（节流等）
│   │   ├── types/              # TypeScript类型定义
│   │   │   └── react-color.d.ts # React颜色组件类型声明
│   │   ├── App.tsx             # 应用根组件
│   │   └── main.tsx            # 应用入口
│   ├── package.json
│   ├── vite.config.ts
│   └── tsconfig.json
└── server/                     # 后端项目 (Node.js + Koa.js)
    ├── controller/             # 控制器
    │   └── ai.js              # AI相关接口控制器
    ├── routes/                # 路由
    │   └── api.js             # API路由定义
    ├── libs/                  # 工具库
    │   └── axios.js           # HTTP请求配置
    ├── app.js                 # 服务器入口文件
    ├── package.json
    └── pnpm-lock.yaml
```

## 功能点描述
1. 长按鼠标右键拖拽画布，ctrl+上下滚轮缩放画布，左键长按节点拖拽
2. 一级菜单开始，样式，插入，视图，和导出，点击高亮切换，且下方出现对应的二级菜单，菜单鼠标hover的时候，有文字tips提示菜单功能
3. 样式二级菜单，按钮内容超出容器宽度时，能滑动展开或收起，避免按钮被遮挡。
4. 二级菜单，未选中节点时，部分按钮置灰，点击弹出不可用tooltips。
5. 二级菜单实现边框宽度、加粗、斜体、字体颜色功能；实现撤销、恢复功能；实现新增子主题、同级主题、父主题功能。
6. 节点选中和hover状态设置了悬停框和选中框
7. 双击节点，文本可编辑，弹出格式化菜单，格式化菜单与二级菜单的加粗、斜体、字体颜色的状态可以保持一致。
8. 选中节点，右侧出现 + 按钮，点击可新增子节点。
9. 选中节点，右侧出现 +/- 按钮，点击可展开/收起子节点。
10. 单击选中矩形，可按住del键删除该矩形（选中第一个主题时不删除），如果有子主题，也会一并删除。
11. 单击选中矩形，Tab键新增子主题，Enter键新增同级主题。、
12. 右键节点，出现右键菜单，实现了新增主题、删除当前主题、AI创作功能。
13. 实现了数据持久化，存储在localstorage中
14. 实现了AI创作功能，点击AI创作按钮，会调用后端接口，生成子主题，流式渲染到画布上。(只有1级节点可以点击AI创作)
15. AI创作过程中会有进度条，创作完成后有重新生成、弃用、使用生成主题三个功能。

## 设计思路

### 1、UI设计
页面划分为两大部分，使用radix ui组件和tailwind设置样式：

**上部分Toolbar工具栏：**
- "开始"子菜单：基础操作
- "样式"子菜单：样式设置。
- MenuBar 菜单：双击节点弹出的格式化菜单。

**下部分canvas画布：**
- 画布使用 svg 构建
- 连线：path
- 节点：rect + foreignObject
- 所有 icon 图标统一放在`icons/SvgIcons.tsx`中，封装成`CustomIcons`组件，通过参数控制获取对应的SVG

### 2、核心组件设计

#### Canvas画布设计
- **双层结构**：使用HTML div作为容器，内嵌SVG处理连线节点绘制
- **坐标系统**：采用SVG坐标系，通过viewbox设置画布尺寸
- **视口管理**：通过transform实现视图缩放和平移，useSvgAutosize(nodes) 自动计算 SVG 的宽高和视口大小。
- **事件委托**：在画布层面统一处理 取消选中 点击事件，提高性能
- **渲染优化**：使用 `useMemo` 优化节点和连线组件渲染，避免不必要的重渲染

#### Node节点设计
- **SVG元素**：使用 `foreignObject` 在SVG中嵌入HTML，实现文本编辑
- **自适应尺寸**：根据文本内容动态计算节点宽高
- **交互状态管理**：支持选中、编辑、悬停等多种状态
- **样式系统**：支持字体颜色、粗体、斜体、边框宽度等样式设置
- **层级限制**：最多支持3级节点（根节点-主题-子主题）
- **双击编辑**：双击进入编辑模式，支持文本修改

#### Edge连线设计
- **折线样式**：支持折线连线样式，根据节点在根节点左右自动计算最佳连接点位置

#### 布局设计
- **布局结构**：采用树形结构，子节点在根节点两侧左右分布
- **布局自适应**：根据节点层级和数量动态调整垂直间距，防止节点重叠，自动调整位置

### 3、全局状态管理
使用zustand进行状态管理和数据持久化，包括：
- **节点数据管理**：nodes数组存储所有节点信息，包含位置、样式、层级、收起展开状态等
- **连线数据管理**：edges数组存储连线的位置、样式等
- **视口状态控制**：viewport对象管理画布的缩放
- **选择状态管理**：selection对象跟踪当前选中的节点
- **编辑状态管理**：editingNodeId标记当前正在编辑的节点
- **AI处理状态**：isAIProcessing、aiProgress等管理AI创作流程
- **性能优化缓存**：nodeMap、childrenMap、parentMap等缓存映射提升查询性能

### 4、自定义hooks

#### 画布交互相关
- **`useCanvasInteractions`**: 处理画布点击事件，实现节点选择和清除选择功能
  - 使用事件委托优化性能，将点击事件绑定到画布（所有节点的父元素）上，避免为每个节点单独绑定事件，消耗性能
  - 通过 `requestAnimationFrame` 进行批量状态更新
  
- **`useViewportNavigation`**: 实现画布的拖拽和缩放功能
  - 支持右键拖拽移动画布视口
  - 支持 Ctrl+滚轮进行缩放，以鼠标位置为中心点
  - 使用节流（throttle）优化高频事件处理，提升性能
  - 修复缩放时坐标变换计算问题

#### 布局和渲染相关
- **`useSvgAutosize`**: 动态计算SVG画布的尺寸
  - 根据所有节点位置自动调整画布大小
  - 考虑节点实际宽高，添加合适的padding
  - 设置最小尺寸，确保良好的用户体验

#### 交互和编辑相关
- **`useMenuBar`**: 管理节点双击编辑时的菜单栏显示
  - 控制编辑状态的开始和结束
  - 处理全局点击和键盘事件
  - 避免多个节点同时编辑的冲突

- **`useTextFormatting`**: 处理文本格式化功能
  - 统一管理粗体、斜体、字体颜色等样式设置
  - 与选中状态联动，只对当前选中节点生效

#### 功能增强相关
- **`useShortcuts`**: 实现键盘快捷键功能
  - Delete/Backspace: 删除选中的节点
  - Ctrl+F: 自适应视图
  - Tab: 为选中节点添加子节点
  - Enter: 为选中节点添加同级节点

- **`useZoom`**: 画布缩放功能

### 5、AI智能创作功能
- **流式AI创作**：支持基于选中根节点内容生成子节点
- **实时进度显示**：通过AIProgressCard组件显示AI生成进度
- **内容确认机制**：用户可以选择确认或丢弃AI生成的内容

## 遇到的问题及解决方案


## 遇到的问题及解决方案

### 1、布局计算和自适应问题
**问题描述**：
- 新增、删除、收起、展开节点时，页面布局无法自适应变化
- 节点内容宽度变化时，连线位置计算错误

**解决方案**：
- 使用 `useSvgAutosize` 动态计算SVG画布尺寸，根据所有节点边界自动调整
- 节点尺寸变化时，通过 `updateNodeSize` 更新缓存，触发连线重新计算

### 2、节点连线问题
**问题描述**：
- 如何实现和原型一样的连线效果

**解决方案**：
- 根节点与二级节点使用贝塞尔曲线，二级节点后的连线不使用贝塞尔曲线，而是设置拐点，然后将拐点圆滑处理
- 根节点与二级节点连线的起点，根据高度差决定出发点，离根节点越远越靠近中心，根据高度差决定出发点，离根节点越远越靠近中心

### 3、性能优化问题
**问题描述**：
- 大量节点时渲染性能下降
- 频繁的递归查询影响交互流畅度
- 组件重渲染过多

**解决方案**：
- 使用 `React.memo` 优化节点和连线组件，避免不必要的重渲染
- 建立缓存映射替代递归查询：`nodeMap`、`childrenMap`、`parentMap`
- 使用 `useMemo` 优化边缘和节点渲染列表计算
- 事件委托优化，避免为每个节点单独绑定事件监听器
- 使用 `requestAnimationFrame` 进行批量状态更新

### 4、编辑状态管理问题
**问题描述**：
多个节点同时编辑状态冲突

**解决方案**：
- 通过 `editingNodeId` 全局状态确保只有一个节点处于编辑状态
- 使用 ref 精确检测点击区域，区分节点、输入框、菜单栏

### 5、MenuBar隐藏问题
**问题描述**：
MenuBar 在点击画布其他地方、点击其他节点时不及时消失。
**解决方案**：
- 创建了 `useMenuBar` hook 来统一管理 MenuBar 的显示和隐藏逻辑
- 添加了全局点击监听，当点击 MenuBar、节点或输入框外的区域时自动隐藏
- 将正在编辑的节点渲染在最后，确保在最上层

### 6、节点字符限制提示
**问题描述**：
不显示的问题
**解决方案**：
- 通过foreignObject将错误提示放在 SVG 内部，使用相对定位


## 待优化方向

1. **组件复用问题**：MenuBar（双击节点弹出的菜单）未复用StartToolbar里的按钮组件
   - 可以把加粗等按钮单独提取出一个组件
   - 但这些组件只用2次，所以暂时没有单独提取
   
2. **节点层级限制**：当前限制为3级
   - 放开限制后不确定功能是否会出现bug

3. **服务端流式AI功能**：
   - 还需要完善异常处理机制
   - 需要完善异常结果的返回处理，在AIprogressCard中显示错误提示

4. **节点内单个文本样式**：
   - 当前只能改整个节点文本，无法选中单个字符进行修改