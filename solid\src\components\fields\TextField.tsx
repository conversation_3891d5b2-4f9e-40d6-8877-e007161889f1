import type { TextField as TextFieldConfig } from "@/types/form";

interface TextFieldProps {
  field: TextFieldConfig;
  value: string;
  error?: string;
  onChange: (value: string) => void;
}

export function TextField({ field, value, error, onChange }: TextFieldProps) {
  return (
    <div className="space-y-2">
      <input
        type="text"
        placeholder={field.placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        maxLength={field.maxLength}
        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
          error ? "border-red-500" : ""
        }`}
      />

      {error && <div className="text-sm text-red-500">{error}</div>}
    </div>
  );
}
