<!-- 聊天主页 -->
<template>
  <div class="flex h-screen overflow-hidden bg-gray-50 font-sans">
    <!-- 左侧聊天列表 -->
    <ChatSidebar
      :contacts="chats"
      :current-contact-id="currentChatId"
      :is-loading="isLoadingUsers"
      @select-contact="selectChat"
      @refresh="loadUsers"
    />

    <!-- 右侧聊天区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <ChatHeader
        :current-contact="currentChat"
        @more-options="showMoreOptions"
        @show-user-detail="showUserDetail"
        @logout="handleLogout"
      />

      <!-- 消息列表 -->
      <MessageList
        ref="messageListRef"
        :messages="currentMessages"
        :current-contact="currentChat"
        @show-user-detail="showUserDetail"
      />

      <!-- 消息输入 -->
      <MessageInput
        :current-contact="currentChat"
        @send-message="sendMessage"
        @insert-emoji="insertEmoji"
        @attach-file="attachFile"
        @insert-image="insertImage"
      />
    </div>

    <!-- 用户详情弹窗 -->
    <UserDetailModal
      :is-visible="showUserDetailModal"
      :user-id="selectedUserId"
      @close="closeUserDetail"
      @start-chat="handleStartChat"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '../store/user'
import { apiClient } from '../api'
import type { User } from '../api'
import ChatSidebar from '../components/ChatSidebar.vue'
import ChatHeader from '../components/ChatHeader.vue'
import MessageList from '../components/MessageList.vue'
import MessageInput from '../components/MessageInput.vue'
import UserDetailModal from '../components/UserDetailModal.vue'

// 用户状态管理
const userStore = useUserStore()

// 响应式数据
const currentChatId = ref<string | null>(null)
const isLoadingUsers = ref(false)
const messageListRef = ref<InstanceType<typeof MessageList>>()

// 用户详情弹窗相关
const showUserDetailModal = ref(false)
const selectedUserId = ref<string | null>(null)

// 聊天数据 - 从API获取的用户列表转换为聊天列表
const chats = ref<
  Array<{
    id: string
    name: string
    avatar: string
    status: string
    lastMessage: string
    lastMessageTime: Date
    user: User
  }>
>([])

// 从API加载用户列表
const loadUsers = async () => {
  try {
    isLoadingUsers.value = true
    const response = await apiClient.getUsers()

    if (response.success) {
      // 将用户列表转换为聊天列表格式
      chats.value = response.users
        .filter((user) => user.id !== userStore.currentUser.value?.id) // 排除当前用户
        .map((user) => ({
          id: user.id,
          name: user.displayName,
          avatar: getNameAvatar(user.displayName), // 使用姓名后两个字作为头像
          status: user.isOnline ? '在线' : '离线',
          lastMessage: '暂无消息',
          lastMessageTime: new Date(user.lastOnlineTime),
          user
        }))

      console.log('加载用户列表成功:', chats.value)
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    // 如果API失败，使用默认数据
    chats.value = [
      {
        id: '1',
        name: '朱钰蒨',
        avatar: getNameAvatar('朱钰蒨'),
        status: '在线',
        lastMessage: '你好，最近怎么样？',
        lastMessageTime: new Date(Date.now() - 1000 * 60 * 5),
        user: {
          id: '1',
          username: 'zhuyuqian',
          email: '<EMAIL>',
          displayName: '朱钰蒨',
          avatar: '👩',
          department: '产品部',
          position: '产品经理',
          isOnline: true,
          lastOnlineTime: new Date().toISOString()
        }
      }
    ]
  } finally {
    isLoadingUsers.value = false
  }
}

interface Message {
  id: string
  senderId: string
  senderName: string
  content: string
  timestamp: Date
}

// 消息数据
const messages = ref<Record<string, Message[]>>({
  '1': [
    {
      id: '1',
      senderId: 'user1',
      senderName: '朱钰蒨',
      content: '你好！很高兴见到你',
      timestamp: new Date(Date.now() - 1000 * 60 * 10)
    },
    {
      id: '2',
      senderId: 'current',
      senderName: userStore.userDisplayName.value || '我',
      content: '你好！我也很高兴见到你',
      timestamp: new Date(Date.now() - 1000 * 60 * 8)
    },
    {
      id: '4',
      senderId: 'user1',
      senderName: '朱钰蒨',
      content:
        '这是一条很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的消息，用来测试自动换行功能是否正常工作。',
      timestamp: new Date(Date.now() - 1000 * 60 * 5)
    },
    {
      id: '5',
      senderId: 'current',
      senderName: userStore.userDisplayName.value || '我',
      content:
        '这是一条包含换行的消息：\n第一行内容\n第二行内容\n第三行内容\n用来测试换行符是否能正确显示。',
      timestamp: new Date(Date.now() - 1000 * 60 * 3)
    }
  ],
  '2': [
    {
      id: '3',
      senderId: 'user2',
      senderName: '张三',
      content: '明天的会议准备好了吗？',
      timestamp: new Date(Date.now() - 1000 * 60 * 30)
    }
  ],
  '3': []
})

// 计算属性
const currentChat = computed(() => {
  return chats.value.find((chat) => chat.id === currentChatId.value)
})

const currentMessages = computed(() => {
  return currentChatId.value ? messages.value[currentChatId.value] || [] : []
})

// 组件挂载时的初始化
onMounted(async () => {
  // 检查用户是否已登录
  if (!userStore.isAuthenticated.value) {
    console.log('用户未登录，应该跳转到登录页')
    return
  }

  // 加载用户列表
  await loadUsers()

  // 默认选择第一个聊天
  if (chats.value.length > 0) {
    currentChatId.value = chats.value[0].id
  }
})

// 工具函数
const getNameAvatar = (name: string) => {
  // 获取姓名的后两个字作为头像
  return name.length >= 2 ? name.slice(-2) : name
}

// 方法
const selectChat = (chatId: string) => {
  currentChatId.value = chatId
}

const showMoreOptions = () => {
  console.log('显示更多选项')
  // TODO: 实现更多选项功能
}

// 显示用户详情
const showUserDetail = (userId: string) => {
  selectedUserId.value = userId
  showUserDetailModal.value = true
}

// 关闭用户详情弹窗
const closeUserDetail = () => {
  showUserDetailModal.value = false
  selectedUserId.value = null
}

// 处理从用户详情弹窗发起聊天
const handleStartChat = (userId: string) => {
  // 查找对应的聊天
  const chat = chats.value.find((c) => c.user.id === userId)
  if (chat) {
    currentChatId.value = chat.id
  }
}

const handleLogout = () => {
  userStore.logout()
  // 触发父组件重新渲染，显示登录页面
  window.location.reload()
}

const insertEmoji = () => {
  console.log('插入表情')
  // TODO: 实现表情选择功能
}

const attachFile = () => {
  console.log('附加文件')
  // TODO: 实现文件上传功能
}

const insertImage = () => {
  console.log('插入图片')
  // TODO: 实现图片上传功能
}

const sendMessage = (content: string) => {
  if (!content.trim() || !currentChatId.value) {
    return
  }

  const newMessage = {
    id: Date.now().toString(),
    senderId: 'current',
    senderName: userStore.userDisplayName.value || '我',
    content: content,
    timestamp: new Date()
  }

  // 添加消息到当前聊天
  if (!messages.value[currentChatId.value]) {
    messages.value[currentChatId.value] = []
  }
  messages.value[currentChatId.value].push(newMessage)

  // 更新聊天列表中的最后消息
  const chat = chats.value.find((c) => c.id === currentChatId.value)
  if (chat) {
    chat.lastMessage = content
    chat.lastMessageTime = new Date()
  }
}
</script>
