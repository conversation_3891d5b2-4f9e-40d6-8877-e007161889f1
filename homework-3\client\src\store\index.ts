import { create } from "zustand";
import { persist } from "zustand/middleware";
import {
  LAYOUT_CONFIG,
  STYLE_CONFIG,
  EDIT_CONFIG,
  HISTORY_CONFIG,
} from "../config/constants";

export interface MindMapNode {
  id: string;
  x: number;
  y: number;
  text: string;
  level: number;
  parentId: string | null;
  width?: number;
  height?: number;
  collapsed?: boolean; // 是否收起子节点
  children?: string[]; // 子节点ID列表
  fontColor?: string; // 字体颜色
  isBold?: boolean; // 是否加粗
  isItalic?: boolean; // 是否斜体
  borderWidth?: number; // 边框宽度
}

export interface MindMapEdge {
  id: string;
  from: string;
  to: string;
  bendPoint?: { x: number; y: number }; // 添加拐点属性
  style?: "straight" | "curved" | "polyline" | string; // 线条样式
}

interface MindMapState {
  nodes: MindMapNode[];
  edges: MindMapEdge[];
  history: { nodes: MindMapNode[]; edges: MindMapEdge[] }[];
  future: { nodes: MindMapNode[]; edges: MindMapEdge[] }[];
  selection: { nodes: string[]; edges: string[] };
  viewport: { zoom: number; x: number; y: number };
  editingNodeId: string | null; // 正在编辑的节点ID
  isAIProcessing: boolean;
  aiProgress: string;
  pendingAINodes: string[]; // 待确认的AI生成节点ID列表
  abortController: AbortController | null; // 添加AbortController引用

  // 添加缓存映射
  nodeMap: Map<string, MindMapNode>; // 节点ID到节点的映射
  childrenMap: Map<string, string[]>; // 父节点ID到子节点ID列表的映射
  parentMap: Map<string, string>; // 子节点ID到父节点ID的映射

  // 思维导图功能
  addChildTheme: (nodeId?: string) => void;
  addSameLevelTheme: (nodeId?: string) => void;
  addParentTheme: (nodeId?: string) => void;
  updateNodeText: (id: string, text: string) => void;
  updateNodeFontColor: (id: string, fontColor: string) => void; // 更新节点字体颜色
  updateNodeBold: (id: string) => void; // 更新节点粗体状态
  updateNodeItalic: (id: string) => void; // 更新节点斜体状态
  updateNodeBorderWidth: (id: string, borderWidth: number) => void; // 更新节点边框宽度

  setEditingNode: (nodeId: string | null) => void; // 设置正在编辑的节点

  // AI相关
  generateChildrenFromAI: (nodeId: string) => void;
  generateChildrenFromAIStream: (nodeId: string) => void; // 流式AI创作方法
  stopAIGeneration: () => void; // 停止AI生成
  confirmAIGeneration: () => void; // 确认使用AI生成的内容
  discardAIGeneration: () => void; // 丢弃AI生成的内容
  addToPendingNodes: (nodeId: string) => void; // 添加到待确认列表

  // 布局相关
  calculateLayout: () => void;
  addChildNode: (parentId: string) => void;
  toggleNodeCollapse: (nodeId: string) => void;
  updateNodeSize: (id: string, width: number, height: number) => void;

  // 基础操作
  moveNode: (id: string, x: number, y: number) => void;
  removeNode: (id: string) => void;
  removeEdge: (id: string) => void;
  setSelection: (selection: any) => void;
  setViewport: (viewport: { zoom: number; x: number; y: number }) => void;
  fitView: () => void;
  clearSelection: () => void;
  selectNode: (id: string) => void;
  pushHistory: () => void;
  undo: () => void;
  redo: () => void;

  isNodeVisible: (nodeId: string) => boolean;
  updateMaps: () => void; // 更新缓存映射
  getDescendants: (nodeId: string) => string[]; // 获取所有后代节点
  getAncestors: (nodeId: string) => string[]; // 获取所有祖先节点

  // 删除节点函数（删除节点本身+所有子孙节点）
  deleteNodeWithDescendants: (nodeId: string, skipHistory: boolean) => void;

  // 清空子节点函数（只删除子节点，保留父节点）
  clearChildNodes: (parentId: string) => void;
}

export const useMindMapStore = create<MindMapState>()(
  persist(
    (set, get) => ({
      nodes: [
        {
          id: "root",
          x: LAYOUT_CONFIG.CENTER_X,
          y: LAYOUT_CONFIG.CENTER_Y,
          text: EDIT_CONFIG.DEFAULT_TEXTS.ROOT,
          level: 1,
          parentId: null,
          width: LAYOUT_CONFIG.NODE_DIMENSIONS.LEVEL_1.width,
          height: LAYOUT_CONFIG.NODE_DIMENSIONS.LEVEL_1.height,
          collapsed: false,
          children: [],
          fontColor: STYLE_CONFIG.NODE_COLORS.LEVEL_1.textColor,
        },
      ],
      edges: [],
      history: [],
      future: [],
      selection: { nodes: [], edges: [] },
      viewport: { zoom: 1, x: 0, y: 0 },
      editingNodeId: null, // 初始化编辑状态
      isAIProcessing: false,
      aiProgress: "",
      pendingAINodes: [], // 初始化待确认的AI生成节点ID列表
      abortController: null, // 添加AbortController引用

      // 初始化缓存映射
      nodeMap: new Map(),
      childrenMap: new Map(),
      parentMap: new Map(),

      // 更新缓存映射
      updateMaps: () => {
        const { nodes } = get();
        const nodeMap = new Map<string, MindMapNode>();
        const childrenMap = new Map<string, string[]>();
        const parentMap = new Map<string, string>();

        // 构建映射
        nodes.forEach((node) => {
          nodeMap.set(node.id, node);
          if (node.parentId) {
            parentMap.set(node.id, node.parentId);
            if (!childrenMap.has(node.parentId)) {
              childrenMap.set(node.parentId, []);
            }
            childrenMap.get(node.parentId)!.push(node.id);
          }
        });

        set((state) => ({ ...state, nodeMap, childrenMap, parentMap }));
      },

      // 优化：使用广度优先搜索获取所有后代节点
      getDescendants: (nodeId: string) => {
        const { childrenMap } = get();
        const descendants: string[] = [];
        const queue: string[] = [nodeId];

        while (queue.length > 0) {
          const currentId = queue.shift()!;
          const children = childrenMap.get(currentId) || [];

          children.forEach((childId) => {
            descendants.push(childId);
            queue.push(childId);
          });
        }

        return descendants;
      },

      // 优化：获取所有祖先节点
      getAncestors: (nodeId: string) => {
        const { parentMap } = get();
        const ancestors: string[] = [];
        let currentId = nodeId;

        while (parentMap.has(currentId)) {
          const parentId = parentMap.get(currentId)!;
          ancestors.push(parentId);
          currentId = parentId;
        }

        return ancestors;
      },

      // 新增主题
      addChildTheme: (nodeId?: string) => {
        const { selection, nodes, updateMaps } = get();
        const targetNodeId = nodeId || selection.nodes[0];
        if (!targetNodeId) return;

        const selectedNode = nodes.find((n) => n.id === targetNodeId);
        if (!selectedNode || selectedNode.level >= 3) return;

        // 如果是根节点，需要特殊处理左右分布
        if (selectedNode.level === 1) {
          const { pushHistory, calculateLayout, edges } = get();
          pushHistory();

          // 获取当前根节点的所有二级子节点
          const existingLevel2Nodes = nodes.filter(
            (n) => n.level === 2 && n.parentId === selectedNode.id
          );

          // 计算左右两侧的节点数量
          const leftNodes = existingLevel2Nodes.filter((n) => n.x < 400);
          const rightNodes = existingLevel2Nodes.filter((n) => n.x >= 400);

          // 决定新节点放在哪一侧（优先放在节点数量少的一侧）
          const shouldPlaceLeft = leftNodes.length <= rightNodes.length;

          const newNode: MindMapNode = {
            id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            x: shouldPlaceLeft ? 250 : 550, // 临时位置，会被calculateLayout重新计算
            y: 300,
            text: "分支主题",
            level: 2,
            parentId: selectedNode.id,
            width: 100,
            height: 40,
            collapsed: false,
            children: [],
            fontColor: "#000000",
          };

          const newEdge: MindMapEdge = {
            id: `edge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            from: selectedNode.id,
            to: newNode.id,
          };

          // 更新父节点的children列表
          const updatedNodes = nodes.map((n) =>
            n.id === selectedNode.id
              ? { ...n, children: [...(n.children || []), newNode.id] }
              : n
          );

          set((state) => ({
            ...state,
            nodes: [...updatedNodes, newNode],
            edges: [...edges, newEdge],
            selection: { ...state.selection, nodes: [newNode.id] },
          }));

          updateMaps();
          calculateLayout();
        } else {
          // 非根节点的处理保持原有逻辑
          get().addChildNode(targetNodeId);
          updateMaps();
        }
      },

      addSameLevelTheme: (nodeId?: string) => {
        const {
          selection,
          nodes,
          edges,
          pushHistory,
          calculateLayout,
          updateMaps,
        } = get();
        const targetNodeId = nodeId || selection.nodes[0];
        if (!targetNodeId) return;

        const selectedNode = nodes.find((n) => n.id === targetNodeId);
        if (!selectedNode || selectedNode.level === 1) return;

        pushHistory();

        // 计算新节点位置：在同一侧添加
        const newNode: MindMapNode = {
          id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          x: selectedNode.x, // 保持在同一侧的x坐标
          y: selectedNode.y + 80,
          text: selectedNode.level === 2 ? "分支主题" : "子主题",
          level: selectedNode.level,
          parentId: selectedNode.parentId,
          width: selectedNode.width,
          height: selectedNode.height,
          collapsed: false,
          children: [],
          fontColor: selectedNode.level === 2 ? "#000000" : "#000000",
        };

        const newEdge: MindMapEdge = {
          id: `edge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          from: selectedNode.parentId!,
          to: newNode.id,
        };

        // 更新父节点的children列表
        const updatedNodes = nodes.map((n) =>
          n.id === selectedNode.parentId
            ? { ...n, children: [...(n.children || []), newNode.id] }
            : n
        );

        set((state) => ({
          ...state,
          nodes: [...updatedNodes, newNode],
          edges: [...edges, newEdge],
          selection: { ...state.selection, nodes: [newNode.id] },
        }));

        updateMaps();
        calculateLayout();
      },

      addParentTheme: (nodeId?: string) => {
        const {
          selection,
          nodes,
          edges,
          pushHistory,
          calculateLayout,
          updateMaps,
        } = get();
        const targetNodeId = nodeId || selection.nodes[0];
        if (!targetNodeId) return;

        const selectedNode = nodes.find((n) => n.id === targetNodeId);
        if (!selectedNode || selectedNode.level <= 2) return;

        pushHistory();

        const newNode: MindMapNode = {
          id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          x: selectedNode.x - 180,
          y: selectedNode.y,
          text: "分支主题",
          level: selectedNode.level - 1,
          parentId: selectedNode.parentId,
          width: 100,
          height: 40,
          collapsed: false,
          children: [selectedNode.id],
          fontColor: "#000000", // 默认黑色
        };

        const newEdge: MindMapEdge = {
          id: `edge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          from: newNode.id,
          to: selectedNode.id,
        };

        // 更新选中节点的父节点
        const updatedNodes = nodes.map((n) => {
          if (n.id === selectedNode.id) {
            return { ...n, parentId: newNode.id };
          }
          if (n.id === selectedNode.parentId) {
            const newChildren = (n.children || []).filter(
              (id) => id !== selectedNode.id
            );
            newChildren.push(newNode.id);
            return { ...n, children: newChildren };
          }
          return n;
        });

        // 更新边
        const updatedEdges = edges.map((e) =>
          e.to === selectedNode.id ? { ...e, from: newNode.id } : e
        );

        set((state) => ({
          ...state,
          nodes: [...updatedNodes, newNode],
          edges: [...updatedEdges, newEdge],
          selection: { ...state.selection, nodes: [newNode.id] },
        }));

        updateMaps(); // 更新缓存映射
        calculateLayout();
      },

      updateNodeText: (id: string, text: string) => {
        // 验证文本长度 - 确保至少1个字符
        if (
          text.length < EDIT_CONFIG.INPUT.MIN_LENGTH ||
          text.length > EDIT_CONFIG.INPUT.MAX_LENGTH
        ) {
          return; // 不更新无效文本
        }

        const { nodes, pushHistory, calculateLayout, updateMaps } = get();
        const node = nodes.find((n) => n.id === id);
        if (!node) return;

        pushHistory();

        const updatedNodes = nodes.map((n) =>
          n.id === id ? { ...n, text } : n
        );

        set((state) => ({ ...state, nodes: updatedNodes }));
        updateMaps();
        calculateLayout();
      },

      //更新节点字体颜色
      updateNodeFontColor: (nodeId: string, color: string) => {
        const { updateMaps } = get();
        set((state) => ({
          nodes: state.nodes.map((node) =>
            node.id === nodeId ? { ...node, fontColor: color } : node
          ),
        }));
        updateMaps(); // 添加缓存更新
      },

      // 更新节点粗体状态
      updateNodeBold: (id: string) => {
        const { nodes, pushHistory, updateMaps } = get();
        pushHistory();
        const node = nodes.find((n) => n.id === id);
        if (!node) return;

        const updatedNodes = nodes.map((n) =>
          n.id === id ? { ...n, isBold: !n.isBold } : n
        );
        set((state) => ({ ...state, nodes: updatedNodes }));
        updateMaps(); // 添加缓存更新
      },

      // 更新节点斜体状态
      updateNodeItalic: (id: string) => {
        const { nodes, pushHistory, updateMaps } = get();
        pushHistory();
        const node = nodes.find((n) => n.id === id);
        if (!node) return;

        const updatedNodes = nodes.map((n) =>
          n.id === id ? { ...n, isItalic: !n.isItalic } : n
        );
        set((state) => ({ ...state, nodes: updatedNodes }));
        updateMaps(); // 添加缓存更新
      },

      // 更新节点边框宽度
      updateNodeBorderWidth: (id: string, borderWidth: number) => {
        const { nodes, pushHistory, updateMaps } = get();
        pushHistory();
        const node = nodes.find((n) => n.id === id);
        if (!node) return;

        const updatedNodes = nodes.map((n) =>
          n.id === id ? { ...n, borderWidth } : n
        );
        set((state) => ({ ...state, nodes: updatedNodes }));
        updateMaps(); // 添加缓存更新
      },

      // 更新节点尺寸并重新计算布局
      updateNodeSize: (id: string, width: number, height: number) => {
        const { updateMaps } = get();
        set((state) => {
          const updatedNodes = state.nodes.map((n) =>
            n.id === id ? { ...n, width, height } : n
          );
          return { ...state, nodes: updatedNodes };
        });

        updateMaps(); // 添加缓存更新

        // 立即重新计算布局
        const { calculateLayout } = get();
        requestAnimationFrame(() => {
          calculateLayout();
        });
      },

      // 新增：设置正在编辑的节点
      setEditingNode: (nodeId: string | null) => {
        set((state) => ({ ...state, editingNodeId: nodeId }));
      },

      // 计算整体布局
      calculateLayout: () => {
        const { nodes, edges } = get();
        const rootNode = nodes.find((n) => n.level === 1);
        if (!rootNode) return;

        const newNodes = [...nodes];
        const newEdges = [...edges];

        // 获取二级节点
        const level2Nodes = nodes.filter(
          (n) => n.level === 2 && n.parentId === rootNode.id
        );

        if (level2Nodes.length === 0) {
          // 只有根节点
          const rootIndex = newNodes.findIndex((n) => n.id === rootNode.id);
          newNodes[rootIndex] = { ...rootNode, x: 400, y: 300 };
          set((state) => ({ ...state, nodes: newNodes, edges: newEdges }));
          return;
        }

        // 根据节点当前位置判断左右侧归属
        const leftNodes: any[] = [];
        const rightNodes: any[] = [];

        level2Nodes.forEach((node) => {
          const visibleChildNodes = nodes.filter(
            (n) => n.parentId === node.id && !node.collapsed
          );
          const allChildNodes = nodes.filter((n) => n.parentId === node.id);

          const nodeWithChildren = {
            ...node,
            childNodes: visibleChildNodes,
            allChildNodes: allChildNodes,
          };

          // 根据节点当前x坐标判断归属（以400为中心线）
          if (node.x < 400) {
            leftNodes.push(nodeWithChildren);
          } else {
            rightNodes.push(nodeWithChildren);
          }
        });

        // 计算每一侧节点需要的总高度
        const calculateSideHeight = (sideNodes: any[]) => {
          let totalHeight = 0;
          sideNodes.forEach((node, index) => {
            const childCount = node.childNodes.length;
            const nodeHeight = Math.max(60, childCount * 60);
            totalHeight += nodeHeight;
            if (index < sideNodes.length - 1) {
              totalHeight += 40; // 节点间距
            }
          });
          return totalHeight;
        };

        const leftHeight = calculateSideHeight(leftNodes);
        const rightHeight = calculateSideHeight(rightNodes);
        const maxHeight = Math.max(leftHeight, rightHeight);

        // 根节点位于画布中心，考虑总高度
        const canvasHeight = Math.max(600, maxHeight + 400);
        const rootY = Math.max(200, canvasHeight / 2);

        // 更新根节点位置
        const rootIndex = newNodes.findIndex((n) => n.id === rootNode.id);
        newNodes[rootIndex] = { ...rootNode, x: 400, y: rootY };

        // 布局左右两侧节点
        const layoutSide = (sideNodes: any[], isLeft: boolean) => {
          const sideHeight = calculateSideHeight(sideNodes);
          let currentY = rootY - sideHeight / 2;

          sideNodes.forEach((node) => {
            const childCount = node.childNodes.length;
            const nodeHeight = Math.max(60, childCount * 60);
            const nodeY = currentY + nodeHeight / 2;

            const nodeIndex = newNodes.findIndex((n) => n.id === node.id);
            const rootWidth = rootNode.width || 120;
            const nodeWidth = node.width || 100;
            const baseOffset = (rootWidth + nodeWidth) / 2 + 50;
            const xOffset = isLeft ? -baseOffset : baseOffset;

            newNodes[nodeIndex] = {
              ...node,
              x: 400 + xOffset,
              y: nodeY,
            };

            // 布局子节点
            if (node.allChildNodes && node.allChildNodes.length > 0) {
              const parentWidth = node.width || 100;
              const childBaseOffset = parentWidth / 2 + 40;
              const childXOffset = isLeft ? -childBaseOffset : childBaseOffset;

              node.allChildNodes.forEach((child: any, childIndex: number) => {
                const childNodeIndex = newNodes.findIndex(
                  (n) => n.id === child.id
                );
                if (childNodeIndex !== -1) {
                  const childWidth = child.width || 80;
                  const finalChildXOffset = isLeft
                    ? childXOffset - childWidth / 2
                    : childXOffset + childWidth / 2;

                  newNodes[childNodeIndex] = {
                    ...child,
                    x: newNodes[nodeIndex].x + finalChildXOffset,
                    y:
                      nodeY +
                      (childIndex - (node.allChildNodes.length - 1) / 2) * 60,
                  };
                }
              });
            }

            currentY += nodeHeight + 40;
          });
        };

        layoutSide(leftNodes, true);
        layoutSide(rightNodes, false);

        // 更新边
        const updatedEdges = newEdges.map((edge) => {
          const fromNode = newNodes.find((n) => n.id === edge.from);
          const toNode = newNodes.find((n) => n.id === edge.to);

          if (!fromNode || !toNode) return edge;

          const isLeftSide = toNode.x < 400;
          const fromNodeWidth = fromNode.width || 100;
          const toNodeWidth = toNode.width || 100;

          const sourceX = isLeftSide
            ? fromNode.x - fromNodeWidth / 2
            : fromNode.x + fromNodeWidth / 2;
          const targetX = isLeftSide
            ? toNode.x + toNodeWidth / 2
            : toNode.x - toNodeWidth / 2;

          const bendPoint = {
            x: (sourceX + targetX) / 2,
            y: fromNode.y,
          };

          return {
            ...edge,
            bendPoint,
            style: "polyline",
          };
        });

        set((state) => ({
          ...state,
          nodes: newNodes,
          edges: updatedEdges,
        }));
      },

      // 添加子节点
      addChildNode: (parentId: string) => {
        const { nodes, edges, pushHistory, calculateLayout, updateMaps } =
          get();
        const parent = nodes.find((n) => n.id === parentId);
        if (!parent) return;

        pushHistory();

        const calculateNodeWidth = (text: string, level: number) => {
          const baseWidth = level === 1 ? 120 : level === 2 ? 100 : 80;
          const charWidth = level === 1 ? 12 : level === 2 ? 10 : 8;
          return Math.max(baseWidth, text.length * charWidth + 32);
        };

        const newNodeText = parent.level === 1 ? "分支主题" : "子主题";
        const newNodeLevel = parent.level + 1;
        const newNodeWidth = calculateNodeWidth(newNodeText, newNodeLevel);

        const newNode: MindMapNode = {
          id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          x: parent.x,
          y: parent.y,
          text: newNodeText,
          level: newNodeLevel,
          parentId: parentId,
          width: newNodeWidth,
          height: newNodeLevel === 2 ? 40 : 35,
          collapsed: false,
          children: [],
          fontColor: parent.level === 1 ? "#000000" : "#000000",
        };

        const newEdge: MindMapEdge = {
          id: `edge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          from: parentId,
          to: newNode.id,
        };

        // 如果父节点是收起状态，自动展开
        const updatedNodes = nodes.map((n) => {
          if (n.id === parentId) {
            return {
              ...n,
              children: [...(n.children || []), newNode.id],
              collapsed: false, // 自动展开父节点
            };
          }
          return n;
        });

        set((state) => ({
          ...state,
          nodes: [...updatedNodes, newNode],
          edges: [...edges, newEdge],
          selection: { ...state.selection, nodes: [newNode.id] },
        }));

        updateMaps();
        calculateLayout();
      },

      // 收起/展开操作
      toggleNodeCollapse: (nodeId: string) => {
        const { nodeMap, getDescendants, updateMaps, calculateLayout } = get();
        const targetNode = nodeMap.get(nodeId);
        if (!targetNode) return;

        set((state) => {
          const nodes = [...state.nodes];
          const targetNodeIndex = nodes.findIndex((n) => n.id === nodeId);
          if (targetNodeIndex === -1) return state;

          // 切换目标节点的收起状态
          nodes[targetNodeIndex] = {
            ...targetNode,
            collapsed: !targetNode.collapsed,
          };

          if (nodes[targetNodeIndex].collapsed) {
            // 收起：将所有后代节点设为收起状态
            const descendants = getDescendants(nodeId);
            descendants.forEach((descendantId) => {
              const index = nodes.findIndex((n) => n.id === descendantId);
              if (index !== -1) {
                nodes[index] = { ...nodes[index], collapsed: true };
              }
            });
          } else {
            // 展开：只展开直接子节点
            const directChildren = state.childrenMap.get(nodeId) || [];
            directChildren.forEach((childId) => {
              const index = nodes.findIndex((n) => n.id === childId);
              if (index !== -1) {
                nodes[index] = { ...nodes[index], collapsed: false };
              }
            });
          }

          return { ...state, nodes };
        });

        // 更新缓存映射
        updateMaps();
        // 添加重新计算布局
        calculateLayout();
      },

      // 基础操作
      moveNode: (id, x, y) => {
        const { nodes } = get();
        const updatedNodes = nodes.map((n) =>
          n.id === id ? { ...n, x, y } : n
        );
        set((state) => ({ ...state, nodes: updatedNodes }));
      },

      // 删除节点函数（删除节点本身+所有子孙节点）
      deleteNodeWithDescendants: (
        nodeId: string,
        skipHistory: boolean = false
      ) => {
        const {
          nodes,
          edges,
          pushHistory,
          getDescendants,
          updateMaps,
          calculateLayout,
        } = get();

        const nodeToDelete = nodes.find((n) => n.id === nodeId);
        if (nodeToDelete && nodeToDelete.level === 1) {
          console.warn("不允许删除1级节点（根节点）");
          return;
        }

        if (!skipHistory) {
          pushHistory();
        }

        // 删除节点本身 + 所有子孙节点
        const descendants = getDescendants(nodeId);
        const nodesToDelete = new Set([nodeId, ...descendants]);

        const updatedNodes = nodes.filter((n) => !nodesToDelete.has(n.id));
        const updatedEdges = edges.filter(
          (e) => !nodesToDelete.has(e.from) && !nodesToDelete.has(e.to)
        );

        set((state) => ({
          ...state,
          nodes: updatedNodes,
          edges: updatedEdges,
        }));

        updateMaps();
        calculateLayout();
      },

      // 清空子节点函数（只删除子节点，保留父节点）
      clearChildNodes: (parentId: string) => {
        const { nodes, edges, getDescendants, updateMaps, pushHistory } = get();

        pushHistory();

        const childNodes = nodes.filter((n) => n.parentId === parentId);
        if (childNodes.length === 0) return;

        // 只删除子节点 + 子孙节点，不删除父节点本身
        const nodesToDelete = new Set<string>();
        childNodes.forEach((child) => {
          nodesToDelete.add(child.id);
          const descendants = getDescendants(child.id);
          descendants.forEach((id) => nodesToDelete.add(id));
        });

        const updatedNodes = nodes.filter((n) => !nodesToDelete.has(n.id));
        const updatedEdges = edges.filter(
          (e) => !nodesToDelete.has(e.from) && !nodesToDelete.has(e.to)
        );

        // 更新父节点的children列表为空
        const finalNodes = updatedNodes.map((n) =>
          n.id === parentId ? { ...n, children: [] } : n
        );

        set((state) => ({
          ...state,
          nodes: finalNodes,
          edges: updatedEdges,
        }));

        updateMaps();
      },

      removeNode: (id) => {
        get().deleteNodeWithDescendants(id, false);
      },

      removeEdge: (id) => {
        const { edges, pushHistory, updateMaps } = get();
        pushHistory();
        const updatedEdges = edges.filter((e) => e.id !== id);
        set((state) => ({ ...state, edges: updatedEdges }));
        updateMaps(); // 添加缓存更新
      },

      setSelection: (selection) => set((state) => ({ ...state, selection })),

      setViewport: (viewport) => set((state) => ({ ...state, viewport })),

      fitView: () => {
        const { nodes } = get();
        if (nodes.length === 0) return;

        // 计算所有节点的边界，考虑节点尺寸
        let minX = Infinity,
          minY = Infinity,
          maxX = -Infinity,
          maxY = -Infinity;
        nodes.forEach((n) => {
          const nodeWidth = n.width || 120;
          const nodeHeight = n.height || 50;
          minX = Math.min(minX, n.x);
          minY = Math.min(minY, n.y);
          maxX = Math.max(maxX, n.x + nodeWidth);
          maxY = Math.max(maxY, n.y + nodeHeight);
        });

        // 获取画布容器尺寸
        const canvasContainer = document.querySelector(
          ".w-full.h-full.bg-gray-50"
        ) as HTMLElement;
        if (!canvasContainer) return;

        const containerWidth = canvasContainer.clientWidth;
        const containerHeight = canvasContainer.clientHeight;

        // 计算内容尺寸
        const contentWidth = maxX - minX;
        const contentHeight = maxY - minY;

        // 计算合适的缩放比例，确保所有内容都能显示
        const padding = 130; // 增加 padding
        const scaleX = (containerWidth - padding * 2) / contentWidth;
        const scaleY = (containerHeight - padding * 2) / contentHeight;
        const zoom = Math.min(1, Math.min(scaleX, scaleY));

        // 计算居中位置，增加顶部偏移
        const centerX = (containerWidth - contentWidth * zoom) / 2;
        const centerY = (containerHeight - contentHeight * zoom) / 2 + 30; // 向下偏移50px

        const newViewport = {
          zoom,
          x: centerX - minX * zoom,
          y: centerY - minY * zoom,
        };

        set((state) => ({ ...state, viewport: newViewport }));
      },

      clearSelection: () =>
        set((state) => ({
          ...state,
          selection: { nodes: [], edges: [] },
        })),

      selectNode: (id) =>
        set((state) => ({
          ...state,
          selection: { ...state.selection, nodes: [id] },
        })),

      pushHistory: () => {
        const { nodes, edges, history } = get();
        const newHistory = [...history, { nodes, edges }];
        if (newHistory.length > HISTORY_CONFIG.MAX_HISTORY_SIZE) {
          newHistory.shift();
        }
        set((state) => ({ ...state, history: newHistory, future: [] }));
      },

      undo: () => {
        const { history, nodes, edges } = get();
        if (history.length === 0) return;

        const previous = history[history.length - 1];
        set((state) => ({
          ...state,
          nodes: previous.nodes,
          edges: previous.edges,
          history: history.slice(0, -1),
          future: [{ nodes, edges }, ...state.future],
        }));
      },

      redo: () => {
        const { future, nodes, edges } = get();
        if (future.length === 0) return;

        const next = future[0];
        set((state) => ({
          ...state,
          nodes: next.nodes,
          edges: next.edges,
          history: [...state.history, { nodes, edges }],
          future: future.slice(1),
        }));
      },

      // 停止AI生成
      stopAIGeneration: () => {
        const { abortController } = get();

        // 取消网络请求
        if (abortController) {
          abortController.abort();
        }

        set((state) => ({
          ...state,
          isAIProcessing: false,
          abortController: null,
          aiProgress:
            state.pendingAINodes.length > 0
              ? `AI创作已停止！已生成${state.pendingAINodes.length}个节点`
              : "AI创作已停止",
        }));
        get().calculateLayout();
      },

      // 确认使用AI生成的内容
      confirmAIGeneration: () => {
        set((state) => ({
          ...state,
          pendingAINodes: [], // 清空待确认列表
          isAIProcessing: false,
          aiProgress: "",
        }));
        get().calculateLayout();
      },

      // 丢弃AI生成的内容
      discardAIGeneration: () => {
        const { pendingAINodes } = get();

        pendingAINodes.forEach((nodeId) => {
          get().deleteNodeWithDescendants(nodeId, true); // skipHistory = true
        });

        set((state) => ({
          ...state,
          pendingAINodes: [],
          isAIProcessing: false,
          aiProgress: "",
        }));
      },

      // 添加到待确认列表
      addToPendingNodes: (nodeId: string) => {
        set((state) => ({
          ...state,
          pendingAINodes: [...state.pendingAINodes, nodeId],
        }));
      },

      generateChildrenFromAI: async (nodeId: string) => {
        const {
          nodes,
          pushHistory,
          calculateLayout,
          addChildNode,
          addToPendingNodes,
        } = get();
        const targetNode = nodes.find((n) => n.id === nodeId);
        if (!targetNode) return;

        // 创建新的AbortController
        const abortController = new AbortController();

        set((state) => ({
          ...state,
          isAIProcessing: true,
          aiProgress: "正在连接AI服务...",
          abortController,
        }));

        pushHistory();

        // 如果是根节点且有子节点，先清空所有子节点
        if (targetNode.level === 1) {
          get().clearChildNodes(nodeId);
        }

        try {
          const response = await fetch(
            "http://localhost:3001/api/generate-mindmap",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                mainNode: targetNode.text,
              }),
              signal: abortController.signal,
            }
          );

          // 检查请求是否被取消
          if (abortController.signal.aborted) {
            return;
          }

          const result = await response.json();

          if (result.code === 0 && result.data) {
            const mindMapData = result.data;
            const newNodes = mindMapData.filter((node: any) => node.level > 1);

            if (newNodes.length === 0) {
              set((state) => ({
                ...state,
                aiProgress: "未生成有效的子节点数据",
                isAIProcessing: false,
              }));
              return;
            }

            set((state) => ({
              ...state,
              aiProgress: "正在创建节点...",
            }));

            const nodeIdMap = new Map();
            // 处理二级节点
            const level2Nodes = newNodes.filter(
              (node: any) => node.level === 2
            );
            level2Nodes.forEach((nodeData: any) => {
              const originalNodes = get().nodes;
              addChildNode(targetNode.id); // 同步调用
              const currentNodes = get().nodes;
              const addedNode = currentNodes.find(
                (n) => !originalNodes.some((on) => on.id === n.id)
              );
              if (addedNode) {
                nodeIdMap.set(nodeData.id, addedNode.id);

                addToPendingNodes(addedNode.id);
                // 更新节点文本
                set((state) => ({
                  ...state,
                  nodes: state.nodes.map((n) =>
                    n.id === addedNode.id ? { ...n, text: nodeData.text } : n
                  ),
                }));
              }
            });
            // 处理三级节点
            const level3Nodes = newNodes.filter(
              (node: any) => node.level === 3
            );
            level3Nodes.forEach((nodeData: any) => {
              const parentId = nodeIdMap.get(nodeData.parentId);
              if (parentId) {
                const originalNodes = get().nodes;
                addChildNode(parentId); // 同步调用
                const currentNodes = get().nodes;
                const addedNode = currentNodes.find(
                  (n) => !originalNodes.some((on) => on.id === n.id)
                );

                if (addedNode) {
                  addToPendingNodes(addedNode.id); // 添加到待确认列表
                  const newWidth = Math.max(
                    100,
                    nodeData.text.length * 14 + 40
                  );
                  set((state) => ({
                    ...state,
                    nodes: state.nodes.map((n) =>
                      n.id === addedNode.id
                        ? { ...n, text: nodeData.text, width: newWidth }
                        : n
                    ),
                  }));
                }
              }
            });

            set((state) => ({
              ...state,
              isAIProcessing: false,
              aiProgress: `AI创作完成！生成了${newNodes.length}个节点`,
            }));
            calculateLayout();
          } else {
            set((state) => ({
              ...state,
              aiProgress: result.message || "创作失败",
              isAIProcessing: false,
              abortController: null,
            }));
          }
        } catch (error) {
          if (error instanceof Error && error.name === "AbortError") {
            console.log("AI请求被取消");
            return;
          }
          set((state) => ({
            ...state,
            aiProgress: `生成失败`,
            isAIProcessing: false,
            abortController: null,
          }));
        }
      },

      generateChildrenFromAIStream: async (nodeId: string) => {
        const { nodes, pushHistory, calculateLayout, addToPendingNodes } =
          get();
        const targetNode = nodes.find((n) => n.id === nodeId);
        if (!targetNode) return;

        // 创建新的AbortController
        const abortController = new AbortController();

        set((state) => ({
          ...state,
          isAIProcessing: true,
          aiProgress: "正在连接AI服务...",
          abortController,
        }));

        pushHistory();

        if (targetNode.level === 1) {
          get().clearChildNodes(nodeId);
        }

        try {
          const response = await fetch(
            "http://localhost:3001/api/generate-mindmap-stream",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                mainNode: targetNode.text,
              }),
              signal: abortController.signal,
            }
          );

          if (!response.body) {
            throw new Error("No response body");
          }

          const reader = response.body.getReader();
          const decoder = new TextDecoder();
          let buffer = "";
          let nodeCount = 0;
          const nodeIdMap = new Map();

          while (true) {
            // 检查是否需要停止
            const currentState = get();
            if (
              !currentState.isAIProcessing ||
              abortController.signal.aborted
            ) {
              reader.cancel();
              break;
            }

            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split("\n");
            buffer = lines.pop() || "";

            for (const line of lines) {
              if (line.trim() === "") continue;

              if (line.startsWith("event: ")) {
                const event = line.substring(7);
                if (event === "end") {
                  set((state) => ({
                    ...state,
                    isAIProcessing: false,
                    abortController: null,
                    aiProgress: `流式AI创作完成！生成了${nodeCount}个节点`,
                  }));
                  calculateLayout();
                }
                continue;
              }

              if (line.startsWith("data: ")) {
                const data = line.substring(6);
                try {
                  const parsed = JSON.parse(data);

                  if (parsed.message) {
                    set((state) => ({
                      ...state,
                      aiProgress: parsed.message,
                    }));
                  }

                  if (parsed.node) {
                    const nodeData = parsed.node;
                    nodeCount++;

                    set((state) => ({
                      ...state,
                      aiProgress: `正在生成节点: ${nodeData.text}`,
                    }));

                    if (nodeData.level === 2) {
                      // 二级节点：实现左右分布
                      const currentNodes = get().nodes;
                      const currentEdges = get().edges;

                      // 获取当前根节点的所有二级子节点
                      const existingLevel2Nodes = currentNodes.filter(
                        (n) => n.level === 2 && n.parentId === targetNode.id
                      );

                      // 计算左右两侧的节点数量
                      const leftNodes = existingLevel2Nodes.filter(
                        (n) => n.x < 400
                      );
                      const rightNodes = existingLevel2Nodes.filter(
                        (n) => n.x >= 400
                      );

                      // 决定新节点放在哪一侧
                      const shouldPlaceLeft =
                        leftNodes.length <= rightNodes.length;

                      const newNodeId = `node-${Date.now()}-${Math.random()
                        .toString(36)
                        .substr(2, 9)}`;
                      const newWidth = Math.max(
                        100,
                        nodeData.text.length * 14 + 40
                      );

                      const newNode: MindMapNode = {
                        id: newNodeId,
                        x: shouldPlaceLeft ? 250 : 550, // 临时位置
                        y: 300,
                        text: nodeData.text,
                        level: 2,
                        parentId: targetNode.id,
                        width: newWidth,
                        height: 40,
                        collapsed: false,
                        children: [],
                        fontColor: "#000000",
                      };

                      const newEdge: MindMapEdge = {
                        id: `edge-${Date.now()}-${Math.random()
                          .toString(36)
                          .substr(2, 9)}`,
                        from: targetNode.id,
                        to: newNodeId,
                      };

                      // 更新父节点的children列表
                      const updatedNodes = currentNodes.map((n) =>
                        n.id === targetNode.id
                          ? {
                              ...n,
                              children: [...(n.children || []), newNodeId],
                            }
                          : n
                      );

                      nodeIdMap.set(nodeData.id, newNodeId);
                      addToPendingNodes(newNodeId);

                      set((state) => ({
                        ...state,
                        nodes: [...updatedNodes, newNode],
                        edges: [...currentEdges, newEdge],
                      }));
                    } else if (nodeData.level === 3) {
                      // 三级节点：使用原有逻辑
                      const parentId = nodeIdMap.get(nodeData.parentId);
                      if (parentId) {
                        const originalNodes = get().nodes;
                        get().addChildNode(parentId);
                        const currentNodes = get().nodes;
                        const addedNode = currentNodes.find(
                          (n) => !originalNodes.some((on) => on.id === n.id)
                        );

                        if (addedNode) {
                          addToPendingNodes(addedNode.id);
                          const newWidth = Math.max(
                            100,
                            nodeData.text.length * 14 + 40
                          );
                          set((state) => ({
                            ...state,
                            nodes: state.nodes.map((n) =>
                              n.id === addedNode.id
                                ? { ...n, text: nodeData.text, width: newWidth }
                                : n
                            ),
                          }));
                        }
                      }
                    }
                  }
                } catch (parseError) {
                  console.error("解析数据失败:", parseError);
                }
              }
            }
          }
        } catch (error) {
          if (error instanceof Error && error.name === "AbortError") {
            console.log("流式AI请求被取消");
            return;
          }
          console.error("流式AI创作失败:", error);
          set((state) => ({
            ...state,
            isAIProcessing: false,
            abortController: null,
            aiProgress: "流式AI创作失败",
          }));
        }
      },

      // 优化后的可见性检查
      isNodeVisible: (nodeId: string) => {
        const { nodeMap, getAncestors } = get();
        const node = nodeMap.get(nodeId);
        if (!node || node.level === 1) return true;

        // 检查所有祖先节点是否被收起
        const ancestors = getAncestors(nodeId);
        return !ancestors.some((ancestorId) => {
          const ancestor = nodeMap.get(ancestorId);
          return ancestor?.collapsed;
        });
      },
    }),
    {
      name: "mindmap-storage",
      partialize: (state) => ({
        nodes: state.nodes,
        edges: state.edges,
        viewport: state.viewport,
      }),
      onRehydrateStorage: () => (state) => {
        // 页面刷新后恢复数据时的回调
        if (state) {
          // 更新缓存映射
          state.updateMaps();
          // 重新计算布局
          setTimeout(() => {
            state.calculateLayout();
          }, 0);
        }
      },
    }
  )
);
