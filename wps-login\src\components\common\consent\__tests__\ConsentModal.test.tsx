import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import ConsentModal from "../ConsentModal";

describe("隐私协议弹窗组件 (ConsentModal)", () => {
  const mockProps = {
    isOpen: true,
    onClose: vi.fn(),
    onAgree: vi.fn(),
    onCancel: vi.fn(),
  };

  it("当 isOpen 为 true 时，弹窗应该可见", () => {
    render(<ConsentModal {...mockProps} />);
    expect(screen.getByText("提示")).toBeInTheDocument();
    expect(screen.getByText(/隐私政策/)).toBeInTheDocument();
  });

  it("当 isOpen 为 false 时，弹窗应该不可见", () => {
    render(<ConsentModal {...mockProps} isOpen={false} />);
    expect(screen.queryByText("提示")).not.toBeInTheDocument();
  });

  it("点击同意按钮时，应该触发 onAgree 回调", () => {
    render(<ConsentModal {...mockProps} />);
    fireEvent.click(screen.getByRole("button", { name: "同意" }));
    expect(mockProps.onAgree).toHaveBeenCalledTimes(1);
  });

  it("点击“取消”按钮时，应该触发 onCancel 回调", () => {
    render(<ConsentModal {...mockProps} />);
    fireEvent.click(screen.getByRole("button", { name: "取消" }));
    expect(mockProps.onCancel).toHaveBeenCalledTimes(1);
  });

  it("弹窗内容应该包含隐私政策和服务协议链接", () => {
    render(<ConsentModal {...mockProps} />);

    const privacyLink = screen.getByText("隐私政策");
    const serviceLink = screen.getByText("在线服务协议");

    expect(privacyLink.tagName.toLowerCase()).toBe("a");
    expect(serviceLink.tagName.toLowerCase()).toBe("a");
  });
});
