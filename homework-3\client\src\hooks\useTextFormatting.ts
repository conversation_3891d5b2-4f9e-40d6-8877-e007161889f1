import { useMindMapStore } from "../store/index";

export const useTextFormatting = () => {
  const updateNodeFontColor = useMindMapStore((s) => s.updateNodeFontColor);
  const updateNodeBold = useMindMapStore((s) => s.updateNodeBold);
  const updateNodeItalic = useMindMapStore((s) => s.updateNodeItalic);
  const nodes = useMindMapStore((s) => s.nodes);
  const selection = useMindMapStore((s) => s.selection);

  const selectedNode = nodes.find((n) => n.id === selection.nodes[0]);
  const hasSelection = selection.nodes.length > 0;

  const handleFormatText = (format: string, value?: any) => {
    if (!hasSelection || !selectedNode) return;

    switch (format) {
      case "bold":
        updateNodeBold(selectedNode.id);
        break;
      case "italic":
        updateNodeItalic(selectedNode.id);
        break;
      case "fontColor":
        updateNodeFontColor(selectedNode.id, value);
        break;
      default:
        break;
    }
  };

  return {
    handleFormatText,
    selectedNode,
    hasSelection,
  };
};
