// 单例模式
// static 让属性/方法属于类而不是实例，是单例模式实现的关键
// 因为它能全局共享唯一数据并且不需要实例化就能访问。
// SingletonStore.getInstance(); // ✅
// new SingletonStore().getInstance(); // ❌ 会报错，因为实例没有这个方法

import { ref } from 'vue'

class SingletonStore {
  private static instance: SingletonStore
  public counter = ref(0)

  private constructor() {}

  public static getInstance(): SingletonStore {
    if (!SingletonStore.instance) {
      SingletonStore.instance = new SingletonStore()
    }
    return SingletonStore.instance
  }

  public increment() {
    this.counter.value++
  }

  public decrement() {
    this.counter.value--
  }
}

export default SingletonStore
