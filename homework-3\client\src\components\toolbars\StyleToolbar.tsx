import { useRef, useState, useEffect } from "react";
import { ChevronRightIcon, ChevronLeftIcon } from "@radix-ui/react-icons";
import Tooltip from "../ui/Tooltip";
import CustomIcons from "../icons/SvgIcons";
import { ChevronDownIcon } from "@radix-ui/react-icons";
import * as Toast from "@radix-ui/react-toast";
import { useMindMapStore } from "../../store/index";
import BorderWidthDropdown from "../ui/BorderWidthDropdown";

const StyleToolbar = () => {
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [translateX, setTranslateX] = useState(0);
  const [showExpand, setShowExpand] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [showToast, setShowToast] = useState(false);

  // 获取选中状态
  const selection = useMindMapStore((s) => s.selection);
  const hasSelection = selection.nodes.length > 0;

  // 显示提示
  const showSelectionToast = () => {
    setShowToast(true);
    // 3秒后自动关闭
    setTimeout(() => {
      setShowToast(false);
    }, 3000);
  };

  // 检查是否需要显示"展开"按钮
  useEffect(() => {
    const checkOverflow = () => {
      const el = toolbarRef.current;
      if (el) {
        setShowExpand(el.scrollWidth > el.clientWidth);
        // 如果收起时内容本来就不溢出，自动回到最左
        if (el.scrollWidth <= el.clientWidth) {
          setTranslateX(0);
          setExpanded(false);
        }
      }
    };
    checkOverflow();
    window.addEventListener("resize", checkOverflow);
    return () => window.removeEventListener("resize", checkOverflow);
  }, []);

  // 展开
  const handleExpand = () => {
    const el = toolbarRef.current;
    if (el) {
      const rightFixedWidth = 38;
      const maxScroll = el.scrollWidth - el.clientWidth + rightFixedWidth;
      setTranslateX(-maxScroll);
      setExpanded(true);
    }
  };

  // 收起
  const handleCollapse = () => {
    setTranslateX(0);
    setExpanded(false);
  };

  const toolbarButtons = [
    {
      key: "node-style",
      tooltip: "选择节点样式模板",
      icon: (
        <CustomIcons
          type="node-style"
          className="w-4 h-4 text-gray-600"
          disabled={!hasSelection}
        />
      ),
      label: "节点样式",
      hasDropdown: true,
      withDivider: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("节点样式功能");
            // 实现节点样式逻辑
          }
        : showSelectionToast,
    },
    {
      key: "shape",
      tooltip: "形状选择",
      icon: (
        <CustomIcons
          type="shape"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "形状",
      hasDropdown: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("形状选择功能");
            // 实现形状选择逻辑
          }
        : showSelectionToast,
    },
    {
      key: "node-bg",
      tooltip: "设置节点背景颜色",
      icon: (
        <CustomIcons
          type="node-bg"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "节点背景",
      hasDropdown: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("节点背景功能");
            // 实现节点背景逻辑
          }
        : showSelectionToast,
    },
    {
      key: "line-type",
      tooltip: "连线类型",
      icon: (
        <CustomIcons
          type="line-type"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "连线类型",
      hasDropdown: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("连线类型功能");
            // 实现连线类型逻辑
          }
        : showSelectionToast,
    },
    {
      key: "line-color",
      tooltip: "设置连线颜色",
      icon: (
        <CustomIcons
          type="line-color"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "连线颜色",
      hasDropdown: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("连线颜色功能");
            // 实现连线颜色逻辑
          }
        : showSelectionToast,
    },
    {
      key: "line-width",
      tooltip: "设置连线粗细",
      icon: (
        <CustomIcons
          type="line-width"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "连线宽度",
      hasDropdown: true,
      withDivider: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("连线宽度功能");
            // 实现连线宽度逻辑
          }
        : showSelectionToast,
    },
    {
      key: "border-width",
      tooltip: "设置节点边框宽度",
      content: <BorderWidthDropdown disabled={!hasSelection} />,
      withDivider: false,
    },
    {
      key: "border-color",
      tooltip: "设置边框颜色",
      icon: (
        <CustomIcons
          type="border-color"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "边框颜色",
      hasDropdown: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("边框颜色功能");
            // 实现边框颜色逻辑
          }
        : showSelectionToast,
    },
    {
      key: "border-type",
      tooltip: "设置边距类型",
      icon: (
        <CustomIcons
          type="border-type"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "边框类型",
      hasDropdown: true,
      withDivider: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("边框类型功能");
            // 实现边框类型逻辑
          }
        : showSelectionToast,
    },
    {
      key: "clear-style",
      tooltip: "清除所有样式设置",
      icon: (
        <CustomIcons
          type="clear-style"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "清除样式",
      hasDropdown: false,
      withDivider: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("清除样式功能");
            // 实现清除样式逻辑
          }
        : showSelectionToast,
    },
    {
      key: "canvas-color",
      tooltip: "画布设置",
      icon: <CustomIcons type="canvas-color" className="w-4 h-4" />,
      label: "画布",
      hasDropdown: true,
      disabled: false,
      onClick: () => {
        console.log("画布设置功能");
        // 实现画布设置逻辑
      },
    },
    {
      key: "style",
      tooltip: "风格设置",
      icon: <CustomIcons type="style" className="w-4 h-4" />,
      label: "风格",
      hasDropdown: true,
      disabled: false,
      onClick: () => {
        console.log("风格设置功能");
        // 实现风格设置逻辑
      },
    },
    {
      key: "structure",
      tooltip: "思维导图结构设置",
      icon: <CustomIcons type="structure" className="w-4 h-4" />,
      label: "结构",
      hasDropdown: true,
      withDivider: true,
      disabled: false,
      onClick: () => {
        console.log("结构设置功能");
        // 实现结构设置逻辑
      },
    },
    {
      key: "theme-spacing",
      tooltip: "主题间距设置",
      icon: <CustomIcons type="theme-spacing" className="w-4 h-4" />,
      label: "主题间距",
      hasDropdown: true,
      onClick: () => {
        console.log("主题间距功能");
        // 实现主题间距逻辑
      },
    },
    {
      key: "theme-width",
      tooltip: "主题宽度设置",
      icon: (
        <CustomIcons
          type="theme-width"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "主题宽度",
      hasDropdown: true,
      disabled: !hasSelection,
      onClick: () => {
        console.log("主题宽度功能");
        // 实现主题宽度逻辑
      },
    },
  ];

  return (
    <Toast.Provider>
      <div className="px-2 py-1 relative w-full">
        <div className="flex items-center relative overflow-x-hidden w-full">
          {/* 左侧收起按钮 */}
          {expanded && (
            <button
              className="absolute left-0 z-10 h-full flex items-center px-1 bg-white"
              onClick={handleCollapse}
              style={{ boxShadow: "2px 0 4px -2px #0001" }}
            >
              <ChevronLeftIcon className="w-5 h-5" />
            </button>
          )}
          {/* 工具栏按钮区 */}
          <div
            ref={toolbarRef}
            className="flex flex-nowrap transition-transform duration-300 gap-x-1 w-full"
            style={{
              transform: `translateX(${translateX}px)`,
              marginLeft: expanded ? 20 : 0, // 给左侧按钮留空间
              marginRight: showExpand || expanded ? 20 : 0,
            }}
          >
            {toolbarButtons.map((btn) => (
              <div
                className={`flex items-center space-x-1 mr-2 bg-#f9fafb ${
                  btn.withDivider ? "border-r border-gray-200 pr-2" : ""
                }`}
                key={btn.key}
              >
                <Tooltip content={btn.tooltip}>
                  {btn.content ? (
                    btn.content
                  ) : (
                    <button
                      className={`flex items-center space-x-1 px-2 py-1 text-sm rounded transition-colors ${
                        btn.disabled
                          ? "text-gray-400 bg-gray-50"
                          : "text-gray-700 hover:bg-gray-100 cursor-pointer"
                      }`}
                      onClick={btn.onClick}
                    >
                      {btn.icon}
                      <span className="whitespace-nowrap">{btn.label}</span>
                      {btn.hasDropdown && (
                        <ChevronDownIcon className="w-3 h-3" />
                      )}
                    </button>
                  )}
                </Tooltip>
              </div>
            ))}
          </div>
          {/* 右侧展开按钮 */}
          {showExpand && !expanded && (
            <button
              className="absolute right-10 z-10 h-full flex items-center px-1 bg-white cursor-pointer"
              onClick={handleExpand}
              style={{ boxShadow: "-2px 0 4px -2px #0001" }}
            >
              <ChevronRightIcon className="w-5 h-5" />
            </button>
          )}
          {/* 右侧帮助与反馈按钮 */}

          <Tooltip content="帮助与反馈">
            <button
              className="absolute right-0 z-20 h-full flex items-center px-2 bg-white cursor-pointer"
              style={{ minWidth: 40 }}
            >
              <CustomIcons type="help" className="w-4 h-4 mr-1" />
            </button>
          </Tooltip>
        </div>
      </div>

      <Toast.Root
        className="bg-white rounded-lg shadow-lg border border-gray-200 p-3"
        open={showToast}
        onOpenChange={setShowToast}
      >
        <Toast.Title className="text-sm font-medium">
          请选中主题后操作
        </Toast.Title>
      </Toast.Root>
      <Toast.Viewport className="fixed top-0.1 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[1001] flex flex-col gap-2 w-auto max-w-[100vw] m-0 list-none outline-none" />
    </Toast.Provider>
  );
};

export default StyleToolbar;
