import { useState } from "react";
import type { SelectField as SelectFieldConfig } from "@/types/form";

interface SelectFieldProps {
  field: SelectFieldConfig;
  value: string;
  error?: string;
  onChange: (value: string) => void;
}

export function SelectField({
  field,
  value,
  error,
  onChange,
}: SelectFieldProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="space-y-2">
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-between ${
            error ? "border-red-500" : ""
          }`}
        >
          <span className={value ? "text-gray-900" : "text-gray-500"}>
            {value
              ? field.options.find((opt) => opt.value === value)?.label
              : field.placeholder}
          </span>
          {/* 自定义箭头图标 */}
          <svg
            className={`w-4 h-4 transition-transform ${
              isOpen ? "rotate-180" : ""
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-auto">
            {field.options.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => {
                  onChange(option.value);
                  setIsOpen(false);
                }}
                className="w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 outline-none"
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>
      {error && <div className="text-sm text-red-500">{error}</div>}
    </div>
  );
}
