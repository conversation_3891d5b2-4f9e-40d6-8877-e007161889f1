import { useState, useEffect } from "react";
import type { MindMapNode } from "../store";
import { CANVAS_CONFIG, LAYOUT_CONFIG } from "../config/constants";

export function useSvgAutosize(nodes: MindMapNode[]) {
  const [svgBox, setSvgBox] = useState({
    minX: 0,
    minY: 0,
    width: CANVAS_CONFIG.DEFAULT_SIZE.width,
    height: CANVAS_CONFIG.DEFAULT_SIZE.height,
  });

  useEffect(() => {
    if (nodes.length === 0) {
      setSvgBox({
        minX: 0,
        minY: 0,
        width: CANVAS_CONFIG.DEFAULT_SIZE.width,
        height: CANVAS_CONFIG.DEFAULT_SIZE.height,
      });
      return;
    }
    let minX = Infinity,
      minY = Infinity,
      maxX = -Infinity,
      maxY = -Infinity;
    nodes.forEach((n) => {
      // 考虑节点的实际尺寸
      const nodeWidth = n.width || LAYOUT_CONFIG.NODE_DIMENSIONS.LEVEL_1.width;
      const nodeHeight =
        n.height || LAYOUT_CONFIG.NODE_DIMENSIONS.LEVEL_1.height;

      minX = Math.min(minX, n.x);
      minY = Math.min(minY, n.y);
      maxX = Math.max(maxX, n.x + nodeWidth);
      maxY = Math.max(maxY, n.y + nodeHeight);
    });

    // 调整padding和最小尺寸，让滚动条更合理
    setSvgBox({
      minX: minX - CANVAS_CONFIG.PADDING,
      minY: minY - CANVAS_CONFIG.PADDING,
      width: Math.max(
        CANVAS_CONFIG.MIN_SIZE.width,
        maxX - minX + CANVAS_CONFIG.PADDING * 2
      ),
      height: Math.max(
        CANVAS_CONFIG.MIN_SIZE.height,
        maxY - minY + CANVAS_CONFIG.PADDING * 2
      ),
    });
  }, [nodes]);

  return svgBox;
}
