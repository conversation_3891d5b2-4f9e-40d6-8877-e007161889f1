import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "@/components/ui/icons";
import { ConfigurableForm } from "@/components/ConfigurableForm";
import type { FormConfig } from "@/types/form";
import { FormMode } from "@/types/form";

interface CenterPanelProps {
  formConfig: FormConfig;
  onFormConfigChange: (newConfig: FormConfig) => void;
  selectedFieldId: string | null;
  onFieldSelect: (fieldId: string | null) => void;
}

export function CenterPanel({
  formConfig,
  onFormConfigChange,
  selectedFieldId,
  onFieldSelect,
}: CenterPanelProps) {
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFormConfigChange({ ...formConfig, title: e.target.value });
  };

  return (
    <div className="flex flex-1 flex-col bg-gray-50 p-6">
      <div className="mb-4 rounded-lg bg-white p-4 shadow-sm">
        <Input
          placeholder="请输入表单标题"
          className="mb-2 border-none text-center text-3xl font-bold focus-visible:ring-0"
          value={formConfig.title}
          onChange={handleTitleChange}
        />
      </div>

      <div
        className="relative flex flex-1 flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-white p-6 text-center overflow-auto"
        onClick={() => onFieldSelect(null)}
      >
        {formConfig.fields.length === 0 ? (
          <div className="space-y-4">
            <p className="text-gray-500">
              <span className="inline-flex items-center">
                <Plus className="mr-1 h-4 w-4" />
                点击左侧添加
              </span>{" "}
            </p>
          </div>
        ) : (
          <div className="w-full">
            <ConfigurableForm
              config={formConfig}
              renderMode={FormMode.BUILDER}
              onFieldClick={onFieldSelect}
              selectedFieldId={selectedFieldId}
            />
          </div>
        )}
      </div>
      <div className="mt-6 flex justify-center">
        <Button type="submit" className="w-48">
          提交
        </Button>
      </div>
    </div>
  );
}
