import { useState } from "react";
import "./PhoneLogin.css";
import LoginForm from "@/components/common/forms/LoginForm";
import AccountSelect from "@/components/common/account/AccountSelect";
import type { User } from "@/types/user";

interface PhoneLoginProps {
  onBack: () => void; // 返回主登录页的回调函数
}

const PhoneLogin: React.FC<PhoneLoginProps> = ({ onBack }) => {
  const [step, setStep] = useState<"form" | "account-select">("form");
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const [users, setUsers] = useState<User[]>([]);

  // 处理登录成功
  const handleLoginSuccess = (
    usersList: User[],
    defaultSelectedIds: number[]
  ) => {
    setUsers(usersList);
    setSelectedUserIds(defaultSelectedIds);
    setStep("account-select");
  };

  // 处理确认登录
  const handleConfirmLogin = (selectedIds: number[]) => {
    alert(selectedIds + " 登录成功");
  };

  // 账号选择页面
  if (step === "account-select") {
    return (
      <div className="phone-login-section">
        <AccountSelect
          users={users}
          initialSelectedIds={selectedUserIds}
          onBack={() => setStep("form")}
          onConfirm={handleConfirmLogin}
        />
      </div>
    );
  }

  // 使用可复用的登录表单
  return (
    <div className="phone-login-section">
      <LoginForm
        onBack={onBack}
        onLoginSuccess={handleLoginSuccess}
        showAutoLogin={false}
        title="短信验证码登录"
        subtitle="使用金山办公在线服务账号登录"
      />
    </div>
  );
};

export default PhoneLogin;
