<!-- 用户详情弹窗组件 -->
<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click="handleBackdropClick"
  >
    <div
      class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-96 overflow-y-auto"
      @click.stop
    >
      <!-- 头部 -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">用户详情</h3>
        <button
          class="text-gray-400 hover:text-gray-600 text-xl"
          @click="$emit('close')"
        >
          ×
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="p-6 text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        <p class="mt-2 text-sm text-gray-500">加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="p-6 text-center">
        <p class="text-red-600 text-sm">{{ error }}</p>
        <button
          class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
          @click="loadUserDetail"
        >
          重试
        </button>
      </div>

      <!-- 用户信息 -->
      <div v-else-if="userDetail" class="p-6">
        <!-- 头像和基本信息 -->
        <div class="flex items-center mb-6">
          <UserAvatar
            :name="userDetail.displayName"
            size="large"
            :is-online="userDetail.isOnline"
            :show-online-status="true"
          />
          <div class="ml-4">
            <h4 class="text-lg font-medium text-gray-900">{{ userDetail.displayName }}</h4>
            <p class="text-sm text-gray-500">@{{ userDetail.username }}</p>
            <p class="text-xs text-gray-400 mt-1">
              {{ userDetail.isOnline ? '在线' : '离线' }}
              <span v-if="!userDetail.isOnline">
                · 最后在线: {{ formatTime(userDetail.lastOnlineTime) }}
              </span>
            </p>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="space-y-4">
          <div>
            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide">邮箱</label>
            <p class="mt-1 text-sm text-gray-900">{{ userDetail.email }}</p>
          </div>

          <div>
            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide">部门</label>
            <p class="mt-1 text-sm text-gray-900">{{ userDetail.department }}</p>
          </div>

          <div>
            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide">职位</label>
            <p class="mt-1 text-sm text-gray-900">{{ userDetail.position }}</p>
          </div>

          <div>
            <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide">用户ID</label>
            <p class="mt-1 text-sm text-gray-900 font-mono">{{ userDetail.id }}</p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mt-6 flex gap-2">
          <button
            class="flex-1 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
            @click="startChat"
          >
            发起聊天
          </button>
          <button
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50 text-sm"
            @click="$emit('close')"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { apiClient, type User } from '../api'
import UserAvatar from './UserAvatar.vue'

interface Props {
  isVisible: boolean
  userId: string | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  'start-chat': [userId: string]
}>()

const isLoading = ref(false)
const error = ref('')
const userDetail = ref<User | null>(null)

// 监听弹窗显示状态和用户ID变化
watch(
  () => [props.isVisible, props.userId],
  ([visible, userId]) => {
    if (visible && userId) {
      loadUserDetail()
    } else {
      // 重置状态
      userDetail.value = null
      error.value = ''
    }
  },
  { immediate: true }
)

// 加载用户详情
const loadUserDetail = async () => {
  if (!props.userId) return

  try {
    isLoading.value = true
    error.value = ''
    
    const response = await apiClient.getUserDetail(props.userId)
    
    if (response.success) {
      userDetail.value = response.user
    } else {
      error.value = '获取用户信息失败'
    }
  } catch (err) {
    console.error('获取用户详情失败:', err)
    error.value = err instanceof Error ? err.message : '获取用户信息失败'
  } finally {
    isLoading.value = false
  }
}

// 处理背景点击
const handleBackdropClick = () => {
  emit('close')
}

// 发起聊天
const startChat = () => {
  if (userDetail.value) {
    emit('start-chat', userDetail.value.id)
    emit('close')
  }
}

// 时间格式化
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}
</script>
