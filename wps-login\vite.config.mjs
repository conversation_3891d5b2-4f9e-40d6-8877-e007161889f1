import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { fileURLToPath } from "url";
import { dirname, resolve } from "path";

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // const base = mode === "production" ? "/zhuyuqian/" : "/";
  return {
    base: "/zhuyuqian",
    plugins: [react()],
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
      },
    },
    server: {
      host: "0.0.0.0",
      port: 8000,
      proxy: {
        "/api": {
          target: "https://account.wps.cn",
          changeOrigin: true,
        },
      },
    },
    test: {
      globals: true,
      environment: "jsdom",
      setupFiles: ["./src/test/setup.ts"],
      css: true,
      coverage: {
        enabled: true,
        provider: "v8",
        reportsDirectory: "./coverage",
        reporter: ["text", "json", "html"],
        cleanOnRerun: false,
      },
      // 指定测试文件的查找模式
      include: ["**/__tests__/*.{test,spec}.{ts,tsx}"],
    },
  };
});
