import { Tab } from "@headlessui/react";
import StartToolbar from "./StartToolbar";
import StyleToolbar from "./StyleToolbar";

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function Toolbar() {
  const tabs = [
    { name: "开始", component: StartToolbar },
    { name: "样式", component: StyleToolbar },
    {
      name: "插入",
      component: () => <div className="p-4">插入功能开发中...</div>,
    },
    {
      name: "视图",
      component: () => <div className="p-4">视图功能开发中...</div>,
    },
    {
      name: "导出",
      component: () => <div className="p-4">导出功能开发中...</div>,
    },
  ];

  return (
    <div className="bg-white border-b border-gray-200">
      <Tab.Group>
        {/* Tab Navigation */}
        <div className="bg-gray-200 flex items-center justify-center px-4 py-1 border-b border-gray-200">
          <Tab.List className="flex space-x-1 p-1 rounded-lg">
            {tabs.map((tab) => (
              <Tab
                key={tab.name}
                className={({ selected }) =>
                  classNames(
                    "flex items-center space-x-2 px-4 py-1 text-sm font-medium transition-colors cursor-pointer",
                    selected
                      ? " text-teal-600 border-b-2 border-teal-600"
                      : "text-gray-600 hover:text-teal-600"
                  )
                }
              >
                <span>{tab.name}</span>
              </Tab>
            ))}
          </Tab.List>
        </div>
        {/* Tab Panels */}
        <Tab.Panels>
          {tabs.map((tab, idx) => (
            <Tab.Panel key={idx}>
              <tab.component />
            </Tab.Panel>
          ))}
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
}
