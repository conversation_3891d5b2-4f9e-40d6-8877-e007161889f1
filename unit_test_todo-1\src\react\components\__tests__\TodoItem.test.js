import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import TodoItem from "../TodoItem";

describe("TodoItem组件", () => {
  const mockTodo = {
    id: "123",
    text: "测试待办事项",
    completed: false,
    createdAt: new Date("2023-01-01T12:00:00"),
  };

  const mockHandlers = {
    onToggle: jest.fn(),
    onDelete: jest.fn(),
    onEdit: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("正确渲染待办项内容", () => {
    render(<TodoItem todo={mockTodo} {...mockHandlers} />);

    expect(screen.getByText("测试待办事项")).toBeInTheDocument();
    const checkbox = screen.getByRole("checkbox");
    expect(checkbox).not.toBeChecked();
  });

  test("正确显示完成状态", () => {
    const completedTodo = { ...mockTodo, completed: true };
    render(<TodoItem todo={completedTodo} {...mockHandlers} />);

    const checkbox = screen.getByRole("checkbox");
    expect(checkbox).toBeChecked();

    const todoItem = screen.getByText("测试待办事项").closest(".todo-item");
    expect(todoItem).toHaveClass("completed");
  });

  test("当showDate为true时显示日期", () => {
    render(<TodoItem todo={mockTodo} showDate={true} {...mockHandlers} />);

    // 确保日期格式化函数被调用，格式化后包含年份
    expect(screen.getByText(/2023/)).toBeInTheDocument();
  });

  test("点击复选框时调用onToggle", () => {
    render(<TodoItem todo={mockTodo} {...mockHandlers} />);

    const checkbox = screen.getByRole("checkbox");
    fireEvent.click(checkbox);

    expect(mockHandlers.onToggle).toHaveBeenCalledWith("123");
  });

  test("点击编辑按钮时调用onEdit", () => {
    render(<TodoItem todo={mockTodo} {...mockHandlers} />);

    const editButton = screen.getByTitle("编辑");
    fireEvent.click(editButton);

    expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockTodo);
  });

  test("点击删除按钮时调用onDelete", () => {
    render(<TodoItem todo={mockTodo} {...mockHandlers} />);

    const deleteButton = screen.getByTitle("删除");
    fireEvent.click(deleteButton);

    expect(mockHandlers.onDelete).toHaveBeenCalledWith("123");
  });
});
