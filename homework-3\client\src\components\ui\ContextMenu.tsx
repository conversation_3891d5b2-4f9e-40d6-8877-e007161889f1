import * as React from "react";
import { useState } from "react";
import * as ContextMenu from "@radix-ui/react-context-menu";
import {
  CopyIcon,
  ScissorsIcon,
  ClipboardIcon,
  TrashIcon,
  DownloadIcon,
  ChevronRightIcon,
} from "@radix-ui/react-icons";
import CustomIcons from "../icons/SvgIcons";
import { useMindMapStore } from "../../store/index";
import AIProgressCard from "./AIProgressCard";

interface NodeContextMenuProps {
  children: React.ReactNode;
  nodeId?: string;
}

const NodeContextMenu: React.FC<NodeContextMenuProps> = ({
  children,
  nodeId,
}) => {
  const [showAIProgress, setShowAIProgress] = useState(false);
  const [isRequesting, setIsRequesting] = useState(false);
  const [lastRequestTime, setLastRequestTime] = useState(0);

  // 只订阅需要的数据
  const nodes = useMindMapStore((state) => state.nodes);
  const isAIProcessing = useMindMapStore((state) => state.isAIProcessing);
  const aiProgress = useMindMapStore((state) => state.aiProgress);

  // 方法可以直接获取，不需要订阅
  const {
    selectNode,
    addChildTheme,
    addSameLevelTheme,
    addParentTheme,
    removeNode,
    toggleNodeCollapse,
    generateChildrenFromAI,
    generateChildrenFromAIStream,
    stopAIGeneration,
    confirmAIGeneration,
    discardAIGeneration,
  } = useMindMapStore.getState();

  // 处理右键菜单打开
  const handleContextMenuOpen = () => {
    if (nodeId) {
      selectNode(nodeId);
    }
  };

  // 获取选中的节点
  const selectedNode = nodes.find((n) => n.id === nodeId);

  // 判断功能可用性
  const canAddChild = selectedNode && selectedNode.level < 3;
  const canAddSameLevel = selectedNode && selectedNode.level > 1;
  const canAddParent =
    selectedNode && selectedNode.level > 2 && selectedNode.level < 3;
  const canDelete = selectedNode && selectedNode.level !== 1;
  const canAICreation = selectedNode && selectedNode.level === 1;

  const handleMenuAction = async (action: string) => {
    const now = Date.now();

    // 防止3秒内重复请求
    if (action === "ai-creation" && now - lastRequestTime < 3000) {
      return;
    }

    if (isRequesting) return;

    switch (action) {
      case "ai-creation":
        if (canAICreation && !isRequesting) {
          setLastRequestTime(now);
          setIsRequesting(true);
          setShowAIProgress(true); // 显示进度卡片
          if (!selectedNode) return;
          try {
            await generateChildrenFromAI(selectedNode.id);
            // 成功后由 AIProgressCard 内部处理
          } catch (error) {
            console.log("失败", error);
            // 错误处理由 AIProgressCard 内部处理
          } finally {
            setIsRequesting(false);
          }
        }
        break;
      case "ai-creation-stream":
        if (canAICreation && !isRequesting) {
          setLastRequestTime(now);
          setIsRequesting(true);
          setShowAIProgress(true); // 显示进度卡片
          if (!selectedNode) return;
          try {
            await generateChildrenFromAIStream(selectedNode.id);
            // 成功后由 AIProgressCard 内部处理
          } catch (error) {
            console.log("流式AI创作失败", error);
            // 错误处理由 AIProgressCard 内部处理
          } finally {
            setIsRequesting(false);
          }
        }
        break;
      case "addChild":
        if (canAddChild) addChildTheme(selectedNode.id);
        break;
      case "addSameLevel":
        if (canAddSameLevel) addSameLevelTheme(selectedNode.id);
        break;
      case "addParent":
        if (canAddParent) addParentTheme(selectedNode.id);
        break;
      case "collapse":
        toggleNodeCollapse(selectedNode!.id);
        break;
      case "delete":
        if (canDelete) removeNode(selectedNode.id);
        break;
      case "copy":
        console.log("复制主题");
        break;
      case "cut":
        console.log("剪切主题");
        break;
      case "paste":
        console.log("粘贴主题");
        break;
      case "export":
        console.log("导出为图片");
        break;
      case "focus":
        console.log("聚焦模式");
        break;
    }
  };

  // 处理 AIProgressCard 的回调函数
  const handleUseAI = () => {
    confirmAIGeneration();
    setShowAIProgress(false);
  };

  const handleDiscardAI = () => {
    discardAIGeneration();
    setShowAIProgress(false);
  };

  const handleRegenerateAI = () => {
    // 重新生成AI内容
    if (selectedNode) {
      generateChildrenFromAI(selectedNode.id);
    }
  };

  const handleStopAI = () => {
    // 停止AI生成
    stopAIGeneration();
  };

  return (
    <>
      <ContextMenu.Root onOpenChange={handleContextMenuOpen}>
        <ContextMenu.Trigger asChild>{children}</ContextMenu.Trigger>
        <ContextMenu.Portal>
          <ContextMenu.Content className="min-w-[220px] bg-white rounded-md p-1 shadow-lg border border-gray-200 z-[1000] animate-in fade-in-0 zoom-in-95">
            {/* AI创作（流式）菜单项 */}
            <ContextMenu.Item
              className={`
                flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none
                data-[highlighted]:bg-blue-500 data-[highlighted]:text-white
                data-[disabled]:text-gray-400 data-[disabled]:pointer-events-none
              `}
              onSelect={() => handleMenuAction("ai-creation-stream")}
              disabled={!canAICreation || isRequesting}
            >
              <CustomIcons className="w-4 h-4 mr-2 pb-0.5" type="ai-creation" />
              {isRequesting ? "AI创作中..." : "AI创作"}
            </ContextMenu.Item>

            {/* AI创作菜单项
            <ContextMenu.Item
              className={`
                flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none
                data-[highlighted]:bg-blue-500 data-[highlighted]:text-white
                data-[disabled]:text-gray-400 data-[disabled]:pointer-events-none
              `}
              onSelect={() => handleMenuAction("ai-creation")}
              disabled={!canAICreation || isRequesting}
            >
              <CustomIcons className="w-4 h-4 mr-2 pb-0.5" type="ai-creation" />
              {isRequesting ? "AI创作中..." : "AI创作（非流式）"}
            </ContextMenu.Item> */}
            {/* 主题组 */}
            <ContextMenu.Item
              className={`
                flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none
                data-[highlighted]:bg-blue-500 data-[highlighted]:text-white
                data-[disabled]:text-gray-400 data-[disabled]:pointer-events-none
              `}
              disabled={!canAddChild}
              onSelect={() => handleMenuAction("addChild")}
            >
              <CustomIcons className="w-4 h-4 mr-2" type="child-theme" />
              新增子主题
              <div className="ml-auto text-xs text-gray-500 data-[highlighted]:text-blue-100">
                Tab
              </div>
            </ContextMenu.Item>

            <ContextMenu.Item
              className={`
                flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none
                data-[highlighted]:bg-blue-500 data-[highlighted]:text-white
                data-[disabled]:text-gray-400 data-[disabled]:pointer-events-none
              `}
              disabled={!canAddSameLevel}
              onSelect={() => handleMenuAction("addSameLevel")}
            >
              <CustomIcons className="w-4 h-4 mr-2" type="same-theme" />
              新增同级主题
              <div className="ml-auto text-xs text-gray-500 data-[highlighted]:text-blue-100">
                Enter
              </div>
            </ContextMenu.Item>

            <ContextMenu.Item
              className={`
                flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none
                data-[highlighted]:bg-blue-500 data-[highlighted]:text-white
                data-[disabled]:text-gray-400 data-[disabled]:pointer-events-none
              `}
              disabled={!canAddParent}
              onSelect={() => handleMenuAction("addParent")}
            >
              <CustomIcons className="w-4 h-4 mr-2" type="parent-theme" />
              新增父主题
              <div className="ml-auto text-xs text-gray-500 data-[highlighted]:text-blue-100">
                Shift+Tab
              </div>
            </ContextMenu.Item>

            <ContextMenu.Separator className="h-px bg-gray-200 my-1" />

            {/* 插入和编号子菜单 */}
            <ContextMenu.Sub>
              <ContextMenu.SubTrigger
                className={`
                flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none
                data-[highlighted]:bg-blue-500 data-[highlighted]:text-white
                data-[state=open]:bg-blue-500 data-[state=open]:text-white
              `}
              >
                插入
                <ChevronRightIcon className="w-4 h-4 ml-auto" />
              </ContextMenu.SubTrigger>
              <ContextMenu.Portal>
                <ContextMenu.SubContent className="min-w-[220px] bg-white rounded-md p-1 shadow-lg border border-gray-200 animate-in fade-in-0 zoom-in-95">
                  <ContextMenu.Item className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white">
                    插入图片
                  </ContextMenu.Item>
                  <ContextMenu.Item className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white">
                    插入链接
                  </ContextMenu.Item>
                  <ContextMenu.Item className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white">
                    插入备注
                  </ContextMenu.Item>
                </ContextMenu.SubContent>
              </ContextMenu.Portal>
            </ContextMenu.Sub>

            <ContextMenu.Sub>
              <ContextMenu.SubTrigger
                className={`
                flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none
                data-[highlighted]:bg-blue-500 data-[highlighted]:text-white
                data-[state=open]:bg-blue-500 data-[state=open]:text-white
              `}
              >
                编号
                <ChevronRightIcon className="w-4 h-4 ml-auto" />
              </ContextMenu.SubTrigger>
              <ContextMenu.Portal>
                <ContextMenu.SubContent className="min-w-[220px] bg-white rounded-md p-1 shadow-lg border border-gray-200 animate-in fade-in-0 zoom-in-95">
                  <ContextMenu.Item className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white">
                    数字编号
                  </ContextMenu.Item>
                  <ContextMenu.Item className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white">
                    字母编号
                  </ContextMenu.Item>
                  <ContextMenu.Item className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white">
                    罗马数字
                  </ContextMenu.Item>
                  <ContextMenu.Item className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white">
                    取消编号
                  </ContextMenu.Item>
                </ContextMenu.SubContent>
              </ContextMenu.Portal>
            </ContextMenu.Sub>

            <ContextMenu.Separator className="h-px bg-gray-200 my-1" />

            {/* 主题操作 */}
            <ContextMenu.Item
              className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white"
              onSelect={() => handleMenuAction("collapse")}
            >
              {selectedNode?.collapsed ? "展开主题" : "收起主题"}
            </ContextMenu.Item>

            <ContextMenu.Item className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white">
              选择主题
            </ContextMenu.Item>

            <ContextMenu.Separator className="h-px bg-gray-200 my-1" />

            {/* 编辑操作 */}
            <ContextMenu.Item
              className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white"
              onSelect={() => handleMenuAction("copy")}
            >
              <CopyIcon className="w-4 h-4 mr-2" />
              复制主题
              <div className="ml-auto text-xs text-gray-500">Ctrl+C</div>
            </ContextMenu.Item>

            <ContextMenu.Item
              className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white"
              onSelect={() => handleMenuAction("cut")}
            >
              <ScissorsIcon className="w-4 h-4 mr-2" />
              剪切
              <div className="ml-auto text-xs text-gray-500">Ctrl+X</div>
            </ContextMenu.Item>

            <ContextMenu.Item
              className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white"
              onSelect={() => handleMenuAction("paste")}
            >
              <ClipboardIcon className="w-4 h-4 mr-2" />
              粘贴主题
              <div className="ml-auto text-xs text-gray-500">Ctrl+V</div>
            </ContextMenu.Item>

            <ContextMenu.Separator className="h-px bg-gray-200 my-1" />

            <ContextMenu.Item
              className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white"
              disabled={!canDelete}
              onSelect={() => handleMenuAction("delete")}
            >
              <TrashIcon className="w-4 h-4 mr-2" />
              删除当前主题
              <div className="ml-auto text-xs text-gray-500">delete</div>
            </ContextMenu.Item>

            <ContextMenu.Item
              className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white"
              disabled={!canDelete}
              onSelect={() => handleMenuAction("delete")}
            >
              批量删除
              <div className="ml-auto text-xs text-gray-500">delete</div>
            </ContextMenu.Item>

            <ContextMenu.Separator className="h-px bg-gray-200 my-1" />

            {/* 其他功能 */}
            <ContextMenu.Item
              className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white"
              onSelect={() => handleMenuAction("focus")}
            >
              <CustomIcons className="w-4 h-4 mr-2" type="focus" />
              聚焦模式
            </ContextMenu.Item>

            <ContextMenu.Item
              className="flex items-center h-8 px-2 text-sm text-gray-900 rounded-sm cursor-pointer outline-none data-[highlighted]:bg-blue-500 data-[highlighted]:text-white"
              onSelect={() => handleMenuAction("export")}
            >
              <DownloadIcon className="w-4 h-4 mr-2" />
              导出当前主题为图片
            </ContextMenu.Item>
          </ContextMenu.Content>
        </ContextMenu.Portal>
      </ContextMenu.Root>

      {/* 更新 AIProgressCard 组件 */}
      {showAIProgress && (
        <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[1001]">
          <AIProgressCard
            open={showAIProgress}
            onOpenChange={(open) => setShowAIProgress(open)}
            isAIProcessing={isAIProcessing}
            aiProgress={aiProgress}
            onUse={handleUseAI}
            onDiscard={handleDiscardAI}
            onRegenerate={handleRegenerateAI}
            onStop={handleStopAI}
          />
        </div>
      )}
    </>
  );
};

export default NodeContextMenu;
