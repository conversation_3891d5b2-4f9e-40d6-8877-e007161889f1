<template>
  <div class="chain-demo">
    <h2>职责链模式演示 - 请求处理系统</h2>

    <div class="request-controls">
      <div class="request-type">
        <label>请求类型:</label>
        <select v-model="requestType">
          <option value="auth">认证</option>
          <option value="validation">验证</option>
          <option value="logging">日志</option>
          <option value="processing">处理</option>
        </select>
      </div>

      <div class="request-data">
        <label>请求数据 (JSON):</label>
        <textarea v-model="requestData" placeholder='{"key":"value"}'></textarea>
      </div>

      <div class="user-auth" v-if="requestType === 'auth'">
        <label> <input type="checkbox" v-model="includeUser" /> 包含用户信息 </label>
      </div>

      <button @click="submitRequest" :disabled="isProcessing">
        {{ isProcessing ? '处理中...' : '提交请求' }}
      </button>
    </div>

    <div class="response" :class="{ success: response?.success }">
      <h3>响应结果</h3>
      <pre v-if="response">{{ response }}</pre>
      <p v-else>暂无响应</p>
    </div>

    <div class="chain-visualization">
      <h3>处理链顺序</h3>
      <div class="chain">
        <div v-for="(handler, index) in handlerChain" :key="index" class="handler">
          {{ handler }}
          <span class="connector" v-if="index < handlerChain.length - 1">→</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { AuthHandler } from '../utils/chain/handlers'
import { ValidationHandler } from '../utils/chain/handlers'
import { LoggingHandler } from '../utils/chain/handlers'
import { ProcessingHandler } from '../utils/chain/handlers'
import type { Request } from '../utils/chain/types'

export default defineComponent({
  name: 'ChainDemo',
  setup() {
    const requestType = ref<Request['type']>('auth')
    const requestData = ref('{"test": "data"}')
    const includeUser = ref(false)
    const response = ref<any>(null)
    const isProcessing = ref(false)

    // 构建处理链
    const authHandler = new AuthHandler()
    const validationHandler = new ValidationHandler()
    const loggingHandler = new LoggingHandler()
    const processingHandler = new ProcessingHandler()

    authHandler.setNext(validationHandler).setNext(loggingHandler).setNext(processingHandler)

    const handlerChain = computed(() => [
      'AuthHandler',
      'ValidationHandler',
      'LoggingHandler',
      'ProcessingHandler',
    ])

    const submitRequest = async () => {
      isProcessing.value = true
      response.value = null

      try {
        const request: Request = {
          type: requestType.value,
          data: JSON.parse(requestData.value),
          user: includeUser.value
            ? {
                role: 'admin',
                permissions: ['read', 'write'],
              }
            : undefined,
        }

        response.value = await authHandler.handle(request)
      } catch (error) {
        response.value = {
          success: false,
          message: `请求格式错误: ${error}`,
        }
      } finally {
        isProcessing.value = false
      }
    }

    return {
      requestType,
      requestData,
      includeUser,
      response,
      isProcessing,
      handlerChain,
      submitRequest,
    }
  },
})
</script>

<style scoped>
.chain-demo {
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.request-controls {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f8f8;
  border-radius: 8px;
}

.request-type,
.request-data,
.user-auth {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

select,
textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
}

textarea {
  min-height: 80px;
}

button {
  padding: 10px 20px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
}

button:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.response {
  margin: 20px 0;
  padding: 15px;
  border-radius: 4px;
  background: #f5f5f5;
  border: 1px solid #ddd;
}

.response.success {
  background: #e8f5e9;
  border-color: #a5d6a7;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.chain-visualization {
  margin-top: 30px;
}

.chain {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
}

.handler {
  padding: 10px 20px;
  background: #e3f2fd;
  border-radius: 4px;
  border: 1px solid #bbdefb;
}

.connector {
  margin-left: 15px;
  color: #42b983;
  font-weight: bold;
}
</style>
