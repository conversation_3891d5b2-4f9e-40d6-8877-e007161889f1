hoistPattern:
  - '*'
hoistedDependencies:
  7zip-bin@5.2.0:
    7zip-bin: public
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': public
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': public
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': public
  '@babel/core@7.28.3':
    '@babel/core': public
  '@babel/generator@7.28.3':
    '@babel/generator': public
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': public
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': public
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': public
  '@babel/helper-module-transforms@7.28.3(@babel/core@7.28.3)':
    '@babel/helper-module-transforms': public
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': public
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': public
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': public
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': public
  '@babel/helpers@7.28.3':
    '@babel/helpers': public
  '@babel/parser@7.28.3':
    '@babel/parser': public
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-arrow-functions': public
  '@babel/template@7.27.2':
    '@babel/template': public
  '@babel/traverse@7.28.3':
    '@babel/traverse': public
  '@babel/types@7.28.2':
    '@babel/types': public
  '@develar/schema-utils@2.6.5':
    '@develar/schema-utils': public
  '@electron/asar@3.4.1':
    '@electron/asar': public
  '@electron/get@2.0.3':
    '@electron/get': public
  '@electron/notarize@2.5.0':
    '@electron/notarize': public
  '@electron/osx-sign@1.3.1':
    '@electron/osx-sign': public
  '@electron/rebuild@3.6.1':
    '@electron/rebuild': public
  '@electron/universal@2.0.1':
    '@electron/universal': public
  '@epic-web/invariant@1.0.0':
    '@epic-web/invariant': public
  '@esbuild/win32-x64@0.25.9':
    '@esbuild/win32-x64': public
  '@eslint-community/eslint-utils@4.7.0(eslint@9.33.0(jiti@2.5.1))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/eslint-utils@4.7.0(eslint@9.33.0)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.3.1':
    '@eslint/config-helpers': public
  '@eslint/core@0.15.2':
    '@eslint/core': public
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': public
  '@eslint/js@9.33.0':
    '@eslint/js': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.3.5':
    '@eslint/plugin-kit': public
  '@gar/promisify@1.1.3':
    '@gar/promisify': public
  '@humanfs/core@0.19.1':
    '@humanfs/core': public
  '@humanfs/node@0.16.6':
    '@humanfs/node': public
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': public
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': public
  '@isaacs/balanced-match@4.0.1':
    '@isaacs/balanced-match': public
  '@isaacs/brace-expansion@5.0.0':
    '@isaacs/brace-expansion': public
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': public
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': public
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': public
  '@jridgewell/remapping@2.3.5':
    '@jridgewell/remapping': public
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': public
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': public
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': public
  '@malept/cross-spawn-promise@2.0.0':
    '@malept/cross-spawn-promise': public
  '@malept/flatpak-bundler@0.4.0':
    '@malept/flatpak-bundler': public
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': public
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': public
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': public
  '@npmcli/fs@2.1.2':
    '@npmcli/fs': public
  '@npmcli/move-file@2.0.1':
    '@npmcli/move-file': public
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': public
  '@pkgr/core@0.2.9':
    '@pkgr/core': public
  '@rolldown/pluginutils@1.0.0-beta.29':
    '@rolldown/pluginutils': public
  '@rollup/rollup-win32-x64-msvc@4.46.2':
    '@rollup/rollup-win32-x64-msvc': public
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': public
  '@szmarczak/http-timer@4.0.6':
    '@szmarczak/http-timer': public
  '@tailwindcss/node@4.1.12':
    '@tailwindcss/node': public
  '@tailwindcss/oxide-win32-x64-msvc@4.1.12':
    '@tailwindcss/oxide-win32-x64-msvc': public
  '@tailwindcss/oxide@4.1.12':
    '@tailwindcss/oxide': public
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': public
  '@types/cacheable-request@6.0.3':
    '@types/cacheable-request': public
  '@types/debug@4.1.12':
    '@types/debug': public
  '@types/estree@1.0.8':
    '@types/estree': public
  '@types/fs-extra@9.0.13':
    '@types/fs-extra': public
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': public
  '@types/json-schema@7.0.15':
    '@types/json-schema': public
  '@types/keyv@3.1.4':
    '@types/keyv': public
  '@types/ms@2.1.0':
    '@types/ms': public
  '@types/responselike@1.0.3':
    '@types/responselike': public
  '@types/yauzl@2.10.3':
    '@types/yauzl': public
  '@typescript-eslint/eslint-plugin@8.39.1(@typescript-eslint/parser@8.39.1(eslint@9.33.0(jiti@2.5.1))(typescript@5.9.2))(eslint@9.33.0(jiti@2.5.1))(typescript@5.9.2)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/eslint-plugin@8.39.1(@typescript-eslint/parser@8.39.1(eslint@9.33.0)(typescript@5.9.2))(eslint@9.33.0)(typescript@5.9.2)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.39.1(eslint@9.33.0(jiti@2.5.1))(typescript@5.9.2)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/parser@8.39.1(eslint@9.33.0)(typescript@5.9.2)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/project-service@8.39.1(typescript@5.9.2)':
    '@typescript-eslint/project-service': public
  '@typescript-eslint/scope-manager@8.39.1':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/tsconfig-utils@8.39.1(typescript@5.9.2)':
    '@typescript-eslint/tsconfig-utils': public
  '@typescript-eslint/type-utils@8.39.1(eslint@9.33.0(jiti@2.5.1))(typescript@5.9.2)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/type-utils@8.39.1(eslint@9.33.0)(typescript@5.9.2)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.39.1':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.39.1(typescript@5.9.2)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.39.1(eslint@9.33.0(jiti@2.5.1))(typescript@5.9.2)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/utils@8.39.1(eslint@9.33.0)(typescript@5.9.2)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.39.1':
    '@typescript-eslint/visitor-keys': public
  '@volar/language-core@2.4.22':
    '@volar/language-core': public
  '@volar/source-map@2.4.22':
    '@volar/source-map': public
  '@volar/typescript@2.4.22':
    '@volar/typescript': public
  '@vue/compiler-core@3.5.18':
    '@vue/compiler-core': public
  '@vue/compiler-dom@3.5.18':
    '@vue/compiler-dom': public
  '@vue/compiler-sfc@3.5.18':
    '@vue/compiler-sfc': public
  '@vue/compiler-ssr@3.5.18':
    '@vue/compiler-ssr': public
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': public
  '@vue/language-core@3.0.5(typescript@5.9.2)':
    '@vue/language-core': public
  '@vue/reactivity@3.5.18':
    '@vue/reactivity': public
  '@vue/runtime-core@3.5.18':
    '@vue/runtime-core': public
  '@vue/runtime-dom@3.5.18':
    '@vue/runtime-dom': public
  '@vue/server-renderer@3.5.18(vue@3.5.18(typescript@5.9.2))':
    '@vue/server-renderer': public
  '@vue/shared@3.5.18':
    '@vue/shared': public
  '@xmldom/xmldom@0.8.10':
    '@xmldom/xmldom': public
  abbrev@1.1.1:
    abbrev: public
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: public
  acorn@8.15.0:
    acorn: public
  agent-base@7.1.4:
    agent-base: public
  agentkeepalive@4.6.0:
    agentkeepalive: public
  aggregate-error@3.1.0:
    aggregate-error: public
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: public
  ajv@6.12.6:
    ajv: public
  alien-signals@2.0.6:
    alien-signals: public
  ansi-escapes@7.0.0:
    ansi-escapes: public
  ansi-regex@5.0.1:
    ansi-regex: public
  ansi-styles@4.3.0:
    ansi-styles: public
  app-builder-bin@5.0.0-alpha.10:
    app-builder-bin: public
  app-builder-lib@25.1.8(dmg-builder@25.1.8)(electron-builder-squirrel-windows@25.1.8):
    app-builder-lib: public
  aproba@2.1.0:
    aproba: public
  archiver-utils@2.1.0:
    archiver-utils: public
  archiver@5.3.2:
    archiver: public
  are-we-there-yet@3.0.1:
    are-we-there-yet: public
  argparse@2.0.1:
    argparse: public
  async-exit-hook@2.0.1:
    async-exit-hook: public
  async@3.2.6:
    async: public
  asynckit@0.4.0:
    asynckit: public
  at-least-node@1.0.0:
    at-least-node: public
  balanced-match@1.0.2:
    balanced-match: public
  base64-js@1.5.1:
    base64-js: public
  bl@4.1.0:
    bl: public
  bluebird-lst@1.0.9:
    bluebird-lst: public
  bluebird@3.7.2:
    bluebird: public
  boolbase@1.0.0:
    boolbase: public
  boolean@3.2.0:
    boolean: public
  brace-expansion@1.1.12:
    brace-expansion: public
  braces@3.0.3:
    braces: public
  browserslist@4.25.2:
    browserslist: public
  buffer-crc32@0.2.13:
    buffer-crc32: public
  buffer-from@1.1.2:
    buffer-from: public
  buffer@5.7.1:
    buffer: public
  builder-util-runtime@9.2.10:
    builder-util-runtime: public
  builder-util@25.1.7:
    builder-util: public
  cac@6.7.14:
    cac: public
  cacache@16.1.3:
    cacache: public
  cacheable-lookup@5.0.4:
    cacheable-lookup: public
  cacheable-request@7.0.4:
    cacheable-request: public
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: public
  callsites@3.1.0:
    callsites: public
  caniuse-lite@1.0.30001735:
    caniuse-lite: public
  chalk@4.1.2:
    chalk: public
  chownr@2.0.0:
    chownr: public
  chromium-pickle-js@0.2.0:
    chromium-pickle-js: public
  ci-info@3.9.0:
    ci-info: public
  clean-stack@2.2.0:
    clean-stack: public
  cli-cursor@3.1.0:
    cli-cursor: public
  cli-cursor@5.0.0:
    cli-cursor: public
  cli-spinners@2.9.2:
    cli-spinners: public
  cli-truncate@4.0.0:
    cli-truncate: public
  cliui@8.0.1:
    cliui: public
  clone-response@1.0.3:
    clone-response: public
  clone@1.0.4:
    clone: public
  color-convert@2.0.1:
    color-convert: public
  color-name@1.1.4:
    color-name: public
  color-support@1.1.3:
    color-support: public
  colorette@2.0.20:
    colorette: public
  combined-stream@1.0.8:
    combined-stream: public
  commander@14.0.0:
    commander: public
  commander@5.1.0:
    commander: public
  compare-version@0.1.2:
    compare-version: public
  compress-commons@4.1.2:
    compress-commons: public
  concat-map@0.0.1:
    concat-map: public
  config-file-ts@0.2.8-rc1:
    config-file-ts: public
  console-control-strings@1.1.0:
    console-control-strings: public
  convert-source-map@2.0.0:
    convert-source-map: public
  core-util-is@1.0.3:
    core-util-is: public
  crc-32@1.2.2:
    crc-32: public
  crc32-stream@4.0.3:
    crc32-stream: public
  cross-spawn@7.0.6:
    cross-spawn: public
  cssesc@3.0.0:
    cssesc: public
  csstype@3.1.3:
    csstype: public
  de-indent@1.0.2:
    de-indent: public
  debug@4.4.1:
    debug: public
  decompress-response@6.0.0:
    decompress-response: public
  deep-is@0.1.4:
    deep-is: public
  defaults@1.0.4:
    defaults: public
  defer-to-connect@2.0.1:
    defer-to-connect: public
  define-data-property@1.1.4:
    define-data-property: public
  define-properties@1.2.1:
    define-properties: public
  delayed-stream@1.0.0:
    delayed-stream: public
  delegates@1.0.0:
    delegates: public
  detect-libc@2.0.4:
    detect-libc: public
  detect-node@2.1.0:
    detect-node: public
  dir-compare@4.2.0:
    dir-compare: public
  dmg-builder@25.1.8(electron-builder-squirrel-windows@25.1.8):
    dmg-builder: public
  dotenv-expand@11.0.7:
    dotenv-expand: public
  dotenv@16.6.1:
    dotenv: public
  dunder-proto@1.0.1:
    dunder-proto: public
  eastasianwidth@0.2.0:
    eastasianwidth: public
  ejs@3.1.10:
    ejs: public
  electron-builder-squirrel-windows@25.1.8(dmg-builder@25.1.8):
    electron-builder-squirrel-windows: public
  electron-publish@25.1.7:
    electron-publish: public
  electron-to-chromium@1.5.201:
    electron-to-chromium: public
  emoji-regex@8.0.0:
    emoji-regex: public
  encoding@0.1.13:
    encoding: public
  end-of-stream@1.4.5:
    end-of-stream: public
  enhanced-resolve@5.18.3:
    enhanced-resolve: public
  entities@4.5.0:
    entities: public
  env-paths@2.2.1:
    env-paths: public
  environment@1.1.0:
    environment: public
  err-code@2.0.3:
    err-code: public
  es-define-property@1.0.1:
    es-define-property: public
  es-errors@1.3.0:
    es-errors: public
  es-object-atoms@1.1.1:
    es-object-atoms: public
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: public
  es6-error@4.1.1:
    es6-error: public
  esbuild@0.25.9:
    esbuild: public
  escalade@3.2.0:
    escalade: public
  escape-string-regexp@4.0.0:
    escape-string-regexp: public
  eslint-config-prettier@10.1.8(eslint@9.33.0(jiti@2.5.1)):
    eslint-config-prettier: public
  eslint-config-prettier@10.1.8(eslint@9.33.0):
    eslint-config-prettier: public
  eslint-plugin-prettier@5.5.4(eslint-config-prettier@10.1.8(eslint@9.33.0(jiti@2.5.1)))(eslint@9.33.0(jiti@2.5.1))(prettier@3.6.2):
    eslint-plugin-prettier: public
  eslint-plugin-prettier@5.5.4(eslint-config-prettier@10.1.8(eslint@9.33.0))(eslint@9.33.0)(prettier@3.6.2):
    eslint-plugin-prettier: public
  eslint-scope@8.4.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: public
  espree@10.4.0:
    espree: public
  esquery@1.6.0:
    esquery: public
  esrecurse@4.3.0:
    esrecurse: public
  estraverse@5.3.0:
    estraverse: public
  estree-walker@2.0.2:
    estree-walker: public
  esutils@2.0.3:
    esutils: public
  eventemitter3@5.0.1:
    eventemitter3: public
  exponential-backoff@3.1.2:
    exponential-backoff: public
  extract-zip@2.0.1:
    extract-zip: public
  fast-deep-equal@3.1.3:
    fast-deep-equal: public
  fast-diff@1.3.0:
    fast-diff: public
  fast-glob@3.3.3:
    fast-glob: public
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: public
  fast-levenshtein@2.0.6:
    fast-levenshtein: public
  fastq@1.19.1:
    fastq: public
  fd-slicer@1.1.0:
    fd-slicer: public
  fdir@6.5.0(picomatch@4.0.3):
    fdir: public
  file-entry-cache@8.0.0:
    file-entry-cache: public
  filelist@1.0.4:
    filelist: public
  fill-range@7.1.1:
    fill-range: public
  find-up@5.0.0:
    find-up: public
  flat-cache@4.0.1:
    flat-cache: public
  flatted@3.3.3:
    flatted: public
  foreground-child@3.3.1:
    foreground-child: public
  form-data@4.0.4:
    form-data: public
  fs-constants@1.0.0:
    fs-constants: public
  fs-extra@10.1.0:
    fs-extra: public
  fs-minipass@2.1.0:
    fs-minipass: public
  fs.realpath@1.0.0:
    fs.realpath: public
  function-bind@1.1.2:
    function-bind: public
  gauge@4.0.4:
    gauge: public
  gensync@1.0.0-beta.2:
    gensync: public
  get-caller-file@2.0.5:
    get-caller-file: public
  get-east-asian-width@1.3.0:
    get-east-asian-width: public
  get-intrinsic@1.3.0:
    get-intrinsic: public
  get-proto@1.0.1:
    get-proto: public
  get-stream@5.2.0:
    get-stream: public
  glob-parent@6.0.2:
    glob-parent: public
  glob@10.4.5:
    glob: public
  global-agent@3.0.0:
    global-agent: public
  globals@16.3.0:
    globals: public
  globalthis@1.0.4:
    globalthis: public
  gopd@1.2.0:
    gopd: public
  got@11.8.6:
    got: public
  graceful-fs@4.2.11:
    graceful-fs: public
  graphemer@1.4.0:
    graphemer: public
  has-flag@4.0.0:
    has-flag: public
  has-property-descriptors@1.0.2:
    has-property-descriptors: public
  has-symbols@1.1.0:
    has-symbols: public
  has-tostringtag@1.0.2:
    has-tostringtag: public
  has-unicode@2.0.1:
    has-unicode: public
  hasown@2.0.2:
    hasown: public
  he@1.2.0:
    he: public
  hosted-git-info@4.1.0:
    hosted-git-info: public
  http-cache-semantics@4.2.0:
    http-cache-semantics: public
  http-proxy-agent@7.0.2:
    http-proxy-agent: public
  http2-wrapper@1.0.3:
    http2-wrapper: public
  https-proxy-agent@7.0.6:
    https-proxy-agent: public
  humanize-ms@1.2.1:
    humanize-ms: public
  iconv-lite@0.6.3:
    iconv-lite: public
  ieee754@1.2.1:
    ieee754: public
  ignore@5.3.2:
    ignore: public
  import-fresh@3.3.1:
    import-fresh: public
  imurmurhash@0.1.4:
    imurmurhash: public
  indent-string@4.0.0:
    indent-string: public
  infer-owner@1.0.4:
    infer-owner: public
  inflight@1.0.6:
    inflight: public
  inherits@2.0.4:
    inherits: public
  ip-address@10.0.1:
    ip-address: public
  is-ci@3.0.1:
    is-ci: public
  is-extglob@2.1.1:
    is-extglob: public
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: public
  is-glob@4.0.3:
    is-glob: public
  is-interactive@1.0.0:
    is-interactive: public
  is-lambda@1.0.1:
    is-lambda: public
  is-number@7.0.0:
    is-number: public
  is-unicode-supported@0.1.0:
    is-unicode-supported: public
  isarray@1.0.0:
    isarray: public
  isbinaryfile@5.0.4:
    isbinaryfile: public
  isexe@2.0.0:
    isexe: public
  jackspeak@3.4.3:
    jackspeak: public
  jake@10.9.4:
    jake: public
  jiti@2.5.1:
    jiti: public
  js-tokens@4.0.0:
    js-tokens: public
  js-yaml@4.1.0:
    js-yaml: public
  jsesc@3.1.0:
    jsesc: public
  json-buffer@3.0.1:
    json-buffer: public
  json-schema-traverse@0.4.1:
    json-schema-traverse: public
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: public
  json-stringify-safe@5.0.1:
    json-stringify-safe: public
  json5@2.2.3:
    json5: public
  jsonfile@6.2.0:
    jsonfile: public
  keyv@4.5.4:
    keyv: public
  lazy-val@1.0.5:
    lazy-val: public
  lazystream@1.0.1:
    lazystream: public
  levn@0.4.1:
    levn: public
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: public
  lightningcss@1.30.1:
    lightningcss: public
  lilconfig@3.1.3:
    lilconfig: public
  listr2@9.0.1:
    listr2: public
  locate-path@6.0.0:
    locate-path: public
  lodash.defaults@4.2.0:
    lodash.defaults: public
  lodash.difference@4.5.0:
    lodash.difference: public
  lodash.flatten@4.4.0:
    lodash.flatten: public
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: public
  lodash.merge@4.6.2:
    lodash.merge: public
  lodash.union@4.6.0:
    lodash.union: public
  lodash@4.17.21:
    lodash: public
  log-symbols@4.1.0:
    log-symbols: public
  log-update@6.1.0:
    log-update: public
  lowercase-keys@2.0.0:
    lowercase-keys: public
  lru-cache@5.1.1:
    lru-cache: public
  magic-string@0.30.17:
    magic-string: public
  make-fetch-happen@10.2.1:
    make-fetch-happen: public
  matcher@3.0.0:
    matcher: public
  math-intrinsics@1.1.0:
    math-intrinsics: public
  merge2@1.4.1:
    merge2: public
  micromatch@4.0.8:
    micromatch: public
  mime-db@1.52.0:
    mime-db: public
  mime-types@2.1.35:
    mime-types: public
  mime@2.6.0:
    mime: public
  mimic-fn@2.1.0:
    mimic-fn: public
  mimic-function@5.0.1:
    mimic-function: public
  mimic-response@3.1.0:
    mimic-response: public
  minimatch@3.1.2:
    minimatch: public
  minimist@1.2.8:
    minimist: public
  minipass-collect@1.0.2:
    minipass-collect: public
  minipass-fetch@2.1.2:
    minipass-fetch: public
  minipass-flush@1.0.5:
    minipass-flush: public
  minipass-pipeline@1.2.4:
    minipass-pipeline: public
  minipass-sized@1.0.3:
    minipass-sized: public
  minipass@5.0.0:
    minipass: public
  minizlib@2.1.2:
    minizlib: public
  mkdirp@1.0.4:
    mkdirp: public
  ms@2.1.3:
    ms: public
  muggle-string@0.4.1:
    muggle-string: public
  nano-spawn@1.0.2:
    nano-spawn: public
  nanoid@3.3.11:
    nanoid: public
  natural-compare@1.4.0:
    natural-compare: public
  negotiator@0.6.4:
    negotiator: public
  node-abi@3.75.0:
    node-abi: public
  node-api-version@0.2.1:
    node-api-version: public
  node-gyp@9.4.1:
    node-gyp: public
  node-releases@2.0.19:
    node-releases: public
  nopt@6.0.0:
    nopt: public
  normalize-path@3.0.0:
    normalize-path: public
  normalize-url@6.1.0:
    normalize-url: public
  npmlog@6.0.2:
    npmlog: public
  nth-check@2.1.1:
    nth-check: public
  object-keys@1.1.1:
    object-keys: public
  once@1.4.0:
    once: public
  onetime@5.1.2:
    onetime: public
  onetime@7.0.0:
    onetime: public
  optionator@0.9.4:
    optionator: public
  ora@5.4.1:
    ora: public
  p-cancelable@2.1.1:
    p-cancelable: public
  p-limit@3.1.0:
    p-limit: public
  p-locate@5.0.0:
    p-locate: public
  p-map@4.0.0:
    p-map: public
  package-json-from-dist@1.0.1:
    package-json-from-dist: public
  parent-module@1.0.1:
    parent-module: public
  path-browserify@1.0.1:
    path-browserify: public
  path-exists@4.0.0:
    path-exists: public
  path-is-absolute@1.0.1:
    path-is-absolute: public
  path-key@3.1.1:
    path-key: public
  path-scurry@1.11.1:
    path-scurry: public
  pe-library@0.4.1:
    pe-library: public
  pend@1.2.0:
    pend: public
  picocolors@1.1.1:
    picocolors: public
  picomatch@4.0.3:
    picomatch: public
  pidtree@0.6.0:
    pidtree: public
  plist@3.1.0:
    plist: public
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: public
  postcss@8.5.6:
    postcss: public
  prelude-ls@1.2.1:
    prelude-ls: public
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: public
  process-nextick-args@2.0.1:
    process-nextick-args: public
  progress@2.0.3:
    progress: public
  promise-inflight@1.0.1:
    promise-inflight: public
  promise-retry@2.0.1:
    promise-retry: public
  pump@3.0.3:
    pump: public
  punycode@2.3.1:
    punycode: public
  queue-microtask@1.2.3:
    queue-microtask: public
  quick-lru@5.1.1:
    quick-lru: public
  read-binary-file-arch@1.0.6:
    read-binary-file-arch: public
  readable-stream@3.6.2:
    readable-stream: public
  readdir-glob@1.1.3:
    readdir-glob: public
  require-directory@2.1.1:
    require-directory: public
  resedit@1.7.2:
    resedit: public
  resolve-alpn@1.2.1:
    resolve-alpn: public
  resolve-from@4.0.0:
    resolve-from: public
  responselike@2.0.1:
    responselike: public
  restore-cursor@3.1.0:
    restore-cursor: public
  restore-cursor@5.1.0:
    restore-cursor: public
  retry@0.12.0:
    retry: public
  reusify@1.1.0:
    reusify: public
  rfdc@1.4.1:
    rfdc: public
  rimraf@3.0.2:
    rimraf: public
  roarr@2.15.4:
    roarr: public
  rollup@4.46.2:
    rollup: public
  run-parallel@1.2.0:
    run-parallel: public
  safe-buffer@5.1.2:
    safe-buffer: public
  safer-buffer@2.1.2:
    safer-buffer: public
  sanitize-filename@1.6.3:
    sanitize-filename: public
  sax@1.4.1:
    sax: public
  semver-compare@1.0.0:
    semver-compare: public
  semver@7.7.2:
    semver: public
  serialize-error@7.0.1:
    serialize-error: public
  set-blocking@2.0.0:
    set-blocking: public
  shebang-command@2.0.0:
    shebang-command: public
  shebang-regex@3.0.0:
    shebang-regex: public
  signal-exit@4.1.0:
    signal-exit: public
  simple-update-notifier@2.0.0:
    simple-update-notifier: public
  slice-ansi@5.0.0:
    slice-ansi: public
  smart-buffer@4.2.0:
    smart-buffer: public
  socks-proxy-agent@7.0.0:
    socks-proxy-agent: public
  socks@2.8.7:
    socks: public
  source-map-js@1.2.1:
    source-map-js: public
  source-map-support@0.5.21:
    source-map-support: public
  source-map@0.6.1:
    source-map: public
  sprintf-js@1.1.3:
    sprintf-js: public
  ssri@9.0.1:
    ssri: public
  stat-mode@1.0.0:
    stat-mode: public
  string-argv@0.3.2:
    string-argv: public
  string-width@4.2.3:
    string-width: public
    string-width-cjs: public
  string_decoder@1.1.1:
    string_decoder: public
  strip-ansi@6.0.1:
    strip-ansi: public
    strip-ansi-cjs: public
  strip-json-comments@3.1.1:
    strip-json-comments: public
  sumchecker@3.0.1:
    sumchecker: public
  supports-color@7.2.0:
    supports-color: public
  synckit@0.11.11:
    synckit: public
  tapable@2.2.2:
    tapable: public
  tar-stream@2.2.0:
    tar-stream: public
  tar@6.2.1:
    tar: public
  tar@7.4.3:
    tar: public
  temp-file@3.4.0:
    temp-file: public
  tinyglobby@0.2.14:
    tinyglobby: public
  tmp-promise@3.0.3:
    tmp-promise: public
  tmp@0.2.5:
    tmp: public
  to-regex-range@5.0.1:
    to-regex-range: public
  truncate-utf8-bytes@1.0.2:
    truncate-utf8-bytes: public
  ts-api-utils@2.1.0(typescript@5.9.2):
    ts-api-utils: public
  type-check@0.4.0:
    type-check: public
  type-fest@0.13.1:
    type-fest: public
  typescript-eslint@8.39.1(eslint@9.33.0(jiti@2.5.1))(typescript@5.9.2):
    typescript-eslint: public
  typescript-eslint@8.39.1(eslint@9.33.0)(typescript@5.9.2):
    typescript-eslint: public
  undici-types@6.21.0:
    undici-types: public
  unique-filename@2.0.1:
    unique-filename: public
  unique-slug@3.0.0:
    unique-slug: public
  universalify@2.0.1:
    universalify: public
  update-browserslist-db@1.1.3(browserslist@4.25.2):
    update-browserslist-db: public
  uri-js@4.4.1:
    uri-js: public
  utf8-byte-length@1.0.5:
    utf8-byte-length: public
  util-deprecate@1.0.2:
    util-deprecate: public
  vscode-uri@3.1.0:
    vscode-uri: public
  wcwidth@1.0.1:
    wcwidth: public
  which@2.0.2:
    which: public
  wide-align@1.1.5:
    wide-align: public
  word-wrap@1.2.5:
    word-wrap: public
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: public
  wrap-ansi@9.0.0:
    wrap-ansi: public
  wrappy@1.0.2:
    wrappy: public
  xml-name-validator@4.0.0:
    xml-name-validator: public
  xmlbuilder@15.1.1:
    xmlbuilder: public
  y18n@5.0.8:
    y18n: public
  yallist@4.0.0:
    yallist: public
  yaml@2.8.1:
    yaml: public
  yargs-parser@21.1.1:
    yargs-parser: public
  yargs@17.7.2:
    yargs: public
  yauzl@2.10.0:
    yauzl: public
  yocto-queue@0.1.0:
    yocto-queue: public
  zip-stream@4.1.1:
    zip-stream: public
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Fri, 15 Aug 2025 02:39:07 GMT
publicHoistPattern:
  - '*'
registries:
  '@jsr': https://npm.jsr.io/
  '@scope': https://registry.npm.wps.cn/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.9'
  - '@esbuild/android-arm64@0.25.9'
  - '@esbuild/android-arm@0.25.9'
  - '@esbuild/android-x64@0.25.9'
  - '@esbuild/darwin-arm64@0.25.9'
  - '@esbuild/darwin-x64@0.25.9'
  - '@esbuild/freebsd-arm64@0.25.9'
  - '@esbuild/freebsd-x64@0.25.9'
  - '@esbuild/linux-arm64@0.25.9'
  - '@esbuild/linux-arm@0.25.9'
  - '@esbuild/linux-ia32@0.25.9'
  - '@esbuild/linux-loong64@0.25.9'
  - '@esbuild/linux-mips64el@0.25.9'
  - '@esbuild/linux-ppc64@0.25.9'
  - '@esbuild/linux-riscv64@0.25.9'
  - '@esbuild/linux-s390x@0.25.9'
  - '@esbuild/linux-x64@0.25.9'
  - '@esbuild/netbsd-arm64@0.25.9'
  - '@esbuild/netbsd-x64@0.25.9'
  - '@esbuild/openbsd-arm64@0.25.9'
  - '@esbuild/openbsd-x64@0.25.9'
  - '@esbuild/openharmony-arm64@0.25.9'
  - '@esbuild/sunos-x64@0.25.9'
  - '@esbuild/win32-arm64@0.25.9'
  - '@esbuild/win32-ia32@0.25.9'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-arm64-gnu@4.46.2'
  - '@rollup/rollup-linux-arm64-musl@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-musl@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - '@tailwindcss/oxide-android-arm64@4.1.12'
  - '@tailwindcss/oxide-darwin-arm64@4.1.12'
  - '@tailwindcss/oxide-darwin-x64@4.1.12'
  - '@tailwindcss/oxide-freebsd-x64@4.1.12'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.12'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.12'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.12'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.12'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.12'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.12'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.12'
  - '@types/plist@3.0.5'
  - '@types/verror@1.10.11'
  - assert-plus@1.0.0
  - astral-regex@2.0.0
  - cli-truncate@2.1.0
  - core-util-is@1.0.2
  - crc@3.8.0
  - dmg-license@1.0.11
  - extsprintf@1.4.1
  - fsevents@2.3.3
  - iconv-corefoundation@1.1.7
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - node-addon-api@1.7.2
  - slice-ansi@3.0.0
  - verror@1.10.1
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\Code\zhuyuqian1\electron-im\node_modules\.pnpm
virtualStoreDirMaxLength: 60
