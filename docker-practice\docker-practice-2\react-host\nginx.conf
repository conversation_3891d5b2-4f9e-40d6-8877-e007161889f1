server {
  listen 80;
  server_name localhost;

  location / {
    root /usr/share/nginx/html;
    try_files $uri /index.html;
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

  }

  # location /react-sub/ {
  #   proxy_pass http://react-sub:3001/;
  # }

  # location /vue-sub/ {
  #   proxy_pass http://vue-sub:3002/; 
  # }
}