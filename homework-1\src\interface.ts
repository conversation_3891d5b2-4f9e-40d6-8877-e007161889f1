export interface Contact {
  name: string
  department_show: string
  avatar: string
  id: number
  [key: string]: string | number | boolean
}

export interface MentionListResponse {
  contacts: {
    list: Contact[]
  }
}

export interface MentionItem {
  id: number
  value: string
  avatar?: string
  department_show?: string
  mentionChar: string
  link?: string
  file_src?: string
  ctime?: number
  app_type?: string
}
