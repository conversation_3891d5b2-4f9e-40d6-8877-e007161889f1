import type { DeviceImplementation, RemoteControl } from './types'

export class TV implements DeviceImplementation {
  private volume = 10
  private channel = 1
  private poweredOn = false

  getName(): string {
    return 'TV'
  }

  setVolume(percent: number): void {
    if (!this.poweredOn) return
    this.volume = Math.max(0, Math.min(100, percent))
    console.log(`TV volume set to ${this.volume}%`)
  }

  getVolume(): number {
    return this.volume
  }

  setChannel(channel: number): void {
    if (!this.poweredOn) return
    this.channel = Math.max(1, Math.min(999, channel))
    console.log(`TV channel set to ${this.channel}`)
  }

  getChannel(): number {
    return this.channel
  }

  togglePower(): void {
    this.poweredOn = !this.poweredOn
    console.log(`TV is now ${this.poweredOn ? 'on' : 'off'}`)
  }
}

// 收音机实现
export class Radio implements DeviceImplementation {
  private volume = 30
  private channel = 88.5
  private poweredOn = false

  getName(): string {
    return 'Radio'
  }

  setVolume(percent: number): void {
    if (!this.poweredOn) return
    this.volume = Math.max(0, Math.min(100, percent))
    console.log(`Radio volume set to ${this.volume}%`)
  }

  getVolume(): number {
    return this.volume
  }

  setChannel(channel: number): void {
    if (!this.poweredOn) return
    this.channel = Math.max(88.0, Math.min(108.0, channel))
    console.log(`Radio tuned to ${this.channel.toFixed(1)} FM`)
  }

  getChannel(): number {
    return this.channel
  }

  togglePower(): void {
    this.poweredOn = !this.poweredOn
    console.log(`Radio is now ${this.poweredOn ? 'on' : 'off'}`)
  }
}

export class BasicRemoteControl implements RemoteControl {
  constructor(protected device: DeviceImplementation) {}

  togglePower(): void {
    this.device.togglePower()
  }

  volumeUp(): void {
    const volume = this.device.getVolume()
    this.device.setVolume(volume + 10)
  }

  volumeDown(): void {
    const volume = this.device.getVolume()
    this.device.setVolume(volume - 10)
  }

  channelUp(): void {
    const channel = this.device.getChannel()
    this.device.setChannel(channel + 1)
  }

  channelDown(): void {
    const channel = this.device.getChannel()
    this.device.setChannel(channel - 1)
  }
}

// 高级遥控器扩展
export class AdvancedRemoteControl extends BasicRemoteControl {
  mute(): void {
    this.device.setVolume(0)
    console.log(`${this.device.getName()} muted`)
  }

  presetChannel(channel: number): void {
    this.device.setChannel(channel)
    console.log(`Set ${this.device.getName()} to preset channel ${channel}`)
  }
}
