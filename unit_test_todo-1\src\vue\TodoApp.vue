<template>
  <div class="todo-app">
    <h1>TODO List</h1>
    
    <TodoInput 
      @submit="handleAddTodo"
      @cancel="handleCancel"
      ref="inputRef"
    />
    
    <div class="todo-filters">
      <button 
        v-for="filter in filters"
        :key="filter.value"
        @click="currentFilter = filter.value"
        :class="['filter-btn', { active: currentFilter === filter.value }]"
      >
        {{ filter.label }}
      </button>
    </div>
    
    <div class="todo-list">
      <TodoItem
        v-for="todo in filteredTodos"
        :key="todo.id"
        :todo="todo"
        :show-date="showDate"
        @toggle="handleToggleTodo"
        @delete="handleDeleteTodo"
        @edit="handleEditTodo"
      />
      <div v-if="filteredTodos.length === 0" class="empty-state">
        {{ getEmptyStateMessage() }}
      </div>
    </div>
    
    <TodoStats 
      :stats="stats"
      @clear-completed="handleClearCompleted"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useTodoStore } from './composables/useTodoStore.js'
import { filterTodos, sortTodos, getTodoStats } from './utils/todoUtils.js'
import TodoInput from './components/TodoInput.vue'
import TodoItem from './components/TodoItem.vue'
import TodoStats from './components/TodoStats.vue'

const {
  todos,
  addTodo,
  toggleTodo,
  deleteTodo,
  clearCompleted,
  updateTodo,
  uncompletedCount,
  completedCount,
  totalCount
} = useTodoStore()

const currentFilter = ref('all')
const showDate = ref(false)
const inputRef = ref(null)

const filters = [
  { label: '全部', value: 'all' },
  { label: '进行中', value: 'active' },
  { label: '已完成', value: 'completed' }
]

const filteredTodos = computed(() => {
  const filtered = filterTodos(todos.value, currentFilter.value)
  return sortTodos(filtered, 'created')
})

const stats = computed(() => {
  return getTodoStats(todos.value)
})

const handleAddTodo = (text) => {
  const success = addTodo(text)
  if (success) {
    // 可以添加成功提示
  }
}

const handleCancel = () => {
  // 处理取消操作
}

const handleToggleTodo = (id) => {
  toggleTodo(id)
}

const handleDeleteTodo = (id) => {
  const success = deleteTodo(id)
  if (success) {
    // 可以添加删除成功提示
  }
}

const handleEditTodo = (todo) => {
  // 实现编辑功能
  console.log('编辑待办项:', todo)
}

const handleClearCompleted = () => {
  const clearedCount = clearCompleted()
  if (clearedCount > 0) {
    // 可以添加清除成功提示
  }
}

const getEmptyStateMessage = () => {
  switch (currentFilter.value) {
    case 'active':
      return '没有进行中的待办项'
    case 'completed':
      return '没有已完成的待办项'
    default:
      return '还没有待办项，开始添加吧！'
  }
}

onMounted(() => {
  // 自动聚焦到输入框
  inputRef.value?.focus()
})
</script>

<style scoped>
.todo-app {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.todo-filters {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
}

.filter-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  background-color: #f8f9fa;
}

.filter-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.todo-list {
  margin-bottom: 20px;
  min-height: 200px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  font-style: italic;
}
</style> 