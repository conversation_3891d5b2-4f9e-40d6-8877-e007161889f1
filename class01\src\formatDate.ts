export function formatDate(date: Date, format: string = 'yyyy-MM-dd HH:mm:ss'): string {
    const pad = (n: number) => n.toString().padStart(2, '0');
    return format
        .replace(/yyyy/g, date.getFullYear().toString())
        .replace(/MM/g, pad(date.getMonth() + 1))
        .replace(/dd/g, pad(date.getDate()))
        .replace(/HH/g, pad(date.getHours()))
        .replace(/mm/g, pad(date.getMinutes()))
        .replace(/ss/g, pad(date.getSeconds()));
}