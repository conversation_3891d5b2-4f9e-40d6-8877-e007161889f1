import type { RadioField as RadioFieldConfig } from "@/types/form";

interface RadioFieldProps {
  field: RadioFieldConfig;
  value: string;
  error?: string;
  onChange: (value: string) => void;
}

export function RadioField({ field, value, error, onChange }: RadioFieldProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  return (
    <div className="space-y-2">
      <fieldset className="flex flex-col space-y-2">
        {field.options.map((option) => (
          <div key={option.value} className="flex items-center space-x-2">
            <input
              type="radio"
              id={`${field.id}-${option.value}`}
              name={field.id} // 同一组radio需要相同的name
              value={option.value}
              checked={value === option.value}
              onChange={handleChange}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
            />
            <label
              htmlFor={`${field.id}-${option.value}`}
              className="text-sm cursor-pointer"
            >
              {option.label}
            </label>
          </div>
        ))}
      </fieldset>

      {error && <div className="text-sm text-red-500">{error}</div>}
    </div>
  );
}
