const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require("electron");

// 安全地暴露 API 给渲染进程
contextBridge.exposeInMainWorld("sdk", {
  readFile: (options) => ipcRenderer.invoke("read-file", options),
  openUrl: (options) => ipcRenderer.invoke("open-url", options),
});

contextBridge.exposeInMainWorld("electronAPI", {
  showFileDialog: () => ipcRenderer.invoke("file-dialog:open-file"), // : 区分模块和具体操作
  onOpenInWebview: (callback) =>
    ipcRenderer.on("open-in-webview", (event, url) => callback(url)),
});

console.log("preload脚本已加载");
