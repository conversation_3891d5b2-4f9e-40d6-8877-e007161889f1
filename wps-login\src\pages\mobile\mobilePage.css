.mobile-page-wrapper {
  min-height: 100vh;
  width: 100%;
  padding: 15px 15px; /* 增加顶部padding */
  box-sizing: border-box;
  overflow-y: auto;
}

.mobile-page {
  width: 100%;
  max-width: 260px;
  margin: 0 auto; /* 水平居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mobile-logo {
  width: 100%;
  max-width: 260px;
  height: auto;
  aspect-ratio: 260 / 160;
  background-image: url(@/assets/mobile_logo.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  margin-bottom: 20px;
}

/* 登录容器 */
.mobile-login-container {
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 其他登录选项 */
.other-login-options {
  margin-top: 5px;
}
.other-login-options-text {
  text-align: center;
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.other-login-toggle {
  width: 100%;
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  padding: 10px 0;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.other-login-methods {
  display: flex;
  justify-content: space-around;
  margin-top: 15px;
}

.login-method {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.login-method-icon-mobile {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: 1px solid #f0f0f0;
  background-image: url(@/assets/login_mobile.png);
  background-repeat: no-repeat;
  background-size: 44px 752px;
  margin-bottom: 5px;
  transition: all 0.2s;
}

.wechat {
  background-position: 0 -388px;
}

.qq {
  background-position: 0 -432px;
}

.company {
  background-position: 0 -476px;
}

.apple {
  background-position: 0 -520px;
}

.icon-more {
  background-position: 0 -564px;
}

.login-method-text {
  font-size: 12px;
  color: #666;
}

/* 页脚 */

/* 复选框样式 */
.checkbox-item-mobile {
  display: flex;
  align-items: center;
  margin-top: 15px;
  padding: 0 10px;
}

.checkbox-mobile {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.checkbox-label-moblie {
  font-size: 12px;
  color: #666;
  cursor: pointer;
  user-select: none;
  text-wrap: nowrap;
}

.checkbox-label-moblie a {
  color: #3b82f6;
  text-decoration: none;
}

.checkbox-label-moblie a:hover {
  text-decoration: underline;
}

/* 账号选择页面样式 */
.mobile-account-select {
  width: 100%;
}

.account-select-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
  height: 30px; /* 添加固定高度 */
}

.back-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 5px;
  color: #333;
  position: absolute;
  left: 0;
  top: 0;
}

.account-select-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: auto; /* 自适应宽度 */
  text-align: center;
  white-space: nowrap; /* 防止文字换行 */
}

.account-select-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  text-align: center;
  font-weight: normal;
}

.accounts-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.account-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
}

.account-item.selected {
  border-color: #2563eb;
  background-color: #f0f7ff;
}

.account-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #fff;
  margin-right: 10px;
  overflow: hidden;
}

.account-info {
  flex: 1;
}

.account-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.account-user {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.account-status {
  font-size: 12px;
  color: #f44336;
  margin-top: 2px;
}

.account-check {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #2563eb;
  font-weight: bold;
}

.account-footer {
  margin-top: 20px;
}

.selected-info {
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
}

.confirm-login-btn {
  width: 100%;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
  cursor: pointer;
}

.confirm-login-btn:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}
