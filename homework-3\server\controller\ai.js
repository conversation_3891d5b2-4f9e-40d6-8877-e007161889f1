const axios = require("../libs/axios");
const crypto = require("crypto");

const ARK_API_KEY =
  process.env.ARK_API_KEY || "f88c8bce-305b-4955-8c48-bb71a8ec8938";
const API_BASE_URL =
  "https://ark.cn-beijing.volces.com/api/v3/chat/completions";

function generateActionId() {
  return crypto.randomBytes(16).toString("hex");
}

// 非流式响应处理
async function generateMindMap(ctx) {
  // 添加请求超时和连接池管理
  const timeout = setTimeout(() => {
    ctx.res.destroy(); // 强制关闭连接
  }, 30000);

  ctx.set("Access-Control-Allow-Origin", "*");
  ctx.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  ctx.set("Access-Control-Allow-Headers", "Content-Type");

  if (ctx.method === "OPTIONS") {
    ctx.status = 200;
    return;
  }

  let { mainNode = "" } = ctx.request.body;
  mainNode = mainNode.trim();

  if (!mainNode) {
    ctx.status = 400;
    ctx.body = { code: -1, message: "主节点内容为空" };
    return;
  }

  const prompt = `你的任务是依据给定的一级节点内容，生成符合特定格式的思维导图数据。请认真研读以下一级节点内容，并按照相关指示生成对应的二级和三级节点数据。
一级节点内容:${mainNode}
请遵循以下准则：
1. 二级节点应紧密围绕一级节点展开，是对一级节点的合理的细分。
2. 三级节点应是对二级节点的进一步深入细化与补充。
3. 要确保各节点之间逻辑清晰、层次分明且具有紧密的关联性。
请生成的思维导图数据格式如下：每个节点均包含id、level、text、parentId属性，直接返回JSON数组，不要其他文字说明：
[
    {
        "id": "root",
        "text": "${mainNode}",
        "level": 1,
        "parentId": null
    },
    {
        "id": "二级节点id",
        "text": "分支主题",
        "level": 2,
        "parentId": "root"
    }
]`;

  try {
    const response = await axios.post(
      API_BASE_URL,
      {
        messages: [
          {
            content: "你是一个生成思维导图的助手，只返回JSON格式数据",
            role: "system",
          },
          {
            content: prompt,
            role: "user",
          },
        ],
        model: "doubao-1-5-lite-32k-250115",
        stream: false, // 改为非流式
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${ARK_API_KEY}`,
        },
        // 移除 responseType: "stream"
      }
    );

    clearTimeout(timeout);
    const content = response.choices[0].message.content;
    const jsonMatch = content.match(/\[[\s\S]*?\]/);

    if (jsonMatch) {
      const mindMapData = JSON.parse(jsonMatch[0]);
      ctx.body = { code: 0, data: mindMapData };
    } else {
      ctx.body = { code: -1, message: "未生成有效的JSON数据" };
    }
  } catch (error) {
    clearTimeout(timeout);
    console.error("请求失败:", error);
    ctx.body = { code: -1, message: "请求失败" };
  }
}

// 流式响应处理
async function generateMindMapStream(ctx) {
  // 立即设置状态码和响应头
  ctx.respond = false; // 告诉Koa不要处理响应
  ctx.res.statusCode = 200;
  ctx.res.setHeader("Content-Type", "text/event-stream");
  ctx.res.setHeader("Cache-Control", "no-cache");
  ctx.res.setHeader("Connection", "keep-alive");
  ctx.res.setHeader("Access-Control-Allow-Origin", "*");

  // 立即写入响应头
  ctx.res.flushHeaders();

  // 添加请求超时和连接池管理
  const timeout = setTimeout(() => {
    ctx.res.destroy(); // 强制关闭连接
  }, 60000);

  let { mainNode = "" } = ctx.request.body;
  mainNode = mainNode.trim();

  if (!mainNode) {
    // 修改：直接写入错误响应，不使用ctx.status和ctx.body
    ctx.res.write(`event: error\n`);
    ctx.res.write(
      `data: ${JSON.stringify({ code: -1, message: "主节点内容为空" })}\n\n`
    );
    ctx.res.end();
    return;
  }

  const prompt = `你的任务是根据给定的一级节点内容，生成符合特定流式返回格式的思维导图数据。请认真研读以下内容，并按照相关指示生成对应的二级和三级节点数据。
一级节点内容:${mainNode}
请遵循以下准则：
1. 二级节点应紧密围绕一级节点展开，是对一级节点的合理的细分。
2. 三级节点应是对二级节点的进一步深入细化与补充。
3. 要确保各节点之间逻辑清晰、层次分明且具有紧密的关联性。
请生成的思维导图数据格式如下：每个节点均包含id、level、text、parentId属性，直接返回JSON数组，不要其他文字说明：
[
    {
        "id": "root",
        "text": "${mainNode}",
        "level": 1,
        "parentId": null
    },
    {
        "id": "二级节点id",
        "text": "主题",
        "level": 2,
        "parentId": "root"
    }
]`;

  try {
    const response = await axios.post(
      API_BASE_URL,
      {
        messages: [
          {
            content: "你是一个生成思维导图的助手，只返回JSON格式数据",
            role: "system",
          },
          {
            content: prompt,
            role: "user",
          },
        ],
        model: "doubao-1-5-lite-32k-250115",
        stream: true, // 启用流式
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${ARK_API_KEY}`,
        },
        responseType: "stream", // 设置为流式响应
      }
    );

    clearTimeout(timeout);

    // 处理流式响应
    await processStreamResponse(response, ctx.res);
  } catch (error) {
    clearTimeout(timeout);
    console.error("流式请求失败:", error);

    // 修改：不要设置ctx.status，直接写入错误事件
    ctx.res.write(`event: error\n`);
    ctx.res.write(`data: ${JSON.stringify({ message: "请求失败" })}\n\n`);
    ctx.res.end();
  }
}

// 处理流式响应
async function processStreamResponse(responseStream, outputStream) {
  let buffer = "";
  let fullContent = "";
  let nodeBuffer = ""; // 用于累积单个节点的JSON

  outputStream.write(`event: start\n`);
  outputStream.write(
    `data: ${JSON.stringify({ message: "开始生成思维导图" })}\n\n`
  );

  return new Promise((resolve, reject) => {
    responseStream.on("data", (chunk) => {
      try {
        buffer += chunk.toString();
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        lines.forEach((line) => {
          if (line.trim() === "") return;

          if (line.startsWith("data: ")) {
            const data = line.substring(6).trim();

            if (data === "[DONE]") {
              outputStream.write(`event: end\n`);
              outputStream.write(
                `data: ${JSON.stringify({ message: "生成完成" })}\n\n`
              );
              outputStream.end();
              resolve();
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.choices?.[0]?.delta?.content) {
                const content = parsed.choices[0].delta.content;
                fullContent += content;
                nodeBuffer += content;

                // 尝试解析完整的节点JSON
                const nodeMatches = nodeBuffer.match(/\{[^{}]*"id"[^{}]*\}/g);
                if (nodeMatches) {
                  nodeMatches.forEach((nodeJson) => {
                    try {
                      const node = JSON.parse(nodeJson);
                      outputStream.write(`event: node\n`);
                      outputStream.write(
                        `data: ${JSON.stringify({ node })}\n\n`
                      );

                      // 从buffer中移除已处理的节点
                      nodeBuffer = nodeBuffer.replace(nodeJson, "");
                    } catch (nodeParseError) {
                      // 节点JSON不完整，继续累积
                    }
                  });
                }
              }
            } catch (parseError) {
              console.error("解析响应数据时出错:", parseError);
            }
          }
        });
      } catch (error) {
        console.error("处理流数据时出错:", error);
        reject(error);
      }
    });

    responseStream.on("end", () => {
      outputStream.write(`event: end\n`);
      outputStream.write(
        `data: ${JSON.stringify({ message: "响应结束" })}\n\n`
      );
      outputStream.end();
      resolve(); // 正常结束，不要抛出错误
    });

    responseStream.on("error", (error) => {
      console.error("响应流错误:", error);
      reject(error);
    });
  });
}

module.exports = {
  generateMindMap,
  generateMindMapStream,
};
