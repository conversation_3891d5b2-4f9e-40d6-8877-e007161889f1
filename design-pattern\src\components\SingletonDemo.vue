<template>
  <div>
    <p>Counter: {{ store.counter }}</p>
    <button @click="increment">+</button>
    <button @click="decrement">-</button>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import SingletonStore from '@/utils/SingletonStore'

export default defineComponent({
  setup() {
    return {
      store: SingletonStore.getInstance(),
      increment: () => SingletonStore.getInstance().increment(),
      decrement: () => SingletonStore.getInstance().decrement(),
    }
  },
})
</script>

<style scoped></style>
