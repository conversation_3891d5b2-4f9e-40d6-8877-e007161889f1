<template>
  <div class="chinese-calendar">
    <div class="calendar-header">
      <button @click="previousMonth" class="nav-btn">‹</button>
      <div class="date-selectors">
        <el-select v-model="currentYear" size="small" style="width: 80px" @change="onYearChange">
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
        </el-select>
        <span class="year-text">年</span>
        <span class="month-text">{{ currentMonth }}月</span>
      </div>
      <button @click="nextMonth" class="nav-btn">›</button>
    </div>

    <div class="calendar-grid">
      <div class="calendar-weekdays">
        <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
      </div>
      <div class="calendar-days">
        <div
          v-for="date in calendarDays"
          :key="date.key"
          class="calendar-day"
          :class="{
            'other-month': !date.isCurrentMonth,
            selected: isSelectedDate(date.date),
            today: isToday(date.date),
          }"
          @click="selectDate(date.date)"
        >
          {{ date.day }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

interface Props {
  modelValue?: Date
}

interface Emits {
  (e: 'update:modelValue', value: Date): void
  (e: 'change', value: Date): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => new Date(),
})

const emit = defineEmits<Emits>()

// 状态
const selectedDate = ref(new Date(props.modelValue))
const currentDate = ref(new Date())
const currentYear = ref(new Date().getFullYear())

// 中文星期
const weekdays = ['日', '一', '二', '三', '四', '五', '六']

// 年份选项（当前年份前后各10年）
const yearOptions = computed(() => {
  const currentYearValue = new Date().getFullYear()
  const years = []
  for (let i = currentYearValue - 10; i <= currentYearValue + 10; i++) {
    years.push(i)
  }
  return years
})

// 当前月份
const currentMonth = computed(() => {
  return currentDate.value.getMonth() + 1
})

// 日历天数计算
const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const firstDay = new Date(year, month, 1)
  // const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())

  const days = []
  const current = new Date(startDate)

  for (let i = 0; i < 42; i++) {
    days.push({
      date: new Date(current),
      day: current.getDate(),
      isCurrentMonth: current.getMonth() === month,
      key: current.getTime(),
    })
    current.setDate(current.getDate() + 1)
  }

  return days
})

// 监听外部 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      selectedDate.value = new Date(newVal)
      // 更新当前显示的月份为选中日期的月份
      currentDate.value = new Date(newVal.getFullYear(), newVal.getMonth(), 1)
      currentYear.value = newVal.getFullYear()
    }
  },
)

// 监听当前日期变化，同步年份
watch(currentDate, (newDate) => {
  currentYear.value = newDate.getFullYear()
})

// 监听选中日期变化，向外发送事件
watch(selectedDate, (newDate) => {
  emit('update:modelValue', newDate)
  emit('change', newDate)
})

// 方法
function isSelectedDate(date: Date) {
  return date.toDateString() === selectedDate.value.toDateString()
}

function isToday(date: Date) {
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

function selectDate(date: Date) {
  selectedDate.value = new Date(date)
}

function previousMonth() {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
}

function nextMonth() {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
}

function onYearChange(year: number) {
  currentDate.value = new Date(year, currentDate.value.getMonth(), 1)
}
</script>

<style scoped>
.chinese-calendar {
  width: 100%;
  background: white;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
}

.date-selectors {
  display: flex;
  align-items: center;
  gap: 4px;
}

.year-text,
.month-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.nav-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background: #e6f7ff;
  color: #409eff;
}

.calendar-grid {
  width: 100%;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 4px;
}

.weekday {
  padding: 8px 4px;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.calendar-day {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  position: relative;
  border-radius: 4px;
  margin: 1px;
}

.calendar-day:hover {
  background-color: #f0f8ff;
  color: #409eff;
}

.calendar-day.other-month {
  color: #ccc;
  background-color: #fafafa;
}

.calendar-day.selected {
  background-color: #409eff;
  color: white;
  font-weight: 500;
}

.calendar-day.today {
  background-color: #e6f7ff;
  color: #1890ff;
  font-weight: 500;
}

.calendar-day.today::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #1890ff;
  border-radius: 50%;
}

.calendar-day.selected.today {
  background-color: #409eff;
  color: white;
}

.calendar-day.selected.today::after {
  background-color: white;
}
</style>
