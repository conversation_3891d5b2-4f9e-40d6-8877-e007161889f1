import { useState, useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
  useNavigate,
  Navigate,
} from "react-router-dom";
import LoginPage from "@/pages/pc/LoginPage";
import MobilePage from "@/pages/mobile/MobilePage";

// 响应式布局组件 - 根据屏幕大小自动切换
const ResponsiveLogin = () => {
  const [isMobileView, setIsMobileView] = useState(window.innerWidth < 640);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const syncViewAndUrl = () => {
      const shouldBeMobile = window.innerWidth < 640;
      const isCurrentlyOnMobilePath =
        location.pathname.includes("/v1/mobilelogin");

      setIsMobileView(shouldBeMobile);

      if (shouldBeMobile && !isCurrentlyOnMobilePath) {
        navigate("/v1/mobilelogin", { replace: true });
      } else if (!shouldBeMobile && isCurrentlyOnMobilePath) {
        navigate("/", { replace: true });
      }
    };

    syncViewAndUrl();

    window.addEventListener("resize", syncViewAndUrl);
    return () => window.removeEventListener("resize", syncViewAndUrl);
  }, [navigate, location.pathname]);

  return isMobileView ? <MobilePage /> : <LoginPage />;
};

// 路由重定向组件
const RouteRedirect = () => {
  const isMobile = window.innerWidth < 640;
  return <Navigate to={isMobile ? "/v1/mobilelogin" : "/"} replace />;
};

function App() {
  const basename = import.meta.env.BASE_URL;
  return (
    <Router basename={basename}>
      <Routes>
        <Route path="/" element={<ResponsiveLogin />} />
        <Route path="/v1/mobilelogin" element={<ResponsiveLogin />} />
        <Route path="*" element={<RouteRedirect />} />
      </Routes>
    </Router>
  );
}

export default App;
