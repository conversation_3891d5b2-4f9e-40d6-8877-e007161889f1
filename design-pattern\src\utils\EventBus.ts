/* eslint-disable @typescript-eslint/no-explicit-any */
type Callback = (...args: any[]) => void

class EventBus {
  private events: Record<string, Callback[]> = {}

  private constructor() {}

  public on(eventName: string, callback: Callback) {
    if (!this.events[eventName]) {
      this.events[eventName] = []
    }
    this.events[eventName].push(callback)
  }

  public off(eventName: string, callback?: Callback): void {
    if (!this.events[eventName]) return

    if (!callback) {
      delete this.events[eventName]
    } else {
      this.events[eventName] = this.events[eventName].filter((cb) => cb !== callback)
    }
  }

  public emit(eventName: string, ...args: any[]): void {
    if (!this.events[eventName]) return
    this.events[eventName].forEach((cb) => cb(...args))
  }
}

export const eventBus = new EventBus()
