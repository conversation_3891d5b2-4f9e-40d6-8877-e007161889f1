import { defineConfig, devices } from "@playwright/test";

export default defineConfig({
  timeout: 80000, // 单测试最大超时
  retries: 1, // 失败重试次数
  use: {
    locale: "zh-CN", // 让 navigator.language、Accept-Language 都是中文
    headless: false, // 可视化
  },
  // 多浏览器配置
  projects: [
    {
      name: "Chromium",
      use: { ...devices["Desktop Chrome"] },
    },
    {
      name: "Firefox",
      use: { ...devices["Desktop Firefox"] },
    },
  ],

  // 测试报告
  reporter: [
    ["list"], // 控制台报告
    ["html"], // HTML报告（运行后生成playwright-report）
  ],
});
