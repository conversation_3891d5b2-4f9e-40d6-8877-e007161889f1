<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        display: flex;
        justify-content: center;
        min-height: 100vh;
        background-color: #f5f5f5;
        padding: 0;
        overflow-x: hidden;
      }

      .container {
        width: 100%;
        max-width: 480px;
        min-width: 320px;
        background: linear-gradient(to bottom, #eff6ff, #faf5ff);
        display: flex;
        flex-direction: column;
        padding-top: 60px;
        padding-bottom: 60px;
        margin: 0 auto;
      }

      .text {
        color: #374151;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 16px;
        padding: 0 24px;
      }

      .highlight-text {
        color: #ef4444;
        font-weight: 500;
      }

      .search-container {
        position: fixed;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        max-width: 480px;
        min-width: 320px;
        display: flex;
        align-items: center;
        border-radius: 10px;
        background-color: white;
        padding: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        z-index: 100;
      }

      .search-icon {
        position: absolute;
        left: 16px;
        color: #7e7c7c;
        width: 24px;
        height: 24px;
      }

      .search-input {
        flex: 1;
        border: none;
        padding: 10px;
        padding-left: 35px;
        outline: none;
        font-size: 16px;
        min-width: 0;
      }

      .button-group {
        display: flex;
        margin-left: 8px;
        min-width: fit-content;
        white-space: nowrap;
      }

      .paste-btn {
        border: none;
        background-color: white;
        color: #7e7c7c;
        margin-right: 5px;
        padding: 10px;
        min-width: 40px;
        transition: background-color 0.2s;
      }

      .query-btn {
        border-radius: 20px;
        border: none;
        background: #8b5cf6;
        color: white;
        padding: 10px;
        cursor: pointer;
        transition: background-color 0.3s;
        min-width: 50px;
      }

      .paste-btn:hover {
        cursor: pointer;
        background-color: #f5f5f5;
      }

      .query-btn:hover {
        background-color: #3367d6;
      }

      .content {
        flex: 1;
        padding: 0 24px 24px;
      }

      .image-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        height: 100%;
        min-height: 200px;
      }

      .image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      .bottom-nav {
        background: white;
        border-top: 1px solid #e5e7eb;
        padding: 12px 24px;
        position: fixed;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        max-width: 480px;
        min-width: 320px;
        z-index: 100;
      }

      .nav-container {
        display: flex;
        justify-content: space-around;
        align-items: center;
      }

      .nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        text-decoration: none;
        color: inherit;
      }

      .nav-icon {
        width: 24px;
        height: 24px;
      }

      .nav-text {
        font-size: 12px;
        font-weight: 500;
      }

      .nav-item.active .nav-icon,
      .nav-item.active .nav-text {
        color: #374151;
      }

      .nav-item:not(.active) .nav-icon,
      .nav-item:not(.active) .nav-text {
        color: #9ca3af;
      }

      @media (max-width: 480px) {
        .search-container {
          width: 100%;
          padding: 8px;
        }

        .search-icon {
          left: 16px;
        }

        .bottom-nav {
          width: 100%;
        }
      }

      @media (max-width: 320px) {
        .container,
        .search-container,
        .bottom-nav {
          min-width: 320px;
          width: 320px;
        }

        body {
          min-width: 320px;
        }

        .search-input {
          max-width: 180px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="text">
        <span class="highlight-text">微信支付：</span>
        点击微信列表的「微信支付」进入交易记录，复制商户单号到当前页面进行查询
      </div>
      <div class="search-container">
        <svg class="search-icon" viewBox="0 0 1024 1024">
          <path
            d="M862.609 816.955L726.44 680.785l-0.059-0.056a358.907 358.907 0 0 0 56.43-91.927c18.824-44.507 28.369-91.767 28.369-140.467 0-48.701-9.545-95.96-28.369-140.467-18.176-42.973-44.19-81.56-77.319-114.689-33.13-33.129-71.717-59.144-114.69-77.32-44.507-18.825-91.767-28.37-140.467-28.37-48.701 0-95.96 9.545-140.467 28.37-42.973 18.176-81.56 44.19-114.689 77.32-33.13 33.129-59.144 71.717-77.32 114.689-18.825 44.507-28.37 91.767-28.37 140.467 0 48.7 9.545 95.96 28.37 140.467 18.176 42.974 44.19 81.561 77.32 114.69 33.129 33.129 71.717 59.144 114.689 77.319 44.507 18.824 91.767 28.369 140.467 28.369 48.7 0 95.96-9.545 140.467-28.369 32.78-13.864 62.997-32.303 90.197-54.968 0.063 0.064 0.122 0.132 0.186 0.195l136.169 136.17c6.25 6.25 14.438 9.373 22.628 9.373 8.188 0 16.38-3.125 22.627-9.372 12.496-12.496 12.496-32.758 0-45.254z m-412.274-69.466c-79.907 0-155.031-31.118-211.534-87.62-56.503-56.503-87.62-131.627-87.62-211.534s31.117-155.031 87.62-211.534c56.502-56.503 131.626-87.62 211.534-87.62s155.031 31.117 211.534 87.62c56.502 56.502 87.62 131.626 87.62 211.534s-31.118 155.031-87.62 211.534c-56.503 56.502-131.627 87.62-211.534 87.62z"
          ></path>
        </svg>
        <input
          type="text"
          class="search-input"
          placeholder="请输入搜索内容..."
        />
        <div class="button-group">
          <button class="paste-btn">粘贴</button>
          <button class="query-btn">查询</button>
        </div>
      </div>
      <div class="content">
        <div class="image-container">
          <img src="image.png" class="image" />
        </div>
      </div>
      <div class="text">
        <span class="highlight-text">支付宝支付：</span>
        点击支付宝列表的「账单」进入交易记录，复制商户单号到当前页面进行查询
      </div>
      <div class="content">
        <div class="image-container">
          <img src="image.png" class="image" />
        </div>
      </div>

      <div class="bottom-nav">
        <div class="nav-container">
          <a href="#" class="nav-item">
            <svg class="nav-icon" viewBox="0 0 1024 1024">
              <path
                d="M795.648 923.648H228.352c-58.368 0-105.472-47.104-107.52-104.448L81.92 360.448c0-38.912 21.504-74.752 55.296-93.184L450.56 93.184c38.912-21.504 84.992-21.504 123.904 0L887.808 266.24c33.792 18.432 55.296 54.272 55.296 93.184v3.072l-39.936 454.656c-2.048 59.392-49.152 106.496-107.52 106.496zM163.84 359.424l39.936 457.728c0 13.312 11.264 24.576 25.6 24.576h567.296c14.336 0 25.6-11.264 25.6-24.576v-3.072L860.16 359.424c0-8.192-5.12-16.384-13.312-20.48l-312.32-174.08c-14.336-8.192-30.72-8.192-44.032 0l-313.344 174.08c-8.192 4.096-13.312 11.264-13.312 20.48z"
                fill="#333C4F"
              ></path>
              <path
                d="M699.392 905.216h-81.92V611.328c0-11.264-6.144-17.408-8.192-17.408H413.696c-2.048 0-8.192 6.144-8.192 17.408v293.888h-81.92V611.328c0-55.296 39.936-99.328 90.112-99.328h195.584c49.152 0 90.112 45.056 90.112 99.328v293.888z"
                fill="#333C4F"
              ></path>
            </svg>
            <span class="nav-text">首页</span>
          </a>
          <a href="#" class="nav-item">
            <svg class="nav-icon" viewBox="0 0 1024 1024">
              <path
                d="M512 672c-176.4 0-320-143.5-320-320S335.6 32 512 32s320 143.5 320 320-143.6 320-320 320z m0-576c-141.2 0-256 114.8-256 256s114.8 256 256 256 256-114.8 256-256S653.2 96 512 96z"
                fill="#243154"
              ></path>
              <path
                d="M850.1 992H173.9c-20.3 0-38.9-9.3-51.2-25.5-12.2-16.1-16.1-36.5-10.6-55.9C162.5 732.4 327 608 512 608s349.5 124.4 399.9 302.5c5.5 19.4 1.6 39.8-10.6 56-12.3 16.2-31 25.5-51.2 25.5zM512 672c-156.5 0-295.7 105.3-338.3 256h676.4C807.7 777.3 668.5 672 512 672zM512 544c-60.9 0-116.9-28-153.6-76.8-10.6-14.1-7.8-34.2 6.4-44.8s34.2-7.8 44.8 6.4C434 461.3 471.4 480 512 480s78-18.7 102.4-51.2c10.6-14.1 30.7-16.9 44.8-6.4 14.1 10.6 17 30.7 6.4 44.8C628.9 516 573 544 512 544z"
                fill="#243154"
              ></path>
            </svg>
            <span class="nav-text">个人中心</span>
          </a>
        </div>
      </div>
    </div>
  </body>
</html>
