<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <script>
      class Book {
        constructor(title, author, isbn, publishYear) {
          this.title = title;
          this.author = author;
          this.isbn = isbn;
          this.publishYear = publishYear;
          this.isBorrowed = false; // 默认值为 false
        }

        // 借出图书
        borrowBook() {
          if (this.isBorrowed) {
            return "该书已借出";
          }
          this.isBorrowed = true;
        }

        // 归还图书
        returnBook() {
          if (!this.isBorrowed) {
            return "该书未借出";
          }
          this.isBorrowed = false;
        }

        // 获取图书信息
        getBookInfo() {
          const status = this.isBorrowed ? "已借出" : "未借出";
          return `书名：${this.title}，作者：${this.author}，ISBN：${this.isbn}，出版年份：${this.publishYear}，状态：${status}`;
        }
      }

      // 定义杂志类，继承自图书类
      class Magazine extends Book {
        constructor(title, author, isbn, publishYear, issueNumber) {
          super(title, author, isbn, publishYear);
          this.issueNumber = issueNumber;
        }

        // 重写获取图书信息方法，增加期号信息
        getBookInfo() {
          const status = this.isBorrowed ? "已借出" : "未借出";
          return `书名：${this.title}，作者：${this.author}，ISBN：${this.isbn}，出版年份：${this.publishYear}，期号：${this.issueNumber}，状态：${status}`;
        }
      }

      // 定义图书管理类
      class Library {
        constructor() {
          this.books = []; // 图书列表，用于存储所有图书和杂志实例
        }

        // 添加图书方法
        addBook(book) {
          this.books.push(book);
          return `《${book.title}》添加成功`;
        }

        // 根据书名查找图书
        findBookByTitle(title) {
          const foundBook = this.books.find((book) => book.title === title);
          return foundBook || null;
        }

        // 显示所有图书信息
        showAllBooks() {
          if (this.books.length === 0) {
            return "图书馆暂无藏书";
          }
          return this.books.map((book) => book.getBookInfo()).join("\n");
        }
      }

      console.log("\n=== 测试代码 ===");
      // 创建图书管理系统实例
      const library = new Library();

      // 1. 创建至少2本图书实例和1本杂志实例
      console.log("1. 创建图书和杂志实例：");
      const testBook1 = new Book(
        "JavaScript指南",
        "张三",
        "978-7-111-23456-7",
        2021
      );
      const testBook2 = new Book(
        "Python编程",
        "李四",
        "978-7-115-34567-8",
        2020
      );
      const testMagazine = new Magazine(
        "计算机杂志",
        "计算机杂志社",
        "978-7-111-45678-9",
        2024,
        "第12期"
      );

      console.log("创建图书1：", testBook1.getBookInfo());
      console.log("创建图书2：", testBook2.getBookInfo());
      console.log("创建杂志：", testMagazine.getBookInfo());

      // 2. 将这些实例添加到图书管理类实例中
      console.log("\n2. 添加图书和杂志到图书馆：");
      console.log(library.addBook(testBook1));
      console.log(library.addBook(testBook2));
      console.log(library.addBook(testMagazine));

      // 3. 对其中一本图书执行借出操作，再尝试借出一次
      console.log("\n3. 图书借出测试：");
      console.log("首次借出：", testBook1.borrowBook());
      console.log("图书状态：", testBook1.getBookInfo());
      console.log("再次借出：", testBook1.borrowBook());

      // 4. 对借出的图书执行归还操作，再尝试归还一次
      console.log("\n4. 图书归还测试：");
      console.log("归还图书：", testBook1.returnBook());
      console.log("图书状态：", testBook1.getBookInfo());
      console.log("再次归还：", testBook1.returnBook());

      // 5. 查找其中一本图书，并输出其信息
      console.log("\n5. 图书查找测试：");
      const foundBook = library.findBookByTitle("Python编程");
      if (foundBook) {
        console.log("查找成功：", foundBook.getBookInfo());
      } else {
        console.log("未找到指定图书");
      }

      // 查找不存在的图书
      const notFoundBook = library.findBookByTitle("不存在的图书");
      console.log("查找不存在的图书：", notFoundBook);

      // 6. 显示所有图书的信息
      console.log("\n6. 显示所有图书信息：");
      console.log(library.showAllBooks());
    </script>
  </body>
</html>
