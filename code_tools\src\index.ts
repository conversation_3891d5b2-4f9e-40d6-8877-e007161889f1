import axios from 'axios'

const baseUrl = 'https://kfpxy-fe.wps.cn'
const nameList: string[] = [
  'jindisheng',
  'keyan',
  'lian<PERSON><PERSON><PERSON><PERSON>',
  'yang<PERSON><PERSON><PERSON><PERSON>',
  'z<PERSON><PERSON><PERSON><PERSON>',
  'z<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  'r<PERSON><PERSON><PERSON><PERSON>',
  'lisiji<PERSON>',
]

const studentRouteRegex = /^[a-z]+[0-9]*$/
const suffixes = ['', '1', '2']

async function checkJobPage(route: string, displayName: string) {
  const url = `${baseUrl}/${route}/`
  const res = await axios.get(url, {
    timeout: 5000,
    headers: {
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/114.0 Safari/537.36', // 自定义 User-Agent
    },
    proxy: {
      '/woa/api': {
        target: 'http://127.0.0.1:8899', // 请求发给本地 Whistle 代理端口
        changeOrigin: true,
        secure: false, // Whistle 不校验证书
      },
    },
  })
  const ok = res.status === 200
  console.log(`${displayName} → ${route}: ${ok ? '页面可访问' : '页面异常'}`)
  return true
}
async function main() {
  console.log('开始检测 KAE 作业页面是否部署成功...\n')

  for (const name of nameList) {
    let found = false

    for (const suffix of suffixes) {
      const route = `${name}${suffix}`
      if (!studentRouteRegex.test(route)) continue

      const ok = await checkJobPage(route, name)
      if (ok) {
        found = true
        break
      }
    }

    if (!found) {
      console.log(`${name}: 未找到有效作业页面`)
    } else {
      console.log('') // 空行分隔每个学生的输出
    }
  }
}

main()
