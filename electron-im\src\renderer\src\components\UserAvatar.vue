<!-- 用户头像组件 -->
<template>
  <div
    class="rounded-lg flex items-center justify-center text-white font-medium relative"
    :style="avatarStyle"
  >
    {{ displayText }}
    <!-- 在线状态指示器 -->
    <div
      v-if="showOnlineStatus && isOnline"
      class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  name: string
  size?: 'small' | 'medium' | 'large'
  isOnline?: boolean
  showOnlineStatus?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  isOnline: false,
  showOnlineStatus: false
})

// 获取姓名的后两个字作为头像
const displayText = computed(() => {
  return props.name.length >= 2 ? props.name.slice(-2) : props.name
})

// 根据尺寸计算样式
const avatarStyle = computed(() => {
  const sizes = {
    small: { width: '32px', height: '32px', fontSize: '12px' },
    medium: { width: '40px', height: '40px', fontSize: '15px' },
    large: { width: '48px', height: '48px', fontSize: '18px' }
  }
  
  const size = sizes[props.size]
  
  return {
    backgroundColor: '#4cb7ad',
    width: size.width,
    height: size.height,
    fontSize: size.fontSize,
    lineHeight: '1'
  }
})
</script>
