<template>
  <div class="todo-stats">
    <div class="stats-info">
      <span class="stat-item">
        总计: {{ stats.total }}
      </span>
      <span class="stat-item">
        进行中: {{ stats.active }}
      </span>
      <span class="stat-item">
        已完成: {{ stats.completed }}
      </span>
      <span v-if="stats.total > 0" class="stat-item">
        完成率: {{ stats.percentage }}%
      </span>
    </div>
    
    <div class="stats-actions">
      <button 
        @click="$emit('clear-completed')"
        :disabled="stats.completed === 0"
        class="clear-btn"
      >
        清除已完成
      </button>
    </div>
  </div>
</template>

<script setup>
defineProps({
  stats: {
    type: Object,
    required: true,
    default: () => ({
      total: 0,
      active: 0,
      completed: 0,
      percentage: 0
    })
  }
})

defineEmits(['clear-completed'])
</script>

<style scoped>
.todo-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.stats-info {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.stat-item {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.stats-actions {
  display: flex;
  gap: 10px;
}

.clear-btn {
  padding: 8px 16px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.clear-btn:hover:not(:disabled) {
  background-color: #5a6268;
}

.clear-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 600px) {
  .todo-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .stats-info {
    justify-content: center;
  }
}
</style> 