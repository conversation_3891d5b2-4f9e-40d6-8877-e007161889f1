/* eslint-disable prettier/prettier */
import './public-path.ts';
import { App, createApp } from 'vue';
import AppComponent from './App.vue';
import routes from './router/index';
import { createRouter, createWebHistory, Router } from 'vue-router';

const isQiankun = !!window.__POWERED_BY_QIANKUN__;
console.log(isQiankun);

// 创建路由配置
const router = createRouter({
  history: createWebHistory(isQiankun ? '/vue-sub' : '/'),
  routes: routes, // 类型声明
});



let app: App<Element>;

/** render 函数动态挂载 vue 应用 */
function render(props: Record<string, any> = {}) {
  app = createApp(AppComponent);  // 创建Vue应用
  const { container } = props;
  app.use(router).mount(container ? container.querySelector('#app') : '#app');
}

// 独立运行
if (!isQiankun) {
  render();
}

export async function bootstrap(): Promise<void> {
  // bootstrap 生命周期钩子
  console.log('Vue app bootstraped');
}

export async function mount(props: Record<string, any>): Promise<void> {
  // mount 生命周期钩子，通常在 qiankun 中被调用
  render(props);
}

export async function unmount(): Promise<void> {
  // unmount 生命周期钩子，卸载 Vue 应用
  app.unmount();
}
