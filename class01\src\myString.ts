class MyString {
    private value: string;
    constructor(value = '') {
        this.value = String(value);
    }

    toString() {
        return this.value;
    }

    charAt(index: number) {
        return this.value.charAt(index);
    }

    concat(...strings: any[]) {
        return new MyString(this.value.concat(...strings.map(String)));
    }

    includes(search: string, start = 0) {
        return this.value.includes(search, start);
    }

    indexOf(search: string, start = 0) {
        return this.value.indexOf(search, start);
    }

    slice(start?: number, end?: number) {
        return new MyString(this.value.slice(start, end));
    }

    split(separator: string, limit?: number) {
        return this.value.split(separator, limit);
    }

    toUpperCase() {
        return new MyString(this.value.toUpperCase());
    }

    toLowerCase() {
        return new MyString(this.value.toLowerCase());
    }

    trim() {
        return new MyString(this.value.trim());
    }

    get length() {
        return this.value.length;
    }
}

export default MyString;