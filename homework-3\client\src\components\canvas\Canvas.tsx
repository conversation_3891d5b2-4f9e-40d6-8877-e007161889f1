import React, { useRef, useMemo } from "react";
import { useMindMapStore } from "../../store/index";
import { Node } from "./Node";
import { Edge } from "./Edge";
import { useViewportNavigation } from "../../hooks/useViewportNavigation";
import { useCanvasInteractions } from "../../hooks/useCanvasInteractions";
import { useShortcuts } from "../../hooks/useShortcuts";
import { useSvgAutosize } from "../../hooks/useSvgAutosize";

// 使用 React.memo 优化 Edge 组件
const MemoizedEdge = React.memo(Edge);

// 使用 React.memo 优化 Node 组件
const MemoizedNode = React.memo(Node);

export default function Canvas() {
  const canvasRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const nodes = useMindMapStore((s) => s.nodes);
  const edges = useMindMapStore((s) => s.edges);
  const selection = useMindMapStore((s) => s.selection);
  const viewport = useMindMapStore((s) => s.viewport);
  const editingNodeId = useMindMapStore((s) => s.editingNodeId);
  const setEditingNode = useMindMapStore((s) => s.setEditingNode);
  const { isPanning, isLongPress, isRightDrag } =
    useViewportNavigation(canvasRef);

  const getCursorStyle = () => {
    if (isPanning && (isLongPress || isRightDrag)) return "grabbing";
    if (isLongPress) return "grab";
    return "default";
  };

  const { handleCanvasClick: originalHandleCanvasClick } =
    useCanvasInteractions(canvasRef, svgRef);

  const handleCanvasClick = (e: React.MouseEvent) => {
    originalHandleCanvasClick(e);
    if (editingNodeId) {
      setEditingNode(null);
    }
  };

  // 使用 useMemo 优化边缘渲染，只渲染连接可见节点的边
  const renderedEdges = useMemo(() => {
    const isNodeVisible = useMindMapStore.getState().isNodeVisible;

    return edges
      .filter((edge) => {
        const from = nodes.find((n) => n.id === edge.from);
        const to = nodes.find((n) => n.id === edge.to);
        return from && to && isNodeVisible(from.id) && isNodeVisible(to.id);
      })
      .map((edge) => {
        const from = nodes.find((n) => n.id === edge.from);
        const to = nodes.find((n) => n.id === edge.to);
        if (!from || !to) return null;

        // 根据节点位置计算连接点
        const rootNode = nodes.find((n) => n.level === 1);
        const isLeftSide = rootNode && to.x < rootNode.x;

        // 使用节点实际宽度计算连接点
        const fromWidth = from.width || 100;
        const toWidth = to.width || 100;

        let sourceX, sourceY;

        // 如果是根节点到二级节点的连接
        if (from.level === 1 && to.level === 2) {
          if (Math.abs(to.y - from.y) < 20) {
            // 节点位置接近根节点时从侧边出发
            sourceX = isLeftSide
              ? from.x - fromWidth / 2
              : from.x + fromWidth / 2;
            sourceY = from.y;
          } else {
            // 根据高度差决定出发点，离根节点越远越靠近中心
            const distanceFromRoot = Math.abs(to.y - from.y);
            const fromHeight = from.height || 50;

            // 计算偏移量：距离越远，偏移越小（越靠近中心）
            const maxOffset = fromWidth * 0.4;
            const minOffset = fromWidth * 0.1; // 最小偏移，避免在中间位置

            // 根据距离计算偏移，距离越大偏移越小
            const normalizedDistance = Math.min(distanceFromRoot / 200, 1); // 200为参考距离
            const offsetX =
              maxOffset - (maxOffset - minOffset) * normalizedDistance;

            // 根据节点位置选择出发边缘
            if (to.y < from.y) {
              sourceX = isLeftSide ? from.x - offsetX : from.x + offsetX;
              sourceY = from.y - fromHeight / 2;
            } else {
              sourceX = isLeftSide ? from.x - offsetX : from.x + offsetX;
              sourceY = from.y + fromHeight / 2;
            }
          }
        } else {
          // 二级节点及以后的连接：都从父节点侧边中间出发，使用折线
          sourceX = isLeftSide
            ? from.x - fromWidth / 2
            : from.x + fromWidth / 2;
          sourceY = from.y;
        }

        const targetX = isLeftSide ? to.x + toWidth / 2 : to.x - toWidth / 2;
        const targetY = to.y;

        return (
          <MemoizedEdge
            key={edge.id}
            id={edge.id}
            sourceX={sourceX}
            sourceY={sourceY}
            targetX={targetX}
            targetY={targetY}
            selected={selection.edges.includes(edge.id)}
            lineStyle={from.level === 1 ? "curved" : "polyline"}
          />
        );
      })
      .filter(Boolean);
  }, [edges, nodes, selection.edges]);

  // 使用 useMemo 优化节点渲染，只渲染可见节点
  // 将正在编辑的节点渲染在最后，确保在最上层
  const renderedNodes = useMemo(() => {
    const isNodeVisible = useMindMapStore.getState().isNodeVisible;
    const visibleNodes = nodes.filter((node) => isNodeVisible(node.id));

    // 将编辑中的节点放到最后渲染
    const editingNodeId = useMindMapStore.getState().editingNodeId;
    const editingNode = visibleNodes.find((node) => node.id === editingNodeId);
    const otherNodes = visibleNodes.filter((node) => node.id !== editingNodeId);

    const orderedNodes = editingNode
      ? [...otherNodes, editingNode]
      : visibleNodes;

    return orderedNodes.map((node) => (
      <MemoizedNode
        key={node.id}
        {...node}
        selected={selection.nodes.includes(node.id)}
      />
    ));
  }, [nodes, selection.nodes, editingNodeId]);

  // 使用 useMemo 优化 SVG 样式
  const svgStyle = useMemo(
    () => ({
      minWidth: "100%",
      minHeight: "100%",
      transform: `translate(${viewport.x}px, ${viewport.y}px) scale(${viewport.zoom})`,
      transformOrigin: "0 0",
      // 确保SVG内容不会被裁剪
      overflow: "visible",
    }),
    [viewport.zoom, viewport.x, viewport.y]
  );

  const svgBox = useSvgAutosize(nodes);

  useShortcuts();

  return (
    <div
      ref={canvasRef}
      className="w-full h-full bg-gray-50 relative overflow-auto"
      onClick={handleCanvasClick}
      onContextMenu={(e) => e.preventDefault()} // 阻止右键菜单
      style={{ cursor: getCursorStyle() }}
    >
      <svg
        ref={svgRef}
        width={svgBox.width}
        height={svgBox.height}
        viewBox={`${svgBox.minX} ${svgBox.minY} ${svgBox.width} ${svgBox.height}`}
        style={svgStyle}
        onClick={handleCanvasClick}
      >
        {renderedEdges}
        {renderedNodes}
      </svg>
    </div>
  );
}
