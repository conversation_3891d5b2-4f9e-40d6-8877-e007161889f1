import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Plus, Trash2 } from "@/components/ui/icons";
import type {
  FormField,
  TextField,
  SelectField,
  RadioField,
  ImageUploadFieldType,
} from "@/types/form";
import { useState, useEffect } from "react";

interface RightPanelProps {
  selectedField: FormField | null;
  onFieldUpdate: (fieldId: string, updates: Partial<FormField>) => void;
  onFieldDelete: (fieldId: string) => void;
}

export function RightPanel({
  selectedField,
  onFieldUpdate,
  onFieldDelete,
}: RightPanelProps) {
  const [localField, setLocalField] = useState<FormField | null>(selectedField);

  useEffect(() => {
    setLocalField(selectedField);
  }, [selectedField]);

  const handleUpdate = (key: string, value: unknown) => {
    if (localField) {
      const updatedField = { ...localField, [key]: value } as FormField;
      setLocalField(updatedField);
      onFieldUpdate(localField.id, { [key]: value });
    }
  };

  const handleOptionChange = (
    index: number,
    newLabel: string,
    newValue: string
  ) => {
    if (
      localField &&
      (localField.type === "select" || localField.type === "radio")
    ) {
      const newOptions = [...localField.options];
      newOptions[index] = { label: newLabel, value: newValue };
      setLocalField({ ...localField, options: newOptions });
      onFieldUpdate(localField.id, { options: newOptions });
    }
  };

  const handleAddOption = () => {
    if (
      localField &&
      (localField.type === "select" || localField.type === "radio")
    ) {
      const newOptions = [
        ...localField.options,
        {
          label: `选项 ${localField.options.length + 1}`,
          value: `option${localField.options.length + 1}`,
        },
      ];
      setLocalField({ ...localField, options: newOptions });
      onFieldUpdate(localField.id, { options: newOptions });
    }
  };

  const handleRemoveOption = (index: number) => {
    if (
      localField &&
      (localField.type === "select" || localField.type === "radio")
    ) {
      const newOptions = localField.options.filter((_, i) => i !== index);
      setLocalField({ ...localField, options: newOptions });
      onFieldUpdate(localField.id, { options: newOptions });
    }
  };

  if (!localField) {
    return (
      <div className="flex h-full w-80 flex-col border-l bg-white p-4 text-center text-gray-500">
        <p className="mt-20">
          点击左侧题目，可在右侧面板设置题目和回答区内容等
        </p>
      </div>
    );
  }

  return (
    <div className="flex h-full w-80 flex-col border-l bg-white p-4">
      <h3 className="mb-4 text-lg font-semibold">题目设置</h3>

      <div className="space-y-4">
        {/* Common settings for all field types */}
        <div>
          <Label htmlFor={`${localField.id}-label`}>题目标题</Label>
          <Input
            id={`${localField.id}-label`}
            value={localField.label}
            onChange={(e) => handleUpdate("label", e.target.value)}
          />
        </div>

        <div className="flex items-center justify-between">
          <Label htmlFor={`${localField.id}-required`}>是否必填</Label>
          <Switch
            id={`${localField.id}-required`}
            checked={localField.required || false}
            onCheckedChange={(checked) => handleUpdate("required", checked)}
          />
        </div>

        {/* Type-specific settings */}
        {localField.type === "text" && (
          <>
            <div>
              <Label htmlFor={`${localField.id}-placeholder`}>占位描述</Label>
              <Input
                id={`${localField.id}-placeholder`}
                value={(localField as TextField).placeholder || ""}
                onChange={(e) => handleUpdate("placeholder", e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor={`${localField.id}-maxLength`}>最大字符数</Label>
              <Input
                id={`${localField.id}-maxLength`}
                type="number"
                value={(localField as TextField).maxLength || ""}
                onChange={(e) =>
                  handleUpdate(
                    "maxLength",
                    parseInt(e.target.value) || undefined
                  )
                }
              />
            </div>
          </>
        )}

        {(localField.type === "select" || localField.type === "radio") && (
          <>
            <div>
              <Label>可选项</Label>
              <div className="space-y-2">
                {(localField as SelectField | RadioField).options.map(
                  (option, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Input
                        value={option.label}
                        onChange={(e) =>
                          handleOptionChange(
                            index,
                            e.target.value,
                            option.value
                          )
                        }
                        placeholder="选项标题"
                      />
                      {/* Value input can be added if needed, for now, value is same as label or derived */}
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveOption(index)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  )
                )}
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleAddOption}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  添加选项
                </Button>
              </div>
            </div>
            {localField.type === "select" && (
              <div>
                <Label htmlFor={`${localField.id}-placeholder`}>占位描述</Label>
                <Input
                  id={`${localField.id}-placeholder`}
                  value={(localField as SelectField).placeholder || ""}
                  onChange={(e) => handleUpdate("placeholder", e.target.value)}
                />
              </div>
            )}
          </>
        )}

        {localField.type === "imageUpload" && (
          <>
            <div>
              <Label htmlFor={`${localField.id}-maxFiles`}>
                最大上传文件数
              </Label>
              <Input
                id={`${localField.id}-maxFiles`}
                type="number"
                value={(localField as ImageUploadFieldType).maxFiles || 1}
                onChange={(e) =>
                  handleUpdate("maxFiles", parseInt(e.target.value) || 1)
                }
              />
            </div>
            <div>
              <Label htmlFor={`${localField.id}-maxSizeMB`}>
                单张图片最大MB
              </Label>
              <Input
                id={`${localField.id}-maxSizeMB`}
                type="number"
                value={(localField as ImageUploadFieldType).maxSizeMB || 10}
                onChange={(e) =>
                  handleUpdate("maxSizeMB", parseInt(e.target.value) || 10)
                }
              />
            </div>
          </>
        )}

        <Button
          variant="destructive"
          className="w-full"
          onClick={() => onFieldDelete(localField.id)}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          删除题目
        </Button>
      </div>
    </div>
  );
}
