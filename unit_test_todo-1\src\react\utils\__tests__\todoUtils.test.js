import { describe, test, expect } from "@jest/globals";
import * as todoUtils from "../todoUtils";

describe("todoUtils", () => {
  test("验证待办项文本", () => {
    expect(todoUtils.validateTodoText("")).toBe(false);
    expect(todoUtils.validateTodoText("   ")).toBe(false);
    expect(todoUtils.validateTodoText(null)).toBe(false);
    expect(todoUtils.validateTodoText(123)).toBe(false);
    expect(todoUtils.validateTodoText("有效文本")).toBe(true);
  });

  test("生成唯一的待办项ID", () => {
    const id1 = todoUtils.generateTodoId();
    const id2 = todoUtils.generateTodoId();
    expect(id1).not.toBe(id2);
    expect(typeof id1).toBe("number");
  });

  test("格式化待办项日期", () => {
    const date = new Date("2023-01-01T12:00:00");
    const formatted = todoUtils.formatTodoDate(date);
    expect(typeof formatted).toBe("string");
    expect(formatted).toContain("2023");
    expect(todoUtils.formatTodoDate(null)).toBe("");
  });

  test("过滤待办项", () => {
    const todos = [
      { id: 1, text: "任务1", completed: true },
      { id: 2, text: "任务2", completed: false },
    ];

    const completedTodos = todoUtils.filterTodos(todos, "completed");
    expect(completedTodos).toHaveLength(1);
    expect(completedTodos[0].id).toBe(1);

    const activeTodos = todoUtils.filterTodos(todos, "active");
    expect(activeTodos).toHaveLength(1);
    expect(activeTodos[0].id).toBe(2);

    const allTodos = todoUtils.filterTodos(todos, "all");
    expect(allTodos).toHaveLength(2);
  });

  test("排序待办项", () => {
    const todos = [
      { id: 1, text: "B任务", completed: true, createdAt: "2023-01-02" },
      { id: 2, text: "A任务", completed: false, createdAt: "2023-01-01" },
    ];

    // 按创建时间排序
    const sortedByCreated = todoUtils.sortTodos(todos, "created");
    expect(sortedByCreated[0].id).toBe(1); // 最新的在前面

    // 按文本排序
    const sortedByText = todoUtils.sortTodos(todos, "text");
    expect(sortedByText[0].id).toBe(2); // 'A任务'应该在前面

    // 按完成状态排序
    const sortedByCompleted = todoUtils.sortTodos(todos, "completed");
    expect(sortedByCompleted[0].id).toBe(2); // 未完成的在前面
  });

  test("统计待办项信息", () => {
    const todos = [
      { id: 1, text: "任务1", completed: true },
      { id: 2, text: "任务2", completed: false },
      { id: 3, text: "任务3", completed: false },
    ];

    const stats = todoUtils.getTodoStats(todos);
    expect(stats.total).toBe(3);
    expect(stats.completed).toBe(1);
    expect(stats.active).toBe(2);
    expect(stats.percentage).toBe(33);
  });
});
