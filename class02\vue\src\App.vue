<template>
    <div class="login-container">
        <button :disabled="isLoggedIn" @click="login">
            登录
        </button>
        <button :disabled="!isLoggedIn" @click="logout">
            退出登录
        </button>
        <button :disabled="!isLoggedIn || !hasPrivilege" @click="payPrivilege">
            权益购买
        </button>
        <button @click="handleUploadFile">
            上传文件
        </button>
    </div>
</template>

<style>
.login-container {
    text-align: center;
    margin-top: 40px;
}

button {
    padding: 10px 20px;
    margin: 0 10px;
    font-size: 16px;
    border-radius: 6px;
    border: none;
    background-color: #42b983;
    color: white;
    cursor: pointer;
}

button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

button:hover:enabled {
    background-color: #369e6f;
}
</style>


<script setup>
import { ref, onMounted } from 'vue'

const isLoggedIn = ref(false) // 登录状态，初始为未登录
const hasPrivilege = ref(false)

function login() {
    const res = Extention.Account.login({ bizSrc: "weblogin", loginSrc: "a", qrcode: "a", from: "a", bShowModal: true, bTopWindow: false, a: "a" });
    console.log(res);
    isLoggedIn.value = res.result.login;
}

function logout() {
    const res = Extention.Account.logout({ logoutSrc: "weblogout" });
    console.log(res);
    isLoggedIn.value = res.result.login;
}

function payPrivilege() {
    // 调起支付对话框
    const res = Extention.DocerV2.openDocerUnifyPayDlg({ url: 'https://vip.wps.cn/vcl_svr/static/docerpay' })
    console.log('支付窗口调起', res);
    const privileges = res?.result?.privileges;
    if (Array.isArray(privileges) && privileges.length != 0) {
        if (privileges[0]?.expireTime) {
            hasPrivilege.value = false;
        }
        hasPrivilege.value = true;
    } else {
        hasPrivilege.value = true;
    }
}

async function handleUploadFile() {
    // 1. 检查登录状态
    if (!isLoggedIn.value) {
        const loginRes = Extention.Account.login({
            bizSrc: "weblogin",
            loginSrc: "a",
            qrcode: "a",
            from: "a",
            bShowModal: true,
            bTopWindow: false,
            a: "a",
        })
        if (!loginRes?.result?.login) {
            console.warn("用户未登录，取消上传")
            return
        }
        isLoggedIn.value = true
    }

    try {
        // 2. 选择文件
        const fileResult = Extention.Common.File.getOpenFileNames({
            caption: "请选择文件",
            filter: "Microsoft Word文件(*.docx)",
        })

        console.log("文件选择结果:", fileResult)

        const paths = fileResult?.result?.fileNames;
        if ( paths.length === 0) {
            console.warn("未选择文件")
            return
        }

        const fullPath = paths;
        const fileNameWithExt = fullPath.split(/[/\\]/).pop();
        const fileParts = fileNameWithExt.split(".");
        const fileName = fileParts.slice(0, -1).join(".");
        const fileLast = "." + fileParts.slice(-1)[0] 
        console.log(fileParts, fileName, fileLast);
 
        // 3. 读取文件内容
        const bufferResult = Application.FileSystem.ReadFileAsArrayBuffer(fullPath);
        console.log("读取文件:", bufferResult);

        // 4. 上传
        const uploadResult = await uploadPdfToCloud(bufferResult, fileName, fileLast);
        console.log(uploadResult.msg);
    } catch (e) {
        console.error("上传文件流程出错：", e);
    }
}


function checkLoginStatus() {
    try {
        const res = Extention.Account.getUserInfo();
        console.log('getUserInfo 返回:', res)
        // 根据返回结果判断是否登录（假设 result.login 为 true 表示已登录）
        isLoggedIn.value = !!res?.result;

        if (isLoggedIn.value) {
            checkPrivileInfo()
            // checkHoneyComb()
        } else {
            hasPrivilege.value = false
        }
    } catch (e) {
        console.error('getUserInfo 调用失败:', e)
        isLoggedIn.value = false
        hasPrivilege.value = false
    }
}

function checkPrivileInfo() {
    const privileInfo = Extention.Account.Privilege.getPrivilegeInfo({ privilege_ids: ['resume_package_new'] });
    console.log('privileInfo', privileInfo);
    const privileges = privileInfo?.result?.privileges
    if (Array.isArray(privileges) && privileges.length === 0) {
        console.log('没有权益')
        hasPrivilege.value = true  //按钮高亮
    } else {
        console.log('有权益')
        hasPrivilege.value = false;  //按钮置灰
    }
}

function checkHoneyComb() {
    const res = // 这个蜂巢配置的结果中，返回show字段，值为bool
        Extention.Honeycomb.getMostApplicableDataUnderModuleGroup({ projectID: '60', moduleGroupID: '55748' })
    console.log('蜂巢配置结果', res);
    const parsedResult = JSON.parse(res.result)
    const show = parsedResult.show
    console.log('show 值是:', show)
    if (show) {
        // 如果 show 为 true，则显示按钮
        hasPrivilege.value = true;
    } else {
        // 如果 show 为 false，则隐藏按钮
        hasPrivilege.value = false;
    }
}

// content类型是arrayBuffer，fileName是文件名（不带后缀），fileLast为文件后缀（比如.docx)
const uploadPdfToCloud = async (content, fileName, fileLast) => {
    try {
        const url = 'https://assess.docer.wps.cn/share/v2/upload/0'
        const response = await fetch(`${url}?is_normal=1&file_name=${fileName}&file_last=${fileLast}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/octet-stream',
            },
            body: content,
            credentials: 'include',
        })
        const result = await response.json()
        return result
    } catch (e) {
        console.error(e)
    }
}

// 页面加载时检查一次状态
onMounted(() => {
    checkLoginStatus();
})
</script>