export interface PaymentStrategy {
  pay(amount: number): string
}
// 定义策略接口和具体策略
// 具体策略 - 信用卡支付
export class CreditCardStrategy implements PaymentStrategy {
  constructor(
    private cardNumber: string,
    private expiryDate: string,
    private cvv: string,
  ) {}

  pay(amount: number): string {
    console.log(`处理信用卡支付 $${amount}`)
    // 实际支付逻辑...
    return `支付 $${amount} 使用信用卡结尾 ${this.cardNumber.slice(-4)}`
  }
}

// 具体策略 - PayPal支付
export class PayPalStrategy implements PaymentStrategy {
  constructor(private email: string) {}

  pay(amount: number): string {
    console.log(`处理PayPal支付 $${amount}`)
    // 实际支付逻辑...
    return `支付 $${amount} 使用PayPal账号 ${this.email}`
  }
}

// 具体策略 - 加密货币支付
export class CryptoStrategy implements PaymentStrategy {
  constructor(private walletAddress: string) {}

  pay(amount: number): string {
    console.log(`处理加密货币支付 $${amount}`)
    // 实际支付逻辑...
    return `支付 $${amount} 使用加密货币钱包 ${this.walletAddress.slice(0, 6)}...`
  }
}
