import { useEffect } from "react";
import { useMindMapStore } from "../store";

export function useShortcuts() {
  const selection = useMindMapStore((s) => s.selection);
  const removeNode = useMindMapStore((s) => s.removeNode);
  const removeEdge = useMindMapStore((s) => s.removeEdge);
  const fitView = useMindMapStore((s) => s.fitView);
  const addChildTheme = useMindMapStore((s) => s.addChildTheme);
  const addSameLevelTheme = useMindMapStore((s) => s.addSameLevelTheme);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 阻止在拖拽状态下执行快捷键
      if (document.body.style.cursor === "grabbing") {
        return;
      }

      if (e.key === "Delete" || e.key === "Backspace") {
        e.preventDefault();
        e.stopPropagation();
        if (selection.nodes.length > 0) {
          selection.nodes.forEach((id) => removeNode(id));
        }
        if (selection.edges.length > 0) {
          selection.edges.forEach((id) => removeEdge(id));
        }
      } else if (e.key === "f" && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        e.stopPropagation();
        fitView();
      } else if (e.key === "Tab" && selection.nodes.length > 0) {
        e.preventDefault();
        e.stopPropagation();
        addChildTheme(selection.nodes[0]);
      } else if (e.key === "Enter" && selection.nodes.length > 0) {
        e.preventDefault();
        e.stopPropagation();
        addSameLevelTheme(selection.nodes[0]);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [
    selection,
    removeNode,
    removeEdge,
    fitView,
    addChildTheme,
    addSameLevelTheme,
  ]);
}
