import { useRef, useState } from "react";
import {
  MagnifyingGlassIcon,
  FontBoldIcon,
  FontItalicIcon,
  TextAlignLeftIcon,
  TextAlignCenterIcon,
  TextAlignRightIcon,
  ImageIcon,
  ChevronDownIcon,
} from "@radix-ui/react-icons";
import * as Toast from "@radix-ui/react-toast";
import Tooltip from "../ui/Tooltip";
import CustomIcons from "../icons/SvgIcons";
import { useMindMapStore } from "../../store/index";
import ColorPicker from "../ui/ColorPicker"; // 导入ColorPicker组件
import { useTextFormatting } from "../../hooks/useTextFormatting";

export default function StartToolbar() {
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [showToast, setShowToast] = useState(false);
  const undo = useMindMapStore((s) => s.undo);
  const redo = useMindMapStore((s) => s.redo);
  const addChildTheme = useMindMapStore((s) => s.addChildTheme);
  const addSameLevelTheme = useMindMapStore((s) => s.addSameLevelTheme);
  const addParentTheme = useMindMapStore((s) => s.addParentTheme);
  const { handleFormatText, selectedNode, hasSelection } = useTextFormatting();

  // 判断按钮是否应该置灰
  const canAddChildTheme = !!selectedNode && selectedNode.level < 3;
  const canAddSameLevelTheme = !!selectedNode && selectedNode.level > 1;
  const canAddParentTheme =
    !!selectedNode && selectedNode.level > 2 && selectedNode.level < 3;

  // 显示提示
  const showSelectionToast = () => {
    setShowToast(true);
    // 3秒后自动关闭
    setTimeout(() => {
      setShowToast(false);
    }, 3000);
  };

  const mainButtons = [
    {
      key: "search",
      tooltip: "展示或关闭左侧是目录弹窗",
      icon: <MagnifyingGlassIcon className="w-4 h-4" />,
      label: "查找替换",
      withDivider: true,
      onClick: () => {
        console.log("查找替换功能");
        // 实现查找替换逻辑
      },
    },
    {
      key: "undo",
      tooltip: "撤销",
      icon: <CustomIcons type="undo" className="w-4 h-4" />,
      label: "",
      onClick: undo,
    },
    {
      key: "redo",
      tooltip: "恢复",
      icon: <CustomIcons type="redo" className="w-4 h-4" />,
      label: "",
      onClick: redo,
    },
    {
      key: "format",
      tooltip: "格式刷",
      icon: (
        <CustomIcons
          type="format"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "",
      withDivider: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("格式刷功能");
            // 实现格式刷逻辑
          }
        : showSelectionToast,
    },
    {
      key: "font-type",
      tooltip: "字体类型",
      icon: "",
      label: "微软雅黑",
      disabled: !hasSelection,
      hasDropdown: true,
      onClick: hasSelection
        ? () => {
            console.log("字体类型功能");
            // 实现字体类型切换逻辑
          }
        : showSelectionToast,
    },
    {
      key: "font-size",
      tooltip: "字体大小",
      icon: "",
      label: "20px",
      withDivider: true,
      disabled: !hasSelection,
      hasDropdown: true,
      onClick: hasSelection
        ? () => {
            console.log("字体大小功能");
            // 实现字体大小切换逻辑
          }
        : showSelectionToast,
    },
    {
      key: "bold",
      tooltip: "粗体",
      icon: <FontBoldIcon className="w-4 h-4" />,
      label: "",
      disabled: !hasSelection,
      active: selectedNode?.isBold, // 设置按钮活跃状态
      onClick: hasSelection
        ? () => handleFormatText("bold")
        : showSelectionToast,
    },
    {
      key: "italic",
      tooltip: "斜体",
      icon: <FontItalicIcon className="w-4 h-4" />,
      label: "",
      disabled: !hasSelection,
      active: selectedNode?.isItalic, // 设置按钮活跃状态
      onClick: hasSelection
        ? () => handleFormatText("italic")
        : showSelectionToast,
    },
    {
      key: "font-color",
      tooltip: "字体颜色",
      content: (
        <div className="flex items-center">
          <ColorPicker
            selectedColor={selectedNode?.fontColor || "#000000"}
            onColorChange={(color) => handleFormatText("fontColor", color)}
            disabled={!hasSelection}
          />
        </div>
      ),
      disabled: !hasSelection,
      withDivider: true,
    },
    {
      key: "alignleft",
      tooltip: "左对齐",
      icon: <TextAlignLeftIcon className="w-4 h-4" />,
      label: "",
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("左对齐功能");
            // 实现左对齐逻辑
          }
        : showSelectionToast,
    },
    {
      key: "aligncenter",
      tooltip: "居中对齐",
      icon: <TextAlignCenterIcon className="w-4 h-4" />,
      label: "",
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("居中对齐功能");
            // 实现居中对齐逻辑
          }
        : showSelectionToast,
    },
    {
      key: "alignright",
      tooltip: "右对齐",
      icon: <TextAlignRightIcon className="w-4 h-4" />,
      label: "",
      withDivider: true,
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("右对齐功能");
            // 实现右对齐逻辑
          }
        : showSelectionToast,
    },
    {
      key: "child-theme",
      tooltip: "子主题",
      icon: (
        <CustomIcons
          type="child-theme"
          className="w-4 h-4"
          disabled={!canAddChildTheme}
        />
      ),
      label: "",
      disabled: !canAddChildTheme,
      onClick: canAddChildTheme
        ? () => {
            if (canAddChildTheme) {
              addChildTheme();
            }
          }
        : showSelectionToast,
    },
    {
      key: "same-theme",
      tooltip: "同级主题",
      icon: (
        <CustomIcons
          type="same-theme"
          className="w-4 h-4"
          disabled={!canAddSameLevelTheme}
        />
      ),
      label: "",
      disabled: !canAddSameLevelTheme,
      onClick: canAddSameLevelTheme
        ? () => {
            if (canAddSameLevelTheme) {
              addSameLevelTheme();
            }
          }
        : showSelectionToast,
    },
    {
      key: "parent-theme",
      tooltip: "父主题",
      icon: (
        <CustomIcons
          type="parent-theme"
          className="w-4 h-4"
          disabled={!canAddParentTheme}
        />
      ),
      label: "",
      withDivider: true,
      disabled: !canAddParentTheme,
      onClick: canAddParentTheme
        ? () => {
            if (canAddParentTheme) {
              addParentTheme();
            }
          }
        : showSelectionToast,
    },
    {
      key: "summary",
      tooltip: "概要",
      icon: (
        <CustomIcons
          type="summary"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      disabled: !hasSelection,
      label: "概要",
      onClick: hasSelection
        ? () => {
            console.log("概要功能");
            // 实现概要逻辑
          }
        : showSelectionToast,
    },
    {
      key: "frame",
      tooltip: "外框",
      icon: (
        <CustomIcons
          type="frame"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "外框",
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("外框功能");
            // 实现外框逻辑
          }
        : showSelectionToast,
    },
    {
      key: "image",
      tooltip: "插入图片",
      icon: <ImageIcon className="w-4 h-4" />,
      label: "图片",
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("插入图片功能");
            // 实现插入图片逻辑
          }
        : showSelectionToast,
    },
    {
      key: "hyperlink",
      tooltip: "插入超链接",
      icon: (
        <CustomIcons
          type="hyperlink"
          className="w-4 h-4"
          disabled={!hasSelection}
        />
      ),
      label: "超链接",
      disabled: !hasSelection,
      onClick: hasSelection
        ? () => {
            console.log("插入超链接功能");
            // 实现插入超链接逻辑
          }
        : showSelectionToast,
    },
    {
      key: "watermark",
      tooltip: "插入水印",
      icon: <CustomIcons type="watermark" className="w-4 h-4" />,
      label: "水印",
      withDivider: true,
      onClick: () => {
        console.log("插入水印功能");
        // 实现插入水印逻辑
      },
    },
    {
      key: "canvas",
      tooltip: "画布设置",
      icon: <CustomIcons type="canvas-color" className="w-4 h-4" />,
      label: "画布",
      onClick: () => {
        console.log("画布设置功能");
        // 实现画布设置逻辑
      },
    },
    {
      key: "style",
      tooltip: "风格设置",
      icon: <CustomIcons type="style" className="w-4 h-4" />,
      label: "风格",
      onClick: () => {
        console.log("风格设置功能");
        // 实现风格设置逻辑
      },
    },
    {
      key: "structure",
      tooltip: "结构设置",
      icon: <CustomIcons type="structure" className="w-4 h-4" />,
      label: "结构",
      hasDropdown: true,
      onClick: () => {
        console.log("结构设置功能");
        // 实现结构设置逻辑
      },
    },
    {
      key: "fold",
      tooltip: "收起",
      icon: <CustomIcons type="fold" className="w-4 h-4" />,
      label: "收起",
      hasDropdown: true,
      onClick: () => {
        console.log("收起功能");
        // 实现收起逻辑
      },
    },
    {
      key: "unfold",
      tooltip: "展开",
      icon: <CustomIcons type="unfold" className="w-4 h-4" />,
      label: "展开",
      hasDropdown: true,
      onClick: () => {
        console.log("展开功能");
        // 实现展开逻辑
      },
    },
  ];

  return (
    <Toast.Provider swipeDirection="up">
      <div className="px-4 py-2 relative w-full">
        <div className="flex items-center relative overflow-x-hidden w-full"></div>
        <div
          ref={toolbarRef}
          className="flex flex-nowrap gap-x-px w-full"
          style={{
            marginRight: 20,
          }}
        >
          {mainButtons.map((btn) => (
            <div
              className={`flex items-center space-x-1 mr-1 ${
                btn.withDivider ? "border-r border-gray-200 pr-1" : ""
              }`}
              key={btn.key}
            >
              <Tooltip content={btn.tooltip}>
                {btn.content ? (
                  btn.content
                ) : (
                  <button
                    className={`flex items-center space-x-1 px-2 py-1 text-sm rounded transition-colors ${
                      btn.disabled
                        ? "text-gray-400 bg-gray-50"
                        : btn.active
                        ? "text-blue-600 bg-blue-50"
                        : "text-gray-700 hover:bg-gray-100 cursor-pointer"
                    }`}
                    onClick={btn.onClick}
                  >
                    {btn.icon}
                    {btn.label && (
                      <span className="whitespace-nowrap">{btn.label}</span>
                    )}
                    {btn.hasDropdown && <ChevronDownIcon className="w-3 h-3" />}
                  </button>
                )}
              </Tooltip>
            </div>
          ))}

          {/* 右侧固定按钮区 */}
          <div className="absolute right-0 top-0 h-full flex items-center bg-white z-20">
            {/* 帮助与反馈 */}
            <Tooltip content="帮助与反馈">
              <button className="flex items-center px-2 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded cursor-pointer">
                <CustomIcons type="help" className="w-4 h-4" />
              </button>
            </Tooltip>
          </div>
        </div>
      </div>

      <Toast.Root
        className="bg-white rounded-lg shadow-lg border border-gray-200 p-3"
        open={showToast}
        onOpenChange={setShowToast}
        duration={2000}
      >
        <Toast.Title className="text-sm font-medium">
          请选中主题后操作
        </Toast.Title>
      </Toast.Root>
      <Toast.Viewport className="fixed top-0.1 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[1001] flex flex-col gap-2 w-auto max-w-[100vw] m-0 list-none outline-none" />
    </Toast.Provider>
  );
}
