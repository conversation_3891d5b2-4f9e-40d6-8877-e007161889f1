<template>
  <div class="strategy-demo">
    <h2>策略模式演示 - 支付系统</h2>

    <div class="controls">
      <select v-model="selectedStrategy">
        <option value="creditCard">信用卡</option>
        <option value="paypal">PayPal</option>
        <option value="crypto">加密货币</option>
      </select>

      <div v-if="selectedStrategy === 'creditCard'" class="strategy-form">
        <input v-model="creditCard.number" placeholder="卡号" />
        <input v-model="creditCard.expiry" placeholder="有效期" />
        <input v-model="creditCard.cvv" placeholder="CVV" />
      </div>

      <div v-if="selectedStrategy === 'paypal'" class="strategy-form">
        <input v-model="paypalEmail" placeholder="PayPal邮箱" />
      </div>

      <div v-if="selectedStrategy === 'crypto'" class="strategy-form">
        <input v-model="cryptoWallet" placeholder="钱包地址" />
      </div>

      <input type="number" v-model.number="paymentAmount" placeholder="金额" />
      <button @click="processPayment">支付</button>
    </div>

    <div class="result" v-if="paymentResult">
      <h3>支付结果</h3>
      <p>{{ paymentResult }}</p>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { PaymentContext } from '@/utils/strategies/PaymentContext'
import { CreditCardStrategy, PayPalStrategy, CryptoStrategy } from '@/utils/strategies/types'

export default defineComponent({
  name: 'StrategyDemo',
  setup() {
    const selectedStrategy = ref<'creditCard' | 'paypal' | 'crypto'>('creditCard')
    const paymentAmount = ref<number>(0)
    const paymentResult = ref<string>('')

    const creditCard = ref({
      number: '',
      expiry: '',
      cvv: '',
    })

    const paypalEmail = ref('')
    const cryptoWallet = ref('')

    const processPayment = () => {
      let strategy

      switch (selectedStrategy.value) {
        case 'creditCard':
          if (!creditCard.value.number || !creditCard.value.expiry || !creditCard.value.cvv) {
            alert('请输入完整的信用卡信息')
            return
          }
          strategy = new CreditCardStrategy(
            creditCard.value.number,
            creditCard.value.expiry,
            creditCard.value.cvv,
          )
          break

        case 'paypal':
          if (!paypalEmail.value) {
            alert('请输入PayPal邮箱')
            return
          }
          strategy = new PayPalStrategy(paypalEmail.value)
          break

        case 'crypto':
          if (!cryptoWallet.value) {
            alert('请输入钱包地址')
            return
          }
          strategy = new CryptoStrategy(cryptoWallet.value)
          break
      }

      const context = new PaymentContext(strategy)
      paymentResult.value = context.executePayment(paymentAmount.value)
    }

    return {
      selectedStrategy,
      paymentAmount,
      paymentResult,
      creditCard,
      paypalEmail,
      cryptoWallet,
      processPayment,
    }
  },
})
</script>

<style scoped>
.strategy-demo {
  border: 1px solid #eee;
  padding: 20px;
  margin: 20px 0;
  border-radius: 8px;
  font-family: Arial, sans-serif;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.strategy-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

input,
select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  padding: 10px 15px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.result {
  margin-top: 20px;
  padding: 15px;
  background: #f8f8f8;
  border-radius: 4px;
}
</style>
