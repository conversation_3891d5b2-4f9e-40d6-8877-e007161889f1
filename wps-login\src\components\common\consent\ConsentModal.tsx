import "./ConsentModal.css";
import type { ConsentModalProps } from "@/types/user";

export default function ConsentModal({
  isOpen,
  onClose,
  onAgree,
  onCancel,
}: ConsentModalProps) {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-container" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3 className="modal-title">提示</h3>
          <button onClick={onClose} className="close-button">
            <span className="close-icon">×</span>
          </button>
        </div>

        <div className="modal-divider"></div>

        <div className="modal-content">
          <p className="modal-text">
            欢迎选择使用金山办公在线服务！在您使用本服务前，请您务必充分阅读
            <a href="#" className="modal-link">
              隐私政策
            </a>
            和
            <a href="#" className="modal-link">
              在线服务协议
            </a>
            ，我们将严格按照协议内容为您提供服务、保护您的个人信息。
          </p>
        </div>

        <div className="modal-footer">
          <button onClick={onCancel} className="cancel-button">
            取消
          </button>
          <button onClick={onAgree} className="agree-button">
            同意
          </button>
        </div>
      </div>
    </div>
  );
}
