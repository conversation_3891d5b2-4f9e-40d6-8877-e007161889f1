import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Text,
  CircleDot,
  SquareCheck,
  Image,
  LayoutGrid,
  Book,
} from "@/components/ui/icons";
import type { FormField } from "@/types/form";

interface SidebarProps {
  onAddField: (type: FormField["type"]) => void;
}

export function Sidebar({ onAddField }: SidebarProps) {
  return (
    <div className="flex h-full w-64 flex-col border-r bg-white p-4">
      <div className="mb-1">
        <Input placeholder="智能搜题" className="pl-2" />
      </div>
      <div className="mb-2 flex space-x-2 border-b border-gray-200 pb-1">
        <Button variant="ghost" className="flex-1">
          <LayoutGrid className="mr-2 h-4 w-4" />
          题型
        </Button>
        <Button variant="ghost" className="flex-1">
          <Book className="mr-2 h-4 w-4" />
          题库
        </Button>
      </div>

      <div className="mb-6">
        <h3 className="mb-3 text-sm font-semibold text-gray-700">基础题型</h3>
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            onClick={() => onAddField("text")}
            className="flex flex-col h-auto py-3"
          >
            <Text className="mb-1 h-5 w-5" />
            <span className="text-xs">填空题</span>
          </Button>
          <Button
            variant="outline"
            onClick={() => onAddField("radio")}
            className="flex flex-col h-auto py-3"
          >
            <CircleDot className="mb-1 h-5 w-5" />
            <span className="text-xs">单选题</span>
          </Button>
          <Button
            variant="outline"
            onClick={() => onAddField("select")}
            className="flex flex-col h-auto py-3"
          >
            <SquareCheck className="mb-1 h-5 w-5" />
            <span className="text-xs">下拉选择</span>{" "}
          </Button>
          <Button
            variant="outline"
            onClick={() => onAddField("imageUpload")}
            className="flex flex-col h-auto py-3"
          >
            <Image className="mb-1 h-5 w-5" />
            <span className="text-xs">图片题</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
