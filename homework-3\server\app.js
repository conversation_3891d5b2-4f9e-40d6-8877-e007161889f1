const Koa = require("koa");
const bodyParser = require("koa-bodyparser");
const cors = require("@koa/cors");
const serve = require("koa-static");
const path = require("path");
const apiRouter = require("./routes/api");

const app = new Koa();

// 中间件
app.use(cors());
app.use(bodyParser());

// 静态文件服务 - 提供客户端构建文件
app.use(serve(path.join(__dirname, "../client/dist")));

// 路由
app.use(apiRouter.routes());
app.use(apiRouter.allowedMethods());

// 启动服务器
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
