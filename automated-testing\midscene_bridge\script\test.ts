import "dotenv/config";
import { chromium } from "playwright";
import { PlaywrightAgent } from "@midscene/web/playwright";

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

Promise.resolve(
  (async () => {
    // 启动浏览器
    const browser = await chromium.launch({
      headless: false,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    // 新建页面，设置视口大小
    const page = await browser.newPage();
    await page.setViewportSize({
      width: 1280,
      height: 768,
    });

    // 打开网站并创建 Midscene 智能代理
    await page.goto("https://www.saucedemo.com/");
    const agent = new PlaywrightAgent(page);

    // 断言页面标题包含 "Swag Labs"
    // await agent.aiAssert("页面标题包含 'Swag Labs'");

    // 智能操作：点击登录按钮，使用用户名 standard_user 和密码 secret_sauce 登录
    await agent.aiAction(
      '点击"Login"按钮，使用用户名 standard_user 和密码 secret_sauce 登录'
    );

    // 结构化提取商品信息，至少提取3条，包含名称、描述、价格
    const products = await agent.aiQuery(
      `{
            items: [{
              name: "商品名称",
              description: "商品描述",
              price: "价格|number"
            }]
          }`,
      { minItems: 3, timeout: 15000 }
    );
    console.log("提取到的商品列表:", products);

    // 筛选价格小于等于20的商品
    const cheapProducts = products.items.filter((p) => p.price <= 20);
    console.log("提取到的商品列表:", cheapProducts);
    if (cheapProducts.length === 0) {
      console.log("无符合条件商品");
      await browser.close();
      return; // 结束流程
    }

    // 计算符合条件商品总价
    let totalPrice = 0;

    // 依次选择符合条件的商品并加入购物车
    for (const product of cheapProducts) {
      await agent.aiAction(
        `点击价格为${product.price} 名称为${product.name}的商品卡片右下角的"Add to cart"按钮`
      );
      totalPrice += product.price;
    }
    // 点击购物车图标，进入结算流程
    await agent.aiAction("点击右上角购物车图标");

    // 断言购物车商品数量与加入的商品数一致
    // await agent.aiAssert(`购物车中商品数量为${cheapProducts.length}`);

    // 点击 checkout 按钮
    await agent.aiAction('点击"Checkout"按钮');

    // 断言结算页面显示正确
    // await agent.aiAssert("结算信息填写表单显示");

    // 填写结算信息
    await agent.aiAction('在"First Name"输入框输入"zhu');
    await agent.aiAction('在"Last Name"输入框输入"zhu');
    await agent.aiAction('在"Zip/Postal Code"输入框输入"123"');

    // 点击 Continue
    await agent.aiAction('点击"Continue"按钮');

    // 断言购物车页面显示的总价和计算总价相等
    const pageTotal = await agent.aiQuery('{ total: "Item total|number" }');

    // 这里做一个简单断言判断
    if (Math.abs(pageTotal.total - totalPrice) > 0.01) {
      throw new Error(
        `总价不匹配！页面显示 ${pageTotal.total}，计算得到 ${totalPrice}`
      );
    } else {
      console.log(`总价验证通过：${pageTotal.total}`);
    }

    // 关闭浏览器
    await browser.close();
  })()
);
