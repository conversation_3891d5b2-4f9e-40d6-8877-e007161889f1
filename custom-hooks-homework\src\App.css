#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.exam-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.exam-title {
  text-align: center;
  margin-bottom: 30px;
}

.question-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.question-text {
  font-size: 18px;
  margin-bottom: 16px;
}

.options-container {
  display: flex;
  flex-direction: column;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
}

.option-item:hover {
  background-color: #eaeaea;
}

.option-item.selected {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.option-item.correct,
.option-item.selected.correct {
  background-color: #f6ffed;
  border: 1px solid #52c41a;
}

.option-text {
  margin-left: 10px;
}

.submit-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 20px;
}

.submit-btn:hover {
  background-color: #40a9ff;
}

.result-container {
  margin-top: 30px;
  padding: 20px;
  background-color: #f6ffed;
  border-radius: 8px;
}

.score-text {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}

.answer-feedback {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  font-weight: bold;
}

@media (max-width: 768px) {
  .exam-container {
    padding: 10px;
  }

  .question-text {
    font-size: 16px;
  }
}
