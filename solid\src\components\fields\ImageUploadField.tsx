import { useState, useCallback, useRef, useEffect } from "react";
import type { ImageUploadField as ImageUploadFieldConfig } from "@/types/form";

interface ImageUploadFieldProps {
  field: ImageUploadFieldConfig;
  value: string;
  error?: string;
  onChange: (value: string) => void;
}

export function ImageUploadField({
  field,
  value,
  error,
  onChange,
}: ImageUploadFieldProps) {
  const [file, setFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [preview, setPreview] = useState<string>(value || "");
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (value && !file) {
      setPreview(value);
    }
  }, [value, file]);

  const handleFileChange = useCallback(
    (selectedFile: File | null) => {
      if (selectedFile) {
        const allowedTypes = ["image/jpeg", "image/png"];
        if (!allowedTypes.includes(selectedFile.type)) {
          alert("不支持的文件类型。请上传图片文件（JPG、PNG）。");
          onChange("");
          setFile(null);
          setPreview("");
          return;
        }
        if (selectedFile.size > (field.maxSizeMB || 10) * 1024 * 1024) {
          alert(`文件大小超过 ${field.maxSizeMB || 10}MB 限制。`);
          onChange("");
          setFile(null);
          setPreview("");
          return;
        }

        const fileUrl = URL.createObjectURL(selectedFile);
        setFile(selectedFile);
        setPreview(fileUrl);
        onChange(fileUrl);
      } else {
        setFile(null);
        setPreview("");
        onChange("");
      }
    },
    [onChange, field.maxSizeMB]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);
      if (e.dataTransfer.files && e.dataTransfer.files[0]) {
        handleFileChange(e.dataTransfer.files[0]);
      }
    },
    [handleFileChange]
  );

  const handleDrag = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleButtonClick = () => {
    inputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileChange(e.target.files[0]);
    }
  };

  const handleRemoveFile = () => {
    handleFileChange(null);
    if (inputRef.current) {
      inputRef.current.value = "";
    }
  };

  return (
    <div className="space-y-2">
      <div
        className={`relative flex min-h-[150px] cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-6 text-center transition-colors hover:border-gray-400 ${
          dragActive ? "border-blue-500 bg-blue-50" : ""
        } ${error ? "border-red-500" : ""}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={inputRef}
          type="file"
          className="hidden"
          accept="image/jpeg,image/png"
          onChange={handleFileInputChange}
        />
        {preview ? (
          <div className="relative h-full w-full">
            <img
              src={preview}
              alt="上传预览"
              className="max-h-[150px] w-auto object-contain"
            />
            <button
              type="button"
              className="absolute -right-2 -top-2 text-red-500 hover:bg-white hover:text-red-600 w-6 h-6 flex items-center justify-center"
              onClick={handleRemoveFile}
            >
              ×
            </button>
          </div>
        ) : (
          <>
            <p className="text-sm text-gray-500">将图片拖拽或粘贴至此处</p>
          </>
        )}
      </div>

      <button
        type="button"
        className="flex items-center text-sm mt-2 px-2 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
        onClick={handleButtonClick}
      >
        <svg viewBox="0 0 1024 1024" className="w-3 h-3 mr-1">
          <path d="M924 1024H100A100 100 0 0 1 0 924V638a40 40 0 0 1 80 0v286a20 20 0 0 0 20 20h824a20 20 0 0 0 20-20V638a40 40 0 0 1 80 0v286a100 100 0 0 1-100 100zM784 352a40 40 0 0 1-28-12L512 97 268 340a40 40 0 0 1-57-57L484 12a40 40 0 0 1 57 0l271 272a40 40 0 0 1-28 68z"></path>
          <path d="M512 788a40 40 0 0 1-40-40V57a40 40 0 0 1 80 0v691a40 40 0 0 1-40 40z"></path>
        </svg>
        上传图片
      </button>

      <p className="text-sm text-gray-500">
        最多上传{field.maxFiles || 1}张图片，单张图片{field.maxSizeMB || 10}
        MB以内
      </p>

      {error && <div className="text-sm text-red-500">{error}</div>}
    </div>
  );
}
