import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import LoginForm from "../LoginForm";
import instance from "@/utils/request";

// 模拟整个 axios 实例模块
vi.mock("@/utils/request");

describe("登录表单组件 (LoginForm)", () => {
  const mockOnLoginSuccess = vi.fn();

  beforeEach(() => {
    // 在每个测试用例运行前，重置所有模拟
    vi.clearAllMocks();
  });
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("应该能正确渲染初始状态", () => {
    render(<LoginForm onLoginSuccess={mockOnLoginSuccess} />);
    expect(screen.getByPlaceholderText("手机号码")).toBeInTheDocument();
    expect(screen.getByText("点击按钮开始智能验证")).toBeInTheDocument();
  });

  it("输入手机号后，点击验证按钮应该切换到验证码输入步骤", async () => {
    render(<LoginForm onLoginSuccess={mockOnLoginSuccess} />);

    const phoneInput = screen.getByPlaceholderText("手机号码");
    fireEvent.change(phoneInput, { target: { value: "13800138000" } });

    const captchaButton = screen.getByText("点击按钮开始智能验证");
    fireEvent.click(captchaButton);

    // 等待智能验证完成并且验证码输入框出现
    await waitFor(
      () => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      },
      { timeout: 3000 }
    ); // 增加超时时间确保验证有足够时间完成
  });

  it("在填写验证码并点击登录后，应该调用 onLoginSuccess 回调", async () => {
    // 模拟 API 成功响应
    const mockVerifyResponse = { result: "ok", ssid: "test-ssid" };
    const mockUsersResponse = { users: [{ userid: 1, nickname: "测试用户" }] };

    // 配置模拟的 post 和 get 方法
    vi.mocked(instance.post).mockResolvedValue(mockVerifyResponse);
    vi.mocked(instance.get).mockResolvedValue(mockUsersResponse);

    render(<LoginForm onLoginSuccess={mockOnLoginSuccess} isMobile={false} />);

    // --- 步骤 1: 输入手机号并通过验证
    fireEvent.change(screen.getByPlaceholderText("手机号码"), {
      target: { value: "13800138000" },
    });
    fireEvent.click(screen.getByText("点击按钮开始智能验证"));
    await waitFor(
      () => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      },
      { timeout: 3000 }
    );

    // --- 步骤 2: 输入验证码并点击登录
    fireEvent.change(screen.getByPlaceholderText("短信验证码"), {
      target: { value: "123456" },
    });
    fireEvent.click(screen.getByRole("button", { name: "立即登录/注册" }));

    // 等待异步的 onLoginSuccess 回调被调用
    await waitFor(() => {
      expect(mockOnLoginSuccess).toHaveBeenCalledTimes(1);
    });

    // 验证回调函数的参数是否正确
    expect(mockOnLoginSuccess).toHaveBeenCalledWith(
      mockUsersResponse.users, // 用户数组
      expect.any(Array) // 默认选中的ID数组
    );
  });

  it("当 API 返回错误时，应该显示错误信息", async () => {
    // 模拟 API 失败响应
    vi.mocked(instance.post).mockResolvedValue({
      result: "InvalidSMSCode",
      msg: "验证码不正确",
    });

    render(<LoginForm onLoginSuccess={mockOnLoginSuccess} />);

    // ... (执行输入和点击操作)
    fireEvent.change(screen.getByPlaceholderText("手机号码"), {
      target: { value: "13800138000" },
    });
    fireEvent.click(screen.getByText("点击按钮开始智能验证"));
    await waitFor(
      () => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      },
      { timeout: 3000 }
    );
    fireEvent.change(screen.getByPlaceholderText("短信验证码"), {
      target: { value: "wrong-code" },
    });
    fireEvent.click(screen.getByRole("button", { name: "立即登录/注册" }));

    // 等待错误信息出现
    await waitFor(() => {
      expect(screen.getByText(/验证码不正确/)).toBeInTheDocument();
    });
  });
});
