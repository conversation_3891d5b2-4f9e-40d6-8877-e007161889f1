/* 通用样式 */
.login-form-container {
  width: 100%;
}

.login-form-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 44px;
  margin-bottom: 1rem;
}

.back-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #333;
  padding: 0;
  margin-bottom: 1.5rem;
  z-index: 10;
}

.login-form-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  padding: 0 40px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.login-form-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 手机号输入 */
.phone-input-group {
  display: flex;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.country-select {
  width: 80px;
  border: none;
  background-color: #f5f5f5;
  padding: 0 10px;
  font-size: 14px;
}

.phone-input {
  flex: 1;
  border: none;
  padding: 12px;
  font-size: 14px;
  outline: none;
}

/* 智能验证按钮 */
.captcha-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.captcha-button:hover:not(:disabled) {
  background-color: #ebebeb;
}

.captcha-button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.captcha-button.loading {
  background-color: #e8f0fe;
}

.captcha-button.completed {
  background-color: #e6f7e6;
}

.captcha-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.captcha-text {
  font-size: 14px;
}

/* 验证码输入 */
.verification-input-group {
  display: flex;
  gap: 10px;
}

.verification-input {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  outline: none;
}

.send-code-btn {
  width: 120px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
}

.send-code-btn:hover:not(:disabled) {
  background-color: #ebebeb;
}

.send-code-btn:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

/* 错误信息 */
.error-message {
  color: #f44336;
  font-size: 12px;
  margin: 5px 0;
  text-align: left;
}

/* 自动登录 */
.auto-login-container {
  display: flex;
  align-items: center;
}

.auto-login-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.auto-login-checkbox {
  margin-right: 8px;
}

.auto-login-text {
  font-size: 14px;
  color: #666;
}

/* 提交按钮 */
.submit-button {
  background-color: #417ff9;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.submit-button:hover:not(:disabled) {
  background-color: #1d4ed8;
}

.submit-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.footer-text {
  font-size: 12px;
  color: #999;
  text-align: center;
  margin-top: 10px;
}

/* 移动端样式调整 */
.login-form-container.mobile .login-form-title {
  font-size: 16px;
}

.login-form-container.mobile .country-select {
  width: 70px;
  font-size: 13px;
}

.login-form-container.mobile .phone-input,
.login-form-container.mobile .verification-input {
  padding: 10px;
  font-size: 13px;
}

.login-form-container.mobile .send-code-btn {
  width: 100px;
  font-size: 13px;
}

.login-form-container.mobile .submit-button {
  font-size: 15px;
  padding: 10px;
}

.login-form-container.mobile .auto-login-text {
  font-size: 13px;
}

.login-form-container.mobile .form-fields {
  gap: 12px;
}
