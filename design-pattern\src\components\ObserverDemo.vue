<template>
  <div>
    <h2>观察者模式演示</h2>
    <button @click="notify">发送通知</button>
    <p v-if="message">收到消息: {{ message }}</p>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from 'vue'
import { eventBus } from '@/utils/EventBus'

export default defineComponent({
  setup() {
    const message = ref('')

    const handleNotification = (msg: string) => {
      message.value = msg
    }

    // 组件挂载时订阅事件
    onMounted(() => {
      eventBus.on('notification', handleNotification)
    })

    // 组件卸载时取消订阅
    onUnmounted(() => {
      eventBus.off('notification', handleNotification)
    })

    const notify = () => {
      eventBus.emit('notification', `当前时间: ${new Date().toLocaleString()}`)
    }

    return { message, notify }
  },
})
</script>
