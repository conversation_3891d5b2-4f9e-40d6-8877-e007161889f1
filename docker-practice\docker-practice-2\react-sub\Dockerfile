# 使用 node 镜像构建 React 子应用
FROM node:20-alpine as builder

# 设置工作目录
WORKDIR /app

# 复制依赖文件并安装依赖
COPY package.json package-lock.json ./
RUN npm install

# 复制源代码
COPY . .

# 构建 React 应用
RUN npm run build

# 使用 nginx 镜像作为生产环境
FROM nginx:alpine

# 拷贝构建后的 React 子应用到 nginx 服务目录
COPY --from=builder /app/build /usr/share/nginx/html

# 拷贝自定义 nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露容器端口
EXPOSE 80
