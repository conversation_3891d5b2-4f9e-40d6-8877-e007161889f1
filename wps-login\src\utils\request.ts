import axios from "axios";
import type {
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";
import qs from "qs";

// 实例的get和post方法直接返回数据T，而不是AxiosResponse<T>
interface CustomInstance extends AxiosInstance {
  get<T = any>(url: string, config?: any): Promise<T>;
  post<T = any>(url: string, data?: any, config?: any): Promise<T>;
}

// 创建axios实例
const instance: CustomInstance = axios.create({
  baseURL: "/",
  timeout: 10000,
  withCredentials: true, // 允许携带cookie
  headers: {
    "Content-Type": "application/x-www-form-urlencoded",
  },
});

// 请求拦截器
instance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 如果是表单数据格式，使用qs序列化
    if (
      config.headers["Content-Type"] === "application/x-www-form-urlencoded" &&
      config.data &&
      typeof config.data === "object"
    ) {
      config.data = qs.stringify(config.data);
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  <T = any>(response: AxiosResponse<T>) => {
    // 现在response.data的类型是T，res的类型也是T
    const res = response.data;
    // 可以统一处理响应数据
    return res;
  },
  (error) => {
    // 统一错误处理
    return Promise.reject(error);
  }
);

// 导出axios实例
export default instance;
