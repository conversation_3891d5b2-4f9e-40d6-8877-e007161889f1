import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import MoreLogin from "../MoreLogin";

describe("更多登录方式组件 (MoreLogin)", () => {
  // 模拟回调函数
  const mockOnBack = vi.fn();

  // 每个测试前重置模拟函数
  beforeEach(() => {
    mockOnBack.mockReset();
    // 替换原生的alert方法
    vi.spyOn(window, "alert").mockImplementation(() => {});
  });

  it("应该正确渲染标题和返回按钮", () => {
    render(<MoreLogin onBack={mockOnBack} />);

    // 验证标题
    expect(screen.getByText("更多登录方式")).toBeInTheDocument();

    // 验证返回按钮
    expect(screen.getByText("返回")).toBeInTheDocument();
  });

  it("点击返回按钮时应调用 onBack 回调函数", () => {
    render(<MoreLogin onBack={mockOnBack} />);

    const backButton = screen.getByText("返回");
    fireEvent.click(backButton);

    expect(mockOnBack).toHaveBeenCalledTimes(1);
  });

  it("应该显示所有登录选项", () => {
    render(<MoreLogin onBack={mockOnBack} />);

    // 验证所有登录选项都显示在页面上
    expect(screen.getByText("账号密码")).toBeInTheDocument();
    expect(screen.getByText("WPS扫码")).toBeInTheDocument();
    expect(screen.getByText("华为账号")).toBeInTheDocument();
    expect(screen.getByText("钉钉账号")).toBeInTheDocument();
    expect(screen.getByText("Apple账号")).toBeInTheDocument();
    expect(screen.getByText("小米账号")).toBeInTheDocument();
    expect(screen.getByText("微博账号")).toBeInTheDocument();
    expect(screen.getByText("教育云账号")).toBeInTheDocument();
  });

  it("点击登录选项时应该调用相应的处理函数", () => {
    render(<MoreLogin onBack={mockOnBack} />);

    // 测试点击账号密码选项
    fireEvent.click(screen.getByText("账号密码"));
    expect(window.alert).toHaveBeenCalledWith("账号密码");

    // 测试点击WPS扫码选项
    fireEvent.click(screen.getByText("WPS扫码"));
    expect(window.alert).toHaveBeenCalledWith("WPS扫码");

    // 测试点击华为账号选项
    fireEvent.click(screen.getByText("华为账号"));
    expect(window.alert).toHaveBeenCalledWith("华为账号");

    // 测试其他选项
    fireEvent.click(screen.getByText("钉钉账号"));
    expect(window.alert).toHaveBeenCalledWith("钉钉账号");
  });

  it("每个登录选项应该包含图标和文本", () => {
    const { container } = render(<MoreLogin onBack={mockOnBack} />);

    // 获取所有登录选项
    const loginOptions = container.querySelectorAll(".login-option");

    // 验证每个选项都包含图标和文本
    loginOptions.forEach((option) => {
      expect(option.querySelector(".login-option-icon")).toBeInTheDocument();
      expect(option.querySelector(".login-option-text")).toBeInTheDocument();
    });
  });

  it("组件布局结构应该符合预期", () => {
    const { container } = render(<MoreLogin onBack={mockOnBack} />);

    // 验证主要容器元素
    expect(container.querySelector(".more-login-section")).toBeInTheDocument();
    expect(container.querySelector(".more-login-form")).toBeInTheDocument();
    expect(container.querySelector(".more-login-header")).toBeInTheDocument();
    expect(container.querySelector(".more-login-content")).toBeInTheDocument();
  });
});
