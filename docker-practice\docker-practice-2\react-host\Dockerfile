# 使用 node 镜像构建基应用
FROM node:20-alpine as builder

# 设置工作目录
WORKDIR /app

# 将项目依赖文件复制到容器内
COPY package.json package-lock.json ./

# 安装依赖
RUN npm install

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 使用轻量级的 nginx 镜像作为生产环境
FROM nginx:alpine


# 拷贝构建好的前端应用到 nginx 服务目录
COPY --from=builder /app/build /usr/share/nginx/html

# 拷贝自定义 nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露容器端口
EXPOSE 80
