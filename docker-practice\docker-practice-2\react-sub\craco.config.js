const path = require("path");
const { name } = require("./package.json");

module.exports = {
  webpack: {
    configure: (config) => {
      config.output.library = { name: `${name}-[name]`, type: "umd" };
      config.output.chunkLoadingGlobal = `webpackJsonp_${name}`;
      config.output.globalObject = "window";
      return config;
    },
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  devServer: {
    // port: 3001,
    open: false,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    historyApiFallback: true,
    hot: true,
    liveReload: false,
  },
};
