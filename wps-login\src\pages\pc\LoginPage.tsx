import { useState } from "react";
import "./LoginPage.css";
import ConsentModal from "@/components/common/consent/ConsentModal";
import MoreLogin from "@/components/moreLogin/MoreLogin";
import PhoneLogin from "@/components/phoneLogin/PhoneLogin";

export default function LoginPage() {
  const [autoLogin, setAutoLogin] = useState(false);
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showMoreLogin, setShowMoreLogin] = useState(false);
  const [showPhoneLogin, setShowPhoneLogin] = useState(false);

  // 处理登录方式点击事件
  const handleLoginMethodClick = (method: string) => {
    // 如果没有同意隐私协议且点击的是"手机"或"更多"，则显示弹窗
    if (!agreeTerms && (method === "phone" || method === "more")) {
      setShowModal(true);
      // 记录当前点击的方法，以便在同意隐私协议后跳转到对应页面
      sessionStorage.setItem("lastClickedMethod", method);
    } else {
      // 如果点击的是"更多"且已同意隐私协议，则显示更多登录页面
      if (method === "more" && agreeTerms) {
        setShowMoreLogin(true);
      }

      // 如果点击的是"手机"且已同意隐私协议，则显示手机登录页面
      if (method === "phone" && agreeTerms) {
        setShowPhoneLogin(true);
      }
    }
  };

  // 处理返回主登录页的回调函数
  const handleBackToMain = () => {
    setShowMoreLogin(false);
    setShowPhoneLogin(false);
  };

  return (
    <>
      <div className="login-container">
        <div className="login-content">
          {/* 左侧Logo区域 */}
          <div className="logo-section">
            <div className="logo-wrapper">
              <div className="logo"></div>
            </div>
          </div>

          {/* 右侧登录区域 - 根据状态显示不同内容 */}
          {showPhoneLogin ? (
            <PhoneLogin onBack={handleBackToMain} />
          ) : showMoreLogin ? (
            <MoreLogin onBack={handleBackToMain} />
          ) : (
            <div className="login-section">
              <div className="login-form">
                <div className="login-header">
                  <h2 className="login-title">微信扫码登录</h2>
                  <p className="login-subtitle">使用金山办公在线服务账号登录</p>
                </div>

                <div className="qr-container">
                  <div className="qr-placeholder"></div>
                </div>

                <div className="checkbox-group">
                  <div className="checkbox-item">
                    <input
                      type="checkbox"
                      id="auto-login"
                      className="checkbox"
                      checked={autoLogin}
                      onChange={(e) => setAutoLogin(e.target.checked)}
                    />
                    <label htmlFor="auto-login" className="checkbox-label">
                      自动登录
                    </label>
                  </div>
                  <div className="checkbox-item">
                    <input
                      type="checkbox"
                      id="agree-terms"
                      className="checkbox"
                      checked={agreeTerms}
                      onChange={(e) => setAgreeTerms(e.target.checked)}
                    />
                    <label htmlFor="agree-terms" className="checkbox-label">
                      已阅读并同意
                      <a href="#"> 隐私政策</a> 和<a href="#"> 在线服务协议</a>
                    </label>
                  </div>
                </div>

                <div className="login-methods">
                  <a
                    className="login-item"
                    onClick={() => handleLoginMethodClick("qq")}
                  >
                    <span className="login-icon qq-icon"></span>
                    <span className="login_icon_txt">QQ账号</span>
                  </a>
                  <a
                    className="login-item"
                    onClick={() => handleLoginMethodClick("phone")}
                  >
                    <span className="login-icon phone-icon"></span>
                    <span className="login_icon_txt">手机</span>
                  </a>
                  <a
                    className="login-item"
                    onClick={() => handleLoginMethodClick("sso")}
                  >
                    <span className="login-icon sso-icon"></span>
                    <span className="login_icon_txt">专属账号</span>
                  </a>
                  <a
                    className="login-item"
                    onClick={() => handleLoginMethodClick("more")}
                  >
                    <span className="login-icon more-icon"></span>
                    <span className="login_icon_txt">更多</span>
                  </a>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <ConsentModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onAgree={() => {
          setAgreeTerms(true);
          setShowModal(false);

          // 根据上次点击的方法跳转到对应页面
          const lastMethod = sessionStorage.getItem("lastClickedMethod");
          if (lastMethod === "more") {
            setShowMoreLogin(true);
          } else if (lastMethod === "phone") {
            setShowPhoneLogin(true);
          }

          // 清除记录
          sessionStorage.removeItem("lastClickedMethod");
        }}
        onCancel={() => setShowModal(false)}
      />
    </>
  );
}
