import MyString from '../src/myString';
import { describe, it, expect } from '@jest/globals';
describe('MyString', () => {
    it('should create an empty MyString by default', () => {
        const s = new MyString();
        expect(s.toString()).toBe('');
        expect(s.length).toBe(0);
    });

    it('should create a MyString with initial value', () => {
        const s = new MyString('hello');
        expect(s.toString()).toBe('hello');
        expect(s.length).toBe(5);
    });

    it('charAt should return correct character', () => {
        const s = new MyString('abc');
        expect(s.charAt(0)).toBe('a');
        expect(s.charAt(2)).toBe('c');
        expect(s.charAt(3)).toBe('');
    });

    it('concat should concatenate strings', () => {
        const s = new MyString('foo');
        const result = s.concat('bar', 'baz');
        expect(result).toBeInstanceOf(MyString);
        expect(result.toString()).toBe('foobarbaz');
    });

    it('includes should work as expected', () => {
        const s = new MyString('hello world');
        expect(s.includes('world')).toBe(true);
        expect(s.includes('foo')).toBe(false);
        expect(s.includes('o', 5)).toBe(true);
    });

    it('indexOf should return correct index', () => {
        const s = new MyString('banana');
        expect(s.indexOf('a')).toBe(1);
        expect(s.indexOf('a', 2)).toBe(3);
        expect(s.indexOf('z')).toBe(-1);
    });

    it('slice should return a new MyString with sliced value', () => {
        const s = new MyString('abcdef');
        const sliced = s.slice(1, 4);
        expect(sliced).toBeInstanceOf(MyString);
        expect(sliced.toString()).toBe('bcd');
    });

    it('split should split the string', () => {
        const s = new MyString('a,b,c');
        expect(s.split(',')).toEqual(['a', 'b', 'c']);
        expect(s.split(',', 2)).toEqual(['a', 'b']);
    });

    it('toUpperCase should return a new MyString in upper case', () => {
        const s = new MyString('abc');
        const upper = s.toUpperCase();
        expect(upper).toBeInstanceOf(MyString);
        expect(upper.toString()).toBe('ABC');
    });

    it('toLowerCase should return a new MyString in lower case', () => {
        const s = new MyString('ABC');
        const lower = s.toLowerCase();
        expect(lower).toBeInstanceOf(MyString);
        expect(lower.toString()).toBe('abc');
    });

    it('trim should return a new MyString with trimmed value', () => {
        const s = new MyString('  hello  ');
        const trimmed = s.trim();
        expect(trimmed).toBeInstanceOf(MyString);
        expect(trimmed.toString()).toBe('hello');
    });

    it('length should return the correct length', () => {
        const s = new MyString('test');
        expect(s.length).toBe(4);
    });
});