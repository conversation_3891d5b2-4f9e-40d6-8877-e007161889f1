<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Flex Layout</title>
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
        min-width: 800px;
      }
      .layout {
        display: flex;
        flex-direction: column;
        height: 100vh;
        width: 100%;
      }

      .header {
        background-color: #001627;
        color: #f9fafb;
        width: 100%;
        height: 80px;
        flex-shrink: 0;
        font-size: 24px;
        display: flex;
        align-items: center;
        padding-left: 20px;
      }

      .container {
        display: flex;
        flex: 1;
        overflow: auto;
      }

      .sidebar {
        width: 200px;
        background-color: #001627;
        color: white;
        flex-shrink: 0;
      }

      .sidebar .item {
        color: #f9fdff;
        margin-bottom: 1rem;
      }

      .nav-link:hover {
        background-color: #334155;
      }
      .nav-link.active {
        background-color: #1878ff;
        font-weight: 500;
      }
      .nav-icon {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .nav-icon-selected {
        width: 16px;
        height: 16px;
        border: 1px solid white;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .nav-icon-dot {
        width: 8px;
        height: 8px;
        background-color: white;
        border-radius: 2px;
      }

      .nav-link {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
      }

      /* 主内容区域样式 */
      .main-content {
        display: flex;
        width: 100%;
        flex-direction: column;
        overflow-x: auto;
        flex: 1;
      }

      /* 搜索表单区域样式 */
      .search-form {
        display: flex;
        box-sizing: border-box;
        background-color: white;
        margin: 24px;
        padding: 24px;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
      }

      .form-row {
        display: flex;
        align-items: center;
        gap: 24px;
        justify-content: space-between;
        flex-wrap: wrap;
      }

      .form-group {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .form-label {
        font-size: 14px;
        color: #374151;
        white-space: nowrap;
      }

      .form-input {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        outline: none;
        transition: border-color 0.2s;
      }

      .form-input:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
      }

      .form-input::placeholder {
        color: #9ca3af;
      }

      .form-select {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        background-color: white;
        cursor: pointer;
        outline: none;
        transition: border-color 0.2s;
      }

      .form-select:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
      }

      /* 按钮组样式 */
      .button-group {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: wrap;
        margin-left: auto;
      }

      .btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
        border: 1px solid transparent;
        white-space: nowrap;
      }

      .btn-primary {
        background-color: #2563eb;
        color: white;
        border-color: #2563eb;
      }

      .btn-primary:hover {
        background-color: #1d4ed8;
        border-color: #1d4ed8;
      }

      .btn-outline {
        background-color: white;
        color: #374151;
        border-color: #d1d5db;
      }

      .btn-outline:hover {
        background-color: #f9fafb;
        border-color: #9ca3af;
      }

      .btn-outline-blue {
        background-color: transparent;
        color: #2563eb;
        border-color: #bfdbfe;
      }

      .btn-outline-blue:hover {
        background-color: #eff6ff;
      }

      .btn-icon {
        width: 16px;
        height: 16px;
      }

      /* 内容区域样式 */
      .content-area {
        flex: 1;
        padding: 24px;
      }

      .content-placeholder {
        background-color: white;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .placeholder-content {
        text-align: center;
        color: #6b7280;
      }

      .placeholder-title {
        font-size: 18px;
        margin-bottom: 8px;
      }

      .placeholder-subtitle {
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div class="layout">
      <header class="header">研发培训院课件系统</header>

      <div class="container">
        <!-- 左侧边栏 -->
        <aside class="sidebar">
          <div class="item">
            <div class="nav-link active">
              <div class="nav-icon-selected">
                <div class="nav-icon-dot"></div>
              </div>
              <span>笔试管理</span>
            </div>
          </div>
        </aside>
        <!-- 主内容区域  -->
        <main class="main-content">
          <!-- 搜索表单区域  -->
          <div class="search-form">
            <div class="form-row">
              <!--  笔试名称  -->
              <div class="form-group">
                <label class="form-label">笔试名称：</label>
                <input
                  type="text"
                  class="form-input"
                  placeholder="请输入笔试名称"
                />
                <label class="form-label">技术方向：</label>
                <select class="form-select">
                  <option value="">请选择技术方向</option>
                  <option value="frontend">前端开发</option>
                  <option value="backend">后端开发</option>
                  <option value="mobile">移动开发</option>
                </select>
              </div>

              <!--  操作按钮  -->
              <div class="button-group">
                <button class="btn btn-primary">
                  <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"
                    />
                  </svg>
                  查询
                </button>
                <button class="btn btn-outline">
                  <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"
                    />
                  </svg>
                  重置
                </button>
                <button class="btn btn-outline-blue">
                  <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                  </svg>
                  智能一键组卷
                  <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7 10l5 5 5-5z" />
                  </svg>
                </button>
                <button class="btn btn-primary">
                  <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                  </svg>
                  创建笔试
                  <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7 10l5 5 5-5z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- 内容区域  -->
          <div class="content-area">
            <div class="content-placeholder">
              <div class="placeholder-content">
                <div class="placeholder-title">暂无数据</div>
                <div class="placeholder-subtitle">
                  请使用上方搜索条件查询或创建新的笔试
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </body>
</html>
