* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f9fafb;
  height: 100vh;
  overflow: hidden;
}

.chat-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* 左侧聊天列表 */
.sidebar {
  width: 320px;
  height: 100vh;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
}

.search-bar {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.search-input-container {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  background: #f3f4f6;
  border: none;
  border-radius: 6px;
  outline: none;
  font-size: 14px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 16px;
}

.add-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6b7280;
}

.add-btn:hover {
  background: #f3f4f6;
}

.chat-list {
  flex: 1;
  overflow-y: auto;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-item:hover {
  background: #f9fafb;
}

.chat-item.active {
  background: #eff6ff;
  border-right: 2px solid #3b82f6;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  position: relative;
}

.chat-info {
  margin-left: 12px;
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.chat-name {
  font-weight: 500;
  font-size: 14px;
  color: #111827;
}

.chat-preview {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 右侧聊天区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header-bar {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-details h3 {
  font-weight: 500;
  font-size: 16px;
  color: #111827;
}

.header-details p {
  font-size: 12px;
  color: #6b7280;
}

.more-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  color: #6b7280;
}

.more-btn:hover {
  background: #f3f4f6;
}

.messages-area {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.no-messages {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
  font-size: 14px;
}

.message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.message-sender {
  font-weight: 500;
  font-size: 14px;
  color: #111827;
}

.message-id {
  font-size: 12px;
  color: #6b7280;
}

.message-time {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.message-bubble {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  max-width: 500px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.message-text {
  font-size: 14px;
  color: #111827;
  line-height: 1.5;
}

/* 富文本消息样式 */
.message-text p {
  margin: 0 0 8px 0;
}

.message-text p:last-child {
  margin-bottom: 0;
}

.message-text strong {
  font-weight: 600;
}

.message-text em {
  font-style: italic;
}

.message-text ul,
.message-text ol {
  margin: 8px 0;
  padding-left: 20px;
}

.message-text blockquote {
  border-left: 4px solid #e5e7eb;
  margin: 8px 0;
  padding-left: 12px;
  color: #6b7280;
}

.message-text img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 4px 0;
}

/* 输入区域 */
.input-area {
  background: white;
  border-top: 1px solid #e5e7eb;
  min-height: 400px;
  padding: 8px;
  box-sizing: border-box; /* 防止内边距撑开盒子 */
}

.input-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  color: #6b7280;
  font-size: 16px;
}

.toolbar-btn:hover {
  background: #f3f4f6;
}

.send-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.send-btn:hover {
  background: #2563eb;
}

.send-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Quill编辑器样式调整 */
.editor-container {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  min-height: 300px;
}

.ql-toolbar {
  border: none;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px 12px;
}

.ql-container {
  border: none;
  font-size: 14px;
}

.ql-editor {
  min-height: 300px;
  max-height: 400px;
  padding: 12px;
  font-size: 14px;
}

.ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: normal;
}

/* 自定义工具栏按钮样式 */
.ql-toolbar .ql-formats {
  margin-right: 8px;
}

.ql-toolbar button {
  width: 28px;
  height: 28px;
}

.ql-toolbar button:hover {
  color: #f8f9fa;
}

.ql-toolbar .ql-active {
  color: #f8f9fa;
}

.ql-mention-list-container {
  width: 270px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(30, 30, 30, 0.08);
  z-index: 9001;
  overflow-y: auto;
  max-height: 300px;
}

.ql-mention-loading {
  line-height: 44px;
  padding: 0 20px;
  vertical-align: middle;
  font-size: 16px;
  color: #999;
}

.ql-mention-list {
  background-color: #f8f9fa;
  list-style: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
  max-height: 250px;
  overflow-y: auto;
  overflow-x: auto;
}

.ql-mention-list-item {
  cursor: pointer;
  padding: 10px 10px;
  display: flex;
  align-items: center;
  transition: #fff 0.2s ease;
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
  max-width: 100%; /* 限制最大宽度 */
  box-sizing: border-box;
  overflow: hidden; /* 防止内容撑开 */
}

.ql-mention-list-item:last-child {
  border-bottom: none;
}

/* 头像容器 */
.ql-mention-list-item .avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
  overflow: hidden;
  background-color: #f0f0f0;
}

.ql-mention-list-item .avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 联系人信息容器 */
.ql-mention-list-item .contact-info {
  flex: 1;
  min-width: 0;
}

/* 联系人姓名 */
.ql-mention-list-item .contact-name {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 2px;
}

/* 联系人部门/职位 */
.ql-mention-list-item .contact-department {
  font-size: 13px;
  color: #999;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 左侧图标容器 */
.ql-mention-list-item .icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 12px;
  flex-shrink: 0;
  overflow: hidden;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ql-mention-list-item .icon .doc-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ql-mention-list-item .icon .doc-icon svg {
  width: 100%;
  height: 100%;
}

.ql-mention-list-item .doc-info {
  flex: 1;
  min-width: 0; /* 关键：允许子元素缩小 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止溢出 */
}

/* 文档名称 */
.ql-mention-list-item .doc-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

/* 文档元信息 */
.ql-mention-list-item .doc-meta {
  font-size: 13px;
  color: #999;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 悬停时的工具提示样式 */
.ql-mention-list-item .doc-name[title]:hover {
  position: relative;
}

/* 悬停状态 */
.ql-mention-list-item:hover,
.ql-mention-list-item:hover * {
  background-color: #f8f9fa !important; /* 强制覆盖蓝色 */
  box-shadow: none !important;
  outline: none !important;
}
/* 选中状态 */
.ql-mention-list-item.selected,
.ql-mention-list-item:selected * {
  background-color: #e6f7ff;
}

.mention {
  height: 24px;
  width: auto;
  min-width: 65px;
  border-radius: 6px;
  background-color: #d3e1eb;
  padding: 3px 8px;
  margin-right: 2px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
  user-select: none;
}

.mention > span {
  margin: 0 3px;
  color: #333;
}

.ql-editor .mention {
  cursor: pointer;
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 500;
}

#mention-card {
  position: fixed;
  z-index: 9999;
  background-color: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: sans-serif;
  transition: opacity 0.2s;
  max-width: 300px;
}

.mention-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  background: #e5e7eb;
}

.mention-name {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.mention-id {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}
