### 主进程 (main.js)

* ipcMain.handle() - 处理来自渲染进程的异步请求

### 预加载脚本 (preload.js)

* contextBridge.exposeInMainWorld() - 安全地暴露API给渲染进程
* ipcRenderer.invoke() - 向主进程发送异步请求

### 渲染进程 (index.html中的JavaScript)

* window.sdk.readFile() - 调用文件读取API
* window.sdk.openUrl() - 调用URL打开API
* window.electronAPI.showFileDialog() - 调用文件选择API。以上方法为异步
* window.electronAPI.onOpenInWebview() - 注册事件回调，监听内嵌webview事件。是同步的

### 调用-响应模式流程（双向通信）

![alt text](方法调用流程.png)

1. 渲染进程调用暴露的 API 方法
2. 预加载脚本通过 invoke 发送 IPC 请求
3. 主进程使用 handle 处理请求并返回
4. 响应通过 Promise 返回到渲染进程

### 事件监听模式流程（单向通信）

![alt text](事件监听流程.png)

1. 渲染进程注册事件回调
2. 预加载脚本建立 IPC 监听通道
3. 主进程主动发送事件消息
4. 预加载脚本触发渲染进程回调
