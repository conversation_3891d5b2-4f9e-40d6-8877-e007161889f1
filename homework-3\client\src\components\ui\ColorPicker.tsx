import { useState, useRef, useEffect } from "react";
import { SketchPicker } from "react-color";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";

// System colors palette
const systemColors = [
  // Row 1 - Light colors
  [
    "#f8f9fa",
    "#e9ecef",
    "#dee2e6",
    "#ffc9c9",
    "#ffd8a8",
    "#fff3bf",
    "#d3f9d8",
    "#c5f6fa",
    "#d0ebff",
    "#e5dbff",
  ],
  // Row 2 - Medium light
  [
    "#f1f3f4",
    "#ced4da",
    "#adb5bd",
    "#ffa8a8",
    "#ffb366",
    "#ffec99",
    "#b2f2bb",
    "#99e9f2",
    "#a5d8ff",
    "#d0bfff",
  ],
  // Row 3 - Medium
  [
    "#868e96",
    "#6c757d",
    "#495057",
    "#ff8787",
    "#ff922b",
    "#ffd43b",
    "#8ce99a",
    "#66d9ef",
    "#74c0fc",
    "#b197fc",
  ],
  // Row 4 - Medium dark
  [
    "#5c6670",
    "#495057",
    "#343a40",
    "#ff6b6b",
    "#fd7e14",
    "#fab005",
    "#51cf66",
    "#22b8cf",
    "#339af0",
    "#9775fa",
  ],
  // Row 5 - Dark
  [
    "#3c4142",
    "#212529",
    "#000000",
    "#e03131",
    "#d9480f",
    "#e67700",
    "#37b24d",
    "#0c8599",
    "#1971c2",
    "#7048e8",
  ],
];

// Bottom row special colors
const specialColors = [
  "#6c757d",
  "#000000",
  "#e03131",
  "#fd7e14",
  "#fab005",
  "#37b24d",
  "#0c8599",
  "#1971c2",
  "#7048e8",
];

interface ColorPickerProps {
  selectedColor?: string;
  onColorChange?: (color: string) => void;
  disabled?: boolean;
}

export default function ColorPicker({
  selectedColor = "#000000",
  onColorChange,
  disabled = false,
}: ColorPickerProps) {
  const [currentColor, setCurrentColor] = useState(selectedColor);
  const [hexInput, setHexInput] = useState(selectedColor.replace("#", ""));
  const [recentColors, setRecentColors] = useState<string[]>(["#495057"]);
  const [showAdvancedPicker, setShowAdvancedPicker] = useState(false);
  const [open, setOpen] = useState(false);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleShowAdvancedPicker = () => {
    console.log("显示高级拾色器"); // 调试用
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }
    setShowAdvancedPicker(true);
    setOpen(true); // 确保下拉菜单保持打开
  };

  const handleHideAdvancedPicker = () => {
    console.log("隐藏高级拾色器"); // 调试用
    hideTimeoutRef.current = setTimeout(() => {
      setShowAdvancedPicker(false);
    }, 300);
  };

  useEffect(() => {
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (selectedColor) {
      // 更新内部颜色状态以反映外部传入的颜色
      setCurrentColor(selectedColor);
      setHexInput(selectedColor.replace("#", ""));
    }
  }, [selectedColor]);

  const handleColorSelect = (color: string) => {
    setCurrentColor(color);
    setHexInput(color.replace("#", ""));

    if (!recentColors.includes(color)) {
      setRecentColors((prev) => [color, ...prev.slice(0, 9)]);
    }

    onColorChange?.(color);
    // 只有在不是高级拾色器选择颜色时才关闭下拉菜单
    if (!showAdvancedPicker) {
      setOpen(false);
    }
  };

  const handleHexInputChange = (value: string) => {
    setHexInput(value);
    if (value.length === 6 && /^[0-9A-Fa-f]{6}$/.test(value)) {
      const color = `#${value}`;
      setCurrentColor(color);
      onColorChange?.(color);
    }
  };

  const handleAdvancedColorChange = (color: any) => {
    const hexColor = color.hex;
    setCurrentColor(hexColor);
    setHexInput(hexColor.replace("#", ""));

    if (!recentColors.includes(hexColor)) {
      setRecentColors((prev) => [hexColor, ...prev.slice(0, 9)]);
    }

    onColorChange?.(hexColor);
    // 不要关闭下拉菜单，让用户可以继续调整颜色
  };

  useEffect(() => {
    if (open) {
      // 禁用快捷键和画布拖拽
      document.body.setAttribute("data-color-picker-open", "true");
      document.body.setAttribute("data-disable-shortcuts", "true");
    } else {
      // 恢复功能
      document.body.removeAttribute("data-color-picker-open");
      document.body.removeAttribute("data-disable-shortcuts");
    }

    return () => {
      document.body.removeAttribute("data-color-picker-open");
      document.body.removeAttribute("data-disable-shortcuts");
    };
  }, [open]);

  return (
    <DropdownMenu.Root
      open={disabled ? false : open}
      onOpenChange={disabled ? undefined : setOpen}
    >
      <DropdownMenu.Trigger asChild disabled={disabled}>
        <button
          className={`flex items-center gap-1.5 px-2 py-1 rounded ${
            disabled
              ? "text-gray-400 bg-gray-50 opacity-60"
              : "text-gray-700 hover:bg-gray-100 cursor-pointer"
          }`}
          title="字体颜色"
        >
          <svg
            viewBox="0 0 1024 1024"
            width="15"
            height="15"
            className="flex-shrink-0 pt-0.5"
          >
            <path
              d="M321.024 736l66.624-190.272h260.416l66.56 190.272H832L569.088 32H454.912L192 736h129.024z m298.24-278.08h-202.88L524.992 149.12h-14.08l108.416 308.8z"
              fill="currentColor"
            ></path>
            <path d="M128 832h768v128H128z" fill={currentColor}></path>
          </svg>
          <svg
            width="10"
            height="10"
            viewBox="0 0 15 15"
            fill="none"
            className="text-gray-500 flex-shrink-0"
          >
            <path
              d="M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z"
              fill="currentColor"
              fillRule="evenodd"
              clipRule="evenodd"
            ></path>
          </svg>
        </button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Content
        className="bg-white rounded-md shadow-lg border border-gray-200 w-80 z-[9999] flex flex-col"
        sideOffset={5}
        style={{ height: "320px" }}
      >
        {/* 内容区域 - 移除滚动 */}
        <div className="flex-1 p-4 pb-2">
          <div className="space-y-3">
            {/* System Colors Section */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm font-medium text-gray-700">
                  系统色
                </span>
                <svg
                  width="15"
                  height="15"
                  viewBox="0 0 15 15"
                  fill="none"
                  className="w-4 h-4 text-gray-400"
                >
                  <path
                    d="M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z"
                    fill="currentColor"
                    fillRule="evenodd"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </div>

              <div className="space-y-1.5">
                {systemColors.map((row, rowIndex) => (
                  <div key={rowIndex} className="flex gap-1.5">
                    {row.map((color, colIndex) => (
                      <button
                        key={`${rowIndex}-${colIndex}`}
                        className="w-5 h-5 rounded border border-gray-200 hover:scale-110 transition-transform cursor-pointer"
                        style={{ backgroundColor: color }}
                        onClick={() => handleColorSelect(color)}
                        onMouseDown={(e) => e.stopPropagation()}
                        title={color}
                      />
                    ))}
                  </div>
                ))}

                {/* Special bottom row */}
                <div className="flex gap-1.5 mt-2">
                  <div className="w-5 h-5 rounded border border-gray-200 bg-white" />
                  {specialColors.map((color, index) => (
                    <button
                      key={`special-${index}`}
                      className="w-5 h-5 rounded border border-gray-200 hover:scale-110 transition-transform cursor-pointer"
                      style={{ backgroundColor: color }}
                      onClick={() => handleColorSelect(color)}
                      title={color}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Recent Colors Section */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                最近使用
              </h3>
              <div className="flex gap-1.5">
                {recentColors.map((color, index) => (
                  <button
                    key={`recent-${index}`}
                    className="w-5 h-5 rounded border border-gray-200 hover:scale-110 transition-transform cursor-pointer"
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorSelect(color)}
                    title={color}
                  />
                ))}
              </div>
            </div>

            {/* Hex Input Section */}
            <div className="flex items-center gap-2 pt-1 border-t border-gray-100">
              <span className="text-sm text-gray-600">🖊️ Hex</span>
              <span className="text-gray-400">=</span>
              <input
                value={hexInput}
                onChange={(e) =>
                  handleHexInputChange(e.target.value.toUpperCase())
                }
                onMouseDown={(e) => {
                  e.stopPropagation();
                }}
                onContextMenu={(e) => {
                  e.stopPropagation();
                }}
                onKeyDown={(e) => {
                  e.stopPropagation();
                }}
                className="flex-1 h-7 text-sm font-mono border border-gray-200 rounded px-2"
                placeholder="000000"
                maxLength={6}
              />
              <div
                className="w-5 h-5 rounded border border-gray-200"
                style={{ backgroundColor: currentColor }}
              />
            </div>
          </div>
        </div>

        {/* 固定在底部的更多颜色 */}
        <div className="border-t border-gray-100 p-4 pt-2 bg-white relative z-50">
          <div className="relative z-10">
            <div
              className="flex items-center gap-2 w-full py-2 px-2 hover:bg-gray-50 rounded text-sm text-gray-700 relative z-10 bg-white"
              onMouseEnter={(e) => {
                e.stopPropagation();
                handleShowAdvancedPicker();
              }}
              onMouseLeave={(e) => {
                e.stopPropagation();
                handleHideAdvancedPicker();
              }}
              onPointerDown={(e) => {
                e.stopPropagation();
                e.preventDefault();
                setOpen(true);
                setShowAdvancedPicker(true);
              }}
            >
              <svg viewBox="0 0 1024 1024" className="w-4 h-4 text-blue-500">
                <path
                  d="M321.024 736l66.624-190.272h260.416l66.56 190.272H832L569.088 32H454.912L192 736h129.024z"
                  fill="currentColor"
                ></path>
                <path d="M128 832h768v128H128z" fill="#22B09F"></path>
              </svg>
              <span>更多颜色</span>
              <svg
                width="15"
                height="15"
                viewBox="0 0 15 15"
                fill="none"
                className="w-4 h-4 ml-auto text-gray-400 rotate-[-90deg]"
              >
                <path
                  d="M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z"
                  fill="currentColor"
                  fillRule="evenodd"
                  clipRule="evenodd"
                ></path>
              </svg>
            </div>

            {showAdvancedPicker && (
              <div
                className="absolute top-[-250px] left-full ml-2 z-[10000]"
                onMouseEnter={handleShowAdvancedPicker}
                onMouseLeave={handleHideAdvancedPicker}
                onMouseDown={(e) => {
                  e.stopPropagation();
                }}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                onContextMenu={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
                onMouseMove={(e) => {
                  e.stopPropagation();
                }}
              >
                <div className="relative bg-white rounded-md shadow-lg border border-gray-200 p-1">
                  <SketchPicker
                    color={currentColor}
                    onChange={handleAdvancedColorChange}
                    disableAlpha
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
}
