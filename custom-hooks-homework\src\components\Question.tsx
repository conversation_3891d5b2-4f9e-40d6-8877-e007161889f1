import type { ExamQuestion } from "../hooks/useExamStore";

interface QuestionProps {
  question: ExamQuestion;
  index: number;
  selectedOption: string | undefined;
  onSelectOption: (questionIndex: number, option: string) => void;
  submitted: boolean;
}

const Question = ({
  question,
  index,
  selectedOption,
  onSelectOption,
  submitted,
}: QuestionProps) => {
  // 提取选项字母（如A、B、C、D）
  const getOptionLetter = (option: string): string => {
    return option.split(".")[0].trim();
  };

  return (
    <div className="question-card">
      <div className="question-text">
        {index + 1}. {question.question}
      </div>
      <div className="options-container">
        {question.options.map((option, optionIndex) => {
          const optionLetter = getOptionLetter(option);
          const isSelected = selectedOption === optionLetter;
          const isCorrect = submitted && optionLetter === question.answer;

          return (
            <div
              key={optionIndex}
              className={`option-item ${isSelected ? "selected" : ""} ${
                submitted && isCorrect ? "correct" : ""
              }`}
              onClick={() => !submitted && onSelectOption(index, optionLetter)}
            >
              <input
                type="radio"
                id={`q${index}-option-${optionIndex}`}
                name={`question-${index}`}
                value={optionLetter}
                checked={isSelected}
                onChange={() => {}}
                disabled={submitted}
              />
              <label
                htmlFor={`q${index}-option-${optionIndex}`}
                className="option-text"
              >
                {option}
              </label>
            </div>
          );
        })}
      </div>
      {submitted && (
        <div className="answer-feedback">
          {selectedOption === question.answer
            ? "✓ 回答正确"
            : `✗ 正确答案是: ${question.answer}`}
        </div>
      )}
    </div>
  );
};

export default Question;
