import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import MobilePage from "../MobilePage";
import type { User } from "@/types/user";

// 模拟依赖组件
vi.mock("@/components/common/forms/LoginForm", () => ({
  default: ({ onLoginSuccess, isMobile, agreeTerms, setAgreeTerms }) => (
    <div data-testid="mock-login-form">
      <span>移动端: {isMobile ? "是" : "否"}</span>
      <span>已同意隐私协议: {agreeTerms ? "是" : "否"}</span>
      <button
        onClick={() => {
          const mockUsers: User[] = [
            {
              userid: 1,
              nickname: "测试用户1",
              company_id: 0,
              avatar_url: "avatar1.png",
              is_login: false,
              status: 1,
            },
            {
              userid: 2,
              nickname: "测试用户2",
              company_id: 123,
              company_name: "测试公司",
              company_logo: "company_logo.png",
              avatar_url: "avatar2.png",
              is_login: true,
              status: 1,
            },
          ];
          onLoginSuccess(mockUsers, [2]);
        }}
      >
        模拟登录成功
      </button>
      <button onClick={() => setAgreeTerms && setAgreeTerms(true)}>
        同意协议
      </button>
    </div>
  ),
}));

vi.mock("@/components/common/account/AccountSelect", () => ({
  default: ({ users, initialSelectedIds, onBack, onConfirm, isMobile }) => (
    <div data-testid="mock-account-select">
      <span>移动端: {isMobile ? "是" : "否"}</span>
      <span>账户数量: {users.length}</span>
      <span>已选账户: {initialSelectedIds.join(",")}</span>
      <button onClick={onBack}>返回登录页</button>
      <button onClick={() => onConfirm([...initialSelectedIds, 1])}>
        确认登录
      </button>
    </div>
  ),
}));

vi.mock("@/components/moreLogin/MoreLogin", () => ({
  default: ({ onBack }) => (
    <div data-testid="mock-more-login">
      <span>更多登录方式</span>
      <button onClick={onBack}>返回主页</button>
    </div>
  ),
}));

vi.mock("@/components/common/consent/ConsentModal", () => ({
  default: ({ isOpen, onClose, onAgree, onCancel }) =>
    isOpen ? (
      <div data-testid="mock-consent-modal">
        <span>隐私协议弹窗</span>
        <button onClick={onAgree}>同意</button>
        <button onClick={onCancel}>取消</button>
        <button onClick={onClose}>关闭</button>
      </div>
    ) : null,
}));

describe("移动端登录页面 (MobilePage)", () => {
  // 模拟 sessionStorage
  const sessionStorageMock = (() => {
    let store: Record<string, string> = {};
    return {
      getItem: (key: string) => store[key] || null,
      setItem: (key: string, value: string) => {
        store[key] = value.toString();
      },
      removeItem: (key: string) => {
        delete store[key];
      },
      clear: () => {
        store = {};
      },
    };
  })();

  // 替换原生的 sessionStorage
  Object.defineProperty(window, "sessionStorage", {
    value: sessionStorageMock,
  });

  beforeEach(() => {
    // 清除 sessionStorage
    sessionStorageMock.clear();
    // 替换原生的alert方法
    vi.spyOn(window, "alert").mockImplementation(() => {});
  });

  it("应该正确渲染移动端登录页面", () => {
    render(<MobilePage />);

    // 验证 LoginForm 组件被渲染
    expect(screen.getByTestId("mock-login-form")).toBeInTheDocument();

    // 验证移动端标志正确传递
    expect(screen.getByText("移动端: 是")).toBeInTheDocument();

    // 验证其他登录选项存在
    expect(screen.getByText("或")).toBeInTheDocument();
  });

  it("应该显示隐私协议勾选框", () => {
    render(<MobilePage />);

    const checkbox = screen.getByRole("checkbox");
    expect(checkbox).toBeInTheDocument();
    expect(checkbox).not.toBeChecked();

    // 验证隐私协议链接
    expect(screen.getByText("隐私政策")).toBeInTheDocument();
    expect(screen.getByText("在线服务协议")).toBeInTheDocument();
  });

  it("登录成功后应跳转到账户选择页面", async () => {
    render(<MobilePage />);

    // 点击模拟登录成功按钮
    fireEvent.click(screen.getByText("模拟登录成功"));

    // 验证是否切换到账户选择界面
    await waitFor(() => {
      expect(screen.getByTestId("mock-account-select")).toBeInTheDocument();
    });

    // 验证账户信息是否正确传递
    expect(screen.getByText("账户数量: 2")).toBeInTheDocument();
    expect(screen.getByText("已选账户: 2")).toBeInTheDocument();
    expect(screen.getByText("移动端: 是")).toBeInTheDocument();
  });

  it("在未同意隐私协议时点击其他登录方式应显示隐私协议弹窗", async () => {
    const { container } = render(<MobilePage />);

    // 查找第一个登录方法元素（QQ登录）
    const loginMethods = container.querySelectorAll(".login-method");
    expect(loginMethods.length).toBeGreaterThan(0);

    // 点击第一个登录方法（通常是QQ）
    fireEvent.click(loginMethods[0]);

    // 验证隐私协议弹窗显示
    await waitFor(() => {
      expect(screen.getByTestId("mock-consent-modal")).toBeInTheDocument();
    });
  });

  it("同意隐私协议后点击更多登录方式应显示更多登录页面", async () => {
    const { container } = render(<MobilePage />);

    // 先同意隐私协议
    fireEvent.click(screen.getByText("同意协议"));

    // 查找最后一个登录方法元素（更多）
    const loginMethods = container.querySelectorAll(".login-method");
    expect(loginMethods.length).toBeGreaterThan(0);
    const moreLoginButton = loginMethods[loginMethods.length - 1]; // 最后一个通常是"更多"

    // 点击"更多"登录方式
    fireEvent.click(moreLoginButton);

    // 验证是否切换到更多登录界面
    await waitFor(() => {
      expect(screen.getByTestId("mock-more-login")).toBeInTheDocument();
    });
  });

  it("在账户选择页面点击返回按钮应返回登录表单", async () => {
    render(<MobilePage />);

    // 先切换到账户选择界面
    fireEvent.click(screen.getByText("模拟登录成功"));

    await waitFor(() => {
      expect(screen.getByTestId("mock-account-select")).toBeInTheDocument();
    });

    // 点击返回按钮
    fireEvent.click(screen.getByText("返回登录页"));

    // 应该返回登录表单
    await waitFor(() => {
      expect(screen.getByTestId("mock-login-form")).toBeInTheDocument();
    });
  });

  it("在更多登录页面点击返回按钮应返回登录表单", async () => {
    const { container } = render(<MobilePage />);

    // 先同意隐私协议
    fireEvent.click(screen.getByText("同意协议"));

    // 查找最后一个登录方法元素（更多）
    const loginMethods = container.querySelectorAll(".login-method");
    const moreLoginButton = loginMethods[loginMethods.length - 1]; // 最后一个通常是"更多"

    // 点击"更多"登录方式
    fireEvent.click(moreLoginButton);

    // 验证切换到更多登录界面
    await waitFor(() => {
      expect(screen.getByTestId("mock-more-login")).toBeInTheDocument();
    });

    // 点击返回按钮
    fireEvent.click(screen.getByText("返回主页"));

    // 应该返回登录表单
    await waitFor(() => {
      expect(screen.getByTestId("mock-login-form")).toBeInTheDocument();
    });
  });

  it("在账户选择界面确认登录后应调用正确的处理函数", async () => {
    render(<MobilePage />);

    // 先切换到账户选择界面
    fireEvent.click(screen.getByText("模拟登录成功"));

    await waitFor(() => {
      expect(screen.getByTestId("mock-account-select")).toBeInTheDocument();
    });

    // 点击确认登录按钮
    fireEvent.click(screen.getByText("确认登录"));

    // 应该调用alert函数，显示登录成功信息
    expect(window.alert).toHaveBeenCalledWith("2,1登录成功");
  });

  it("未同意隐私协议点击更多，同意后应自动跳转到更多登录页", async () => {
    const { container } = render(<MobilePage />);

    // 查找最后一个登录方法元素（更多）
    const loginMethods = container.querySelectorAll(".login-method");
    const moreLoginButton = loginMethods[loginMethods.length - 1]; // 最后一个通常是"更多"

    // 点击"更多"按钮
    fireEvent.click(moreLoginButton);

    // 验证隐私协议弹窗显示
    await waitFor(() => {
      expect(screen.getByTestId("mock-consent-modal")).toBeInTheDocument();
    });

    // 检查sessionStorage中是否保存了lastClickedMethod
    expect(sessionStorageMock.getItem("lastClickedMethod")).toBe("more");

    // 点击同意按钮
    fireEvent.click(screen.getByText("同意"));

    // 验证是否切换到更多登录界面
    await waitFor(() => {
      expect(screen.getByTestId("mock-more-login")).toBeInTheDocument();
    });

    // 验证sessionStorage中的lastClickedMethod是否被清除
    expect(sessionStorageMock.getItem("lastClickedMethod")).toBeNull();
  });
});
