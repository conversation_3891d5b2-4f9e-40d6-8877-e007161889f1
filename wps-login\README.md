

# WPS 登录系统

基于 React + TypeScript 登录系统，包含 PC 端和移动端两种视图，响应式布局。

## 项目结构



```

wps-login/
├── public/                  # 静态资源目录
│   └── wps.png            # 图标
├── src/                     # 源代码目录
│   ├── assets/             # 静态资源
│   ├── components/         # 组件目录
│   │   ├── common/         # 公共组件
│   │   │   ├── consent/    # 隐私协议弹窗组件
│   │   │   │   ├── __tests__/
│   │   │   │   └── ConsentModal.tsx
│   │   │   ├── account/    # 账号选择组件
│   │   │   │   ├── __tests__/
│   │   │   │   └── AccountSelect.tsx
│   │   │   └── forms/      # 表单组件
│   │   │       ├── __tests__/
│   │   │       └── LoginForm.tsx
│   │   ├── moreLogin/      # 更多登录方式组件
│   │   │   ├── __tests__/
│   │   │   └── MoreLogin.tsx
│   │   └── phoneLogin/     # 手机号登录组件
│   │       ├── __tests__/
│   │       └── PhoneLogin.tsx
│   ├── pages/              # 页面组件
│   │   ├── pc/             # PC 端页面
│   │   │   ├── __tests__/
│   │   │   └── LoginPage.tsx
│   │   └── mobile/         # 移动端页面
│   │       ├── __tests__/
│   │       └── MobilePage.tsx
│   ├── types/              # TypeScript 类型定义
│   │   └── user.ts        # 用户相关类型定义
│   ├── utils/              # 工具函数
│   │   └── request.ts     # Axios 请求封装
│   ├── App.tsx            # 应用根组件
│   ├── App.css            # 应用样式
│   ├── main.tsx           # 应用入口
│   └── vite-env.d.ts      # Vite 环境类型声明
├── .whistle.js            # Whistle 代理配置
├── eslint.config.js        # ESLint 配置
├── index.html              # HTML 模板
├── package.json            # 项目依赖和脚本
├── tsconfig.json           # TypeScript 配置
├── tsconfig.app.json       # 应用 TypeScript 配置
├── tsconfig.node.json      # Node TypeScript 配置
└── vite.config.ts          # Vite 构建配置

```
## 技术栈

- **前端框架**: React 20
- **语言**: TypeScript
- **构建工具**: Vite
- **CSS 方案**: CSS Modules
- **HTTP 请求**: Axios
- **测试框架**: Vitest + React Testing Library
- **代理工具**: Whistle.js
- **代码规范**: ESLint

## 功能特点

1. **响应式布局**：
   - PC 端和移动端自适应切换（640px）小于640则为移动端
   - pc端 640px-850px 宽度区间，页面自适应缩小，避免左边logo和右边内容变成1列

2. **登录方式**：
   - 只实现手机验证码登录
   - 更多登录选项 点击进入更多登录选项页面

3. **隐私协议**：
   - 隐私协议弹窗

4. **账号选择功能**：
   - 企业和个人账号区分

5. **路由系统**：
   - 基于窗口宽度的自适应路由
   - 支持直接 URL 访问

## 接口

### 短信验证接口
```

POST https://account.wps.cn/api/v3/sms/verify
参数：

- phone: 手机号（如 +*************）
- smscode: 验证码（如 885688）
- keeponline: 是否保持登录状态（1 表示是）
  返回：ssid

```
### 获取用户列表接口
```

GET https://account.wps.cn/api/v3/login/users
参数：

- ssid: 会话 ID
- filter_rule: 过滤规则（normal）
- check_rule: 检查规则（second_phone）
- _: 手机号
  返回：用户列表

```
## 遇到的问题及解决方法

### 1. 响应式布局问题

**问题**：在 640px-850px 宽度区间，PC 端布局出现内容被裁剪、重叠等问题。

**解决方法**：
- 使用媒体查询针对特定宽度区间调整布局
- 实现了弹性布局，确保内容在不同宽度下正确显示
- 为 logo 和隐私政策文本添加特定的断点样式

### 2. 路由与直接 URL 访问问题

**问题**：直接通过 URL 访问特定页面时，响应式路由逻辑无法正确工作。

**解决方法**：  

- 在 App 组件中实现基于窗口宽度的路由逻辑
- 使用 `useEffect` 监听窗口大小变化，动态调整路由
- 实现路由守卫确保 URL 与当前视图保持一致

```tsx
useEffect(() => {
  const handleResize = () => {
    const isMobile = window.innerWidth < 640;
    if (isMobile && !location.pathname.includes('/mobile')) {
      navigate('/mobile');
    } else if (!isMobile && location.pathname.includes('/mobile')) {
      navigate('/');
    }
  };
  
  window.addEventListener('resize', handleResize);
  return () => window.removeEventListener('resize', handleResize);
}, [navigate]);
```

### 3. Node.js ESM 与 CommonJS 兼容性问题

**问题**：Windows 环境下 Whistle 代理配置文件使用 ESM 导致加载错误。

**解决方法**：

- 在 package.json 中删掉type:module
- `.js` 改为`.mjs`

### 4. 理解代理配置

**问题**：whistle代理解释、vite.config代理

**理解**：

- 设置主要是为了避免了跨域问题
- 当访问account.wps.cn时，请求被代理到localhost:8000（本地Vite开发服务器），即在浏览器访问account.wps.cn/zhuyuqian时，实际显示的是本地开发的前端页面。
- 域名account.wps.cn等被解析到**************（WPS测试服务器IP），所有account.wps.cn/api/***的请求被重定向到https://**************/api/$1，确保前端API接口调用能正确发送到后端服务  

### 5. 单元测试中的异步问题

**问题**：测试异步操作（如验证码验证、弹窗交互）时出现不稳定结果。

**解决方法**：

- 使用 `waitFor` 等待异步操作完成
- 为异步测试添加适当的超时设置

## 运行项目

### 安装依赖

```bash
pnpm install
```

### 配置代理

```bash
pnpm proxy
```

### 开发模式

```bash
pnpm dev  

访问 account.wps.cn/zhuyuqian
```

### 构建项目

```bash
pnpm build
```

### 运行测试

```bash
pnpm test
```

### 生成测试覆盖率报告

```bash
pnpm test:ui
```
