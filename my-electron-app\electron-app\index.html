<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Electron SDK</title>
    <style>
      body {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
      }
      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }
      .section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #fafafa;
      }
      .section h2 {
        color: #555;
        margin-top: 0;
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #666;
      }
      input,
      select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ccc;
        font-size: 14px;
      }
      button {
        padding: 10px 20px;
        border: none;
        cursor: pointer;
        font-size: 14px;
        margin-right: 10px;
      }
      button:hover {
        background-color: #a1a5a7;
      }
      button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
      .result {
        margin-top: 15px;
        padding: 10px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 200px;
        overflow-y: auto;
      }
      .success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
      }
      .error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
      }
      .info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
      }
      #webviewContainer {
        margin-top: 20px;
        border: 1px solid #ccc;
        height: 400px;
        display: none;
      }
      webview {
        width: 100%;
        height: 100%;
      }

      .file-select-btn {
        padding: 5px 10px;
        background-color: #f0f0f0;
        border: 1px solid #ccc;
        cursor: pointer;
        margin-bottom: 10px;
        margin-left: 5px;
      }
      .file-select-btn:hover {
        background-color: #e0e0e0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Electron SDK</h1>

      <!-- 文件读取 -->
      <div class="section">
        <h2>文件读取</h2>

        <div class="form-group">
          <label for="filePath"
            >文件路径:
            <button class="file-select-btn" onclick="selectFile()">
              选择文件
            </button></label
          >
          <input type="text" id="filePath" placeholder="输入要读取的文件路径" />
        </div>
        <div class="form-group">
          <label for="startByte">开始字节:</label>
          <input type="number" id="startByte" value="0" min="0" />
        </div>
        <div class="form-group">
          <label for="endByte">结束字节:</label>
          <input type="number" id="endByte" value="1024" min="1" />
        </div>
        <button onclick="testReadFile()">读取文件</button>
        <div id="readFileResult" class="result" style="display: none"></div>
      </div>

      <!-- 打开URL -->
      <div class="section">
        <h2>打开URL</h2>
        <div class="form-group">
          <label for="urlInput">URL地址:</label>
          <input
            type="url"
            id="urlInput"
            value="https://www.baidu.com"
            placeholder="输入要打开的URL"
          />
        </div>
        <div class="form-group">
          <label for="openType">打开方式:</label>
          <select id="openType">
            <option value="browserwindow">新窗口 (browserwindow)</option>
            <option value="webview">内嵌视图 (webview)</option>
          </select>
        </div>
        <button onclick="testOpenUrl()">打开URL</button>
        <div id="openUrlResult" class="result" style="display: none"></div>
        <div id="webviewContainer">
          <webview id="webview" src="about:blank" allowpopups="false"></webview>
        </div>
      </div>
    </div>

    <script>
      // 文件选择功能
      async function selectFile() {
        try {
          const result = await window.electronAPI.showFileDialog();
          if (!result.canceled && result.filePaths.length > 0) {
            document.getElementById("filePath").value = result.filePaths[0];
          }
        } catch (err) {
          console.error("文件选择错误:", err);
        }
      }

      // 文件读取
      async function testReadFile() {
        const resultDiv = document.getElementById("readFileResult");
        const filePath = document.getElementById("filePath").value;
        const startByte = parseInt(document.getElementById("startByte").value);
        const endByte = parseInt(document.getElementById("endByte").value);

        resultDiv.style.display = "block";
        resultDiv.className = "result info";
        resultDiv.textContent = "正在读取文件...";

        try {
          const result = await window.sdk.readFile({
            filePath: filePath,
            range: [startByte, endByte],
          });

          if (result.result.code === 0) {
            resultDiv.className = "result success";
            let displayText = `读取成功!\n`;
            displayText += `文件类型: ${result.data.mimeType || "未知"}\n`;
            displayText += `读取完成: ${result.data.finished ? "是" : "否"}\n`;
            displayText += `数据大小: ${
              result.data.arrayBuffer ? result.data.arrayBuffer.byteLength : 0
            } 字节\n`;
            resultDiv.textContent = displayText;
          } else {
            resultDiv.className = "result error";
            resultDiv.textContent = `读取失败: ${result.result.msg}`;
          }
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `错误: ${error.message}`;
        }
      }

      // 打开URL
      async function testOpenUrl() {
        const resultDiv = document.getElementById("openUrlResult");
        const url = document.getElementById("urlInput").value;
        const type = document.getElementById("openType").value;

        resultDiv.style.display = "block";
        resultDiv.className = "result info";
        resultDiv.textContent = "正在打开URL...";

        try {
          const result = await window.sdk.openUrl({
            url: url,
            options: { type: type },
          });

          if (result.code === 0) {
            resultDiv.className = "result success";
            resultDiv.textContent = `打开成功: ${result.msg}`;

            // 如果是webview方式，显示webview容器
            if (type === "webview") {
              showWebview(url);
            }
          } else {
            resultDiv.className = "result error";
            resultDiv.textContent = `打开失败: ${result.msg}`;
          }
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `错误: ${error.message}`;
        }
      }

      // 显示webview
      function showWebview(url) {
        const container = document.getElementById("webviewContainer");
        const webview = document.getElementById("webview");
        container.style.display = "block";
        webview.src = url;
      }

      // 监听来自主进程的webview消息
      if (window.electronAPI && window.electronAPI.onOpenInWebview) {
        window.electronAPI.onOpenInWebview((url) => {
          showWebview(url);
        });
      }
    </script>
  </body>
</html>
