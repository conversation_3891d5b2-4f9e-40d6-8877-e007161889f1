// 文件夹类（组合节点）
import { Component } from './types'

export class Folder extends Component {
  private _children: Component[] = []

  constructor(id: string, name: string, parentId: string | null = null) {
    super(id, name, 'folder', parentId)
  }

  get size(): number {
    return this._children.reduce((sum, child) => sum + child.size, 0)
  }

  get children(): Component[] {
    return [...this._children]
  }

  add(component: Component): void {
    component.parentId = this.id
    this._children.push(component)
  }

  remove(componentId: string): boolean {
    const index = this._children.findIndex((c) => c.id === componentId)
    if (index !== -1) {
      this._children.splice(index, 1)
      return true
    }
    return false
  }

  display(indent: string = ''): string {
    const lines = [`${indent}📁 ${this.name} (${this.size}KB)`]
    this._children.forEach((child) => {
      lines.push(child.display(indent + '  '))
    })
    return lines.join('\n')
  }
}
