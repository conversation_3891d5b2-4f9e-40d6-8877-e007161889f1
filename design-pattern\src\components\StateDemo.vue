<!-- src/components/StateDemo.vue -->
<template>
  <div class="state-demo">
    <h2>状态模式演示 - 订单流程系统</h2>

    <div class="order-status">
      <h3>当前订单状态: {{ currentState }}</h3>
    </div>

    <div class="order-actions">
      <button @click="cancelOrder">取消订单</button>
      <button @click="verifyPayment">验证支付</button>
      <button @click="shipOrder">发货</button>
    </div>

    <div class="action-history">
      <h3>操作历史</h3>
      <ul>
        <li v-for="(action, index) in history" :key="index">
          {{ action }}
        </li>
      </ul>
    </div>

    <div class="result" v-if="lastActionResult">
      <p>{{ lastActionResult }}</p>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { OrderContext } from '@/utils/state/OrderContext'

export default defineComponent({
  name: 'StateDemo',
  setup() {
    const order = new OrderContext()
    const currentState = ref(order.getCurrentState())
    const lastActionResult = ref('')
    const history = ref<string[]>([])

    const updateState = () => {
      currentState.value = order.getCurrentState()
    }

    const logAction = (action: string, result: string) => {
      history.value.push(`${action} → ${result}`)
      lastActionResult.value = result
    }

    const cancelOrder = () => {
      const result = order.cancelOrder()
      logAction('取消订单', result)
      updateState()
    }

    const verifyPayment = () => {
      const result = order.verifyPayment()
      logAction('验证支付', result)
      updateState()
    }

    const shipOrder = () => {
      const result = order.shipOrder()
      logAction('发货', result)
      updateState()
    }

    return {
      currentState,
      lastActionResult,
      history,
      cancelOrder,
      verifyPayment,
      shipOrder,
    }
  },
})
</script>

<style scoped>
.state-demo {
  border: 1px solid #eee;
  padding: 20px;
  margin: 20px 0;
  border-radius: 8px;
  font-family: Arial, sans-serif;
  max-width: 500px;
}

.order-status {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f8f8;
  border-radius: 4px;
}

.order-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

button {
  padding: 10px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.action-history {
  margin-top: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 4px;
}

.action-history ul {
  list-style-type: none;
  padding: 0;
}

.action-history li {
  padding: 5px 0;
  border-bottom: 1px solid #ddd;
}

.result {
  margin-top: 15px;
  padding: 10px;
  background: #e8f4fd;
  border-radius: 4px;
  color: #2c3e50;
}
</style>
