import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import TodoStats from "../TodoStats";

describe("TodoStats组件", () => {
  const mockStats = {
    total: 5,
    active: 3,
    completed: 2,
    percentage: 40,
  };

  const mockClearCompleted = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
  });

  test("正确渲染统计数据", () => {
    render(
      <TodoStats stats={mockStats} onClearCompleted={mockClearCompleted} />
    );

    expect(screen.getByText("总计: 5")).toBeInTheDocument();
    expect(screen.getByText("进行中: 3")).toBeInTheDocument();
    expect(screen.getByText("已完成: 2")).toBeInTheDocument();
    expect(screen.getByText("完成率: 40%")).toBeInTheDocument();
  });

  test("没有数据时显示默认值", () => {
    render(<TodoStats onClearCompleted={mockClearCompleted} />);

    expect(screen.getByText("总计: 0")).toBeInTheDocument();
    expect(screen.getByText("进行中: 0")).toBeInTheDocument();
    expect(screen.getByText("已完成: 0")).toBeInTheDocument();
    expect(screen.queryByText(/完成率:/)).not.toBeInTheDocument(); // 没有待办项时不显示完成率
  });

  test('没有已完成项时"清除已完成"按钮应该禁用', () => {
    const noCompletedStats = {
      total: 3,
      active: 3,
      completed: 0,
      percentage: 0,
    };

    render(
      <TodoStats
        stats={noCompletedStats}
        onClearCompleted={mockClearCompleted}
      />
    );

    const clearButton = screen.getByText("清除已完成");
    expect(clearButton).toBeDisabled();
  });

  test('有已完成项时"清除已完成"按钮应该可用', () => {
    render(
      <TodoStats stats={mockStats} onClearCompleted={mockClearCompleted} />
    );

    const clearButton = screen.getByText("清除已完成");
    expect(clearButton).not.toBeDisabled();
  });

  test('点击"清除已完成"按钮应该调用onClearCompleted', () => {
    render(
      <TodoStats stats={mockStats} onClearCompleted={mockClearCompleted} />
    );

    const clearButton = screen.getByText("清除已完成");
    fireEvent.click(clearButton);

    expect(mockClearCompleted).toHaveBeenCalledTimes(1);
  });
});
