{"name": "code_tools", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "ts-node src/index.ts", "lint": "eslint . --ext .ts", "test": "echo \"Error: no test specified\" && exit 1", "lint:fix": "eslint --fix"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "http": "^0.0.1-security"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/node": "^20.11.19", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0"}}