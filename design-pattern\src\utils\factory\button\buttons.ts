import type { ButtonComponent, ButtonVariant, ButtonSize } from './type'

// 具体产品类
export class PrimaryButton implements ButtonComponent {
  constructor(
    public text: string,
    public size: ButtonSize = 'md',
    public disabled: boolean = false,
    public variant: ButtonVariant = 'primary',
  ) {}

  render(): string {
    return `
      <button class="btn btn-${this.variant} btn-${this.size}" 
              ${this.disabled ? 'disabled' : ''}>
        ${this.text}
      </button>
    `
  }
}

export class IconButton implements ButtonComponent {
  constructor(
    public text: string,
    public icon: string,
    public size: ButtonSize = 'md',
    public disabled: boolean = false,
    public variant: ButtonVariant = 'secondary',
  ) {}

  render(): string {
    return `
      <button class="btn btn-${this.variant} btn-${this.size}" 
              ${this.disabled ? 'disabled' : ''}>
        <i class="icon-${this.icon}"></i>
        ${this.text}
      </button>
    `
  }
}
