import React, { useCallback } from "react";
import { useMindMapStore } from "../store";

export function useCanvasInteractions(
  canvasRef: React.RefObject<HTMLDivElement | null>,
  svgRef: React.RefObject<SVGSVGElement | null>
) {
  const clearSelection = useMindMapStore((s) => s.clearSelection);
  const setEditingNode = useMindMapStore((s) => s.setEditingNode);

  // 点击画布时清除选中，使用事件委托优化点击处理
  const handleCanvasClick = useCallback(
    (e: React.MouseEvent) => {
      const target = e.target as HTMLElement;
      const isNodeClick = target.closest("[data-node-id]");
      const isEdgeClick = target.closest("[data-edge-id]");

      // 如果点击了节点，清除编辑状态
      if (isNodeClick) {
        const nodeId = isNodeClick.getAttribute("data-node-id");
        const { editingNodeId } = useMindMapStore.getState();

        // 如果点击的不是当前编辑的节点，清除编辑状态
        if (editingNodeId && editingNodeId !== nodeId) {
          setEditingNode(null);
        }
      }

      const canvas = canvasRef.current;
      const svg = svgRef.current;

      if (
        !isNodeClick &&
        !isEdgeClick &&
        (canvas?.isSameNode(target) || svg?.isSameNode(target))
      ) {
        // 点击空白区域，清除选择和编辑状态
        requestAnimationFrame(() => {
          clearSelection();
          setEditingNode(null);
        });
      }
    },
    [clearSelection, setEditingNode, canvasRef, svgRef]
  );

  return { handleCanvasClick };
}
