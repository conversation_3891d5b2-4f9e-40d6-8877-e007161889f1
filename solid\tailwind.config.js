module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      // 扩展默认颜色，添加一个自定义的品牌色
      colors: {
        primary: "#1DA1F2",
        secondary: "#657786",
      },
      // 扩展默认字体大小
      fontSize: {
        xxl: "2.5rem", // 40px
      },
      // 扩展默认间距
      spacing: {
        128: "32rem", // 512px
      },
      // 扩展默认动画
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
      },
      animation: {
        fadeIn: "fadeIn 1s ease-in-out",
      },
    },
  },
  plugins: [],
};
