import React from "react";
import { render, screen, fireEvent, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import TodoApp from "./TodoApp";

// 模拟依赖项
jest.mock("./hooks/useTodoStore", () => ({
  useTodoStore: jest.fn(),
}));

jest.mock("./utils/todoUtils", () => ({
  filterTodos: jest.fn((todos, filter) => {
    if (filter === "active") return todos.filter((todo) => !todo.completed);
    if (filter === "completed") return todos.filter((todo) => todo.completed);
    return todos;
  }),
  sortTodos: jest.fn((todos) => todos),
  getTodoStats: jest.fn((todos) => ({
    total: todos.length,
    active: todos.filter((todo) => !todo.completed).length,
    completed: todos.filter((todo) => todo.completed).length,
    percentage:
      todos.length > 0
        ? Math.round(
            (todos.filter((todo) => todo.completed).length / todos.length) * 100
          )
        : 0,
  })),
  validateTodoText: jest.fn((text) => text && text.trim().length > 0),
}));

describe("TodoApp组件", () => {
  // 模拟数据和函数
  const mockTodos = [
    {
      id: "1",
      text: "学习React",
      completed: false,
      createdAt: new Date("2023-01-01"),
    },
    {
      id: "2",
      text: "学习Jest",
      completed: true,
      createdAt: new Date("2023-01-02"),
    },
    {
      id: "3",
      text: "写测试用例",
      completed: false,
      createdAt: new Date("2023-01-03"),
    },
  ];

  const mockAddTodo = jest.fn().mockReturnValue(true);
  const mockToggleTodo = jest.fn();
  const mockDeleteTodo = jest.fn().mockReturnValue(true);
  const mockClearCompleted = jest.fn().mockReturnValue(1);
  const mockUpdateTodo = jest.fn();
  const mockPrint = jest.fn();

  beforeEach(() => {
    // 重置所有模拟
    jest.clearAllMocks();

    // 设置useTodoStore的返回值
    const { useTodoStore } = require("./hooks/useTodoStore");
    useTodoStore.mockReturnValue({
      todos: mockTodos,
      addTodo: mockAddTodo,
      toggleTodo: mockToggleTodo,
      deleteTodo: mockDeleteTodo,
      clearCompleted: mockClearCompleted,
      updateTodo: mockUpdateTodo,
      uncompletedCount: 2,
      completedCount: 1,
      totalCount: 3,
    });
  });

  test("渲染TodoApp组件", () => {
    render(<TodoApp />);

    // 检查标题
    expect(screen.getByText("TODO List")).toBeInTheDocument();

    // 检查过滤器按钮
    expect(screen.getByText("全部")).toBeInTheDocument();
    expect(screen.getByText("进行中")).toBeInTheDocument();
    expect(screen.getByText("已完成")).toBeInTheDocument();

    // 检查TodoInput是否存在
    expect(
      screen.getByPlaceholderText("Add a new todo...")
    ).toBeInTheDocument();

    // 检查是否显示所有待办项
    expect(screen.getByText("学习React")).toBeInTheDocument();
    expect(screen.getByText("学习Jest")).toBeInTheDocument();
    expect(screen.getByText("写测试用例")).toBeInTheDocument();
  });

  test("添加新待办项", () => {
    render(<TodoApp />);

    // 输入新待办项
    const input = screen.getByPlaceholderText("Add a new todo...");
    const addButton = screen.getByText("Add");

    fireEvent.change(input, { target: { value: "测试新待办项" } });
    fireEvent.click(addButton);

    // 验证调用了addTodo
    expect(mockAddTodo).toHaveBeenCalledWith("测试新待办项");
  });

  test("切换待办项状态", () => {
    render(<TodoApp />);

    // 找到第一个待办项并点击切换状态
    const todoItem = screen.getByText("学习React").closest(".todo-item");
    fireEvent.click(todoItem);

    // 验证调用了toggleTodo
    expect(mockToggleTodo).toHaveBeenCalledWith("1");
  });

  test("删除待办项", () => {
    render(<TodoApp />);

    // 找到第一个待办项的删除按钮并点击
    const todoItem = screen.getByText("学习React").closest(".todo-item");
    const deleteButton = within(todoItem).getByTitle("删除");
    fireEvent.click(deleteButton);

    // 验证调用了deleteTodo
    expect(mockDeleteTodo).toHaveBeenCalledWith("1");
  });

  test("清除已完成的待办项", () => {
    render(<TodoApp />);

    // 点击"清除已完成"按钮
    const clearButton = screen.getByText("清除已完成");
    fireEvent.click(clearButton);

    // 验证调用了clearCompleted
    expect(mockClearCompleted).toHaveBeenCalled();
  });

  test("过滤待办项 - 全部", () => {
    const { filterTodos } = require("./utils/todoUtils");
    render(<TodoApp />);

    // 点击"全部"过滤器
    const allFilter = screen.getByText("全部");
    fireEvent.click(allFilter);

    // 验证调用了filterTodos，并且显示所有待办项
    expect(filterTodos).toHaveBeenCalledWith(mockTodos, "all");

    // 检查是否显示所有待办项
    expect(screen.getByText("学习React")).toBeInTheDocument();
    expect(screen.getByText("学习Jest")).toBeInTheDocument();
    expect(screen.getByText("写测试用例")).toBeInTheDocument();
  });

  test("过滤待办项 - 进行中", () => {
    const { filterTodos } = require("./utils/todoUtils");

    render(<TodoApp />);

    // 点击"进行中"过滤器
    const activeFilter = screen.getByText("进行中");
    fireEvent.click(activeFilter);

    // 验证调用了filterTodos
    expect(filterTodos).toHaveBeenCalledWith(mockTodos, "active");
  });

  test("过滤待办项 - 已完成", () => {
    const { filterTodos } = require("./utils/todoUtils");

    render(<TodoApp />);

    // 点击"已完成"过滤器
    const completedFilter = screen.getByText("已完成");
    fireEvent.click(completedFilter);

    // 验证调用了filterTodos
    expect(filterTodos).toHaveBeenCalledWith(mockTodos, "completed");
  });

  test("显示空状态 - 没有待办项", () => {
    // 设置useTodoStore返回空数组
    const { useTodoStore } = require("./hooks/useTodoStore");
    useTodoStore.mockReturnValue({
      todos: [],
      addTodo: mockAddTodo,
      toggleTodo: mockToggleTodo,
      deleteTodo: mockDeleteTodo,
      clearCompleted: mockClearCompleted,
      updateTodo: mockUpdateTodo,
      uncompletedCount: 0,
      completedCount: 0,
      totalCount: 0,
    });

    render(<TodoApp />);

    // 验证显示空状态消息
    expect(screen.getByText("还没有待办项，开始添加吧！")).toBeInTheDocument();
  });

  test("显示空状态 - 没有进行中的待办项", () => {
    // 设置useTodoStore返回只有已完成的待办项
    const { useTodoStore } = require("./hooks/useTodoStore");
    const completedTodos = [
      { id: "1", text: "已完成任务1", completed: true, createdAt: new Date() },
      { id: "2", text: "已完成任务2", completed: true, createdAt: new Date() },
    ];

    useTodoStore.mockReturnValue({
      todos: completedTodos,
      addTodo: mockAddTodo,
      toggleTodo: mockToggleTodo,
      deleteTodo: mockDeleteTodo,
      clearCompleted: mockClearCompleted,
      updateTodo: mockUpdateTodo,
      uncompletedCount: 0,
      completedCount: 2,
      totalCount: 2,
    });

    render(<TodoApp />);

    // 点击"进行中"过滤器
    const activeFilter = screen.getByText("进行中");
    fireEvent.click(activeFilter);

    // 验证显示空状态消息
    expect(screen.getByText("没有进行中的待办项")).toBeInTheDocument();
  });

  test("显示空状态 - 没有已完成的待办项", () => {
    // 设置useTodoStore返回只有未完成的待办项
    const { useTodoStore } = require("./hooks/useTodoStore");
    const activeTodos = [
      { id: "1", text: "未完成任务1", completed: false, createdAt: new Date() },
      { id: "2", text: "未完成任务2", completed: false, createdAt: new Date() },
    ];

    useTodoStore.mockReturnValue({
      todos: activeTodos,
      addTodo: mockAddTodo,
      toggleTodo: mockToggleTodo,
      deleteTodo: mockDeleteTodo,
      clearCompleted: mockClearCompleted,
      updateTodo: mockUpdateTodo,
      uncompletedCount: 2,
      completedCount: 0,
      totalCount: 2,
    });

    render(<TodoApp />);

    // 点击"已完成"过滤器
    const completedFilter = screen.getByText("已完成");
    fireEvent.click(completedFilter);

    // 验证显示空状态消息
    expect(screen.getByText("没有已完成的待办项")).toBeInTheDocument();
  });

  test("编辑待办项", () => {
    // 模拟console.log
    const originalConsoleLog = console.log;
    console.log = jest.fn();

    render(<TodoApp />);

    // 找到第一个待办项的编辑按钮并点击
    const todoItem = screen.getByText("学习React").closest(".todo-item");
    const editButton = within(todoItem).getByTitle("编辑");
    fireEvent.click(editButton);

    // 验证console.log被调用，且参数正确
    expect(console.log).toHaveBeenCalledWith(
      "编辑待办项:",
      expect.objectContaining({
        id: "1",
        text: "学习React",
      })
    );

    // 恢复console.log
    console.log = originalConsoleLog;
  });
});
