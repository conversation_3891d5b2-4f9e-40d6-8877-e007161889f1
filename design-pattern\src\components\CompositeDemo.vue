<template>
  <div class="file-explorer">
    <div class="toolbar">
      <button @click="createFolder">新建文件夹</button>
      <button @click="createFile">新建文件</button>
      <button @click="deleteItem" :disabled="!selectedId">删除</button>
    </div>

    <div class="breadcrumb">
      <span v-for="(item, index) in pathComponents" :key="item.id" @click="navigateTo(index)">
        {{ item.name }}
        <span v-if="index < pathComponents.length - 1">/</span>
      </span>
    </div>

    <div class="file-list">
      <div
        v-for="item in currentChildren"
        :key="item.id"
        class="file-item"
        :class="{
          'is-folder': item.type === 'folder',
          selected: selectedId === item.id,
        }"
        @click="selectItem(item)"
        @dblclick="navigateInto(item)"
      >
        <span class="icon">{{ item.type === 'folder' ? '📁' : '📄' }}</span>
        <span class="name">{{ item.name }}</span>
        <span class="size">{{ item.size }}KB</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { Folder } from '../utils/composite/Folder'
import { File } from '../utils/composite/File'
import { Component } from '../utils/composite/types'

export default defineComponent({
  name: 'FileExplorer',
  setup() {
    // 初始化文件系统
    const root = new Folder('root', '我的文件')
    const currentFolder = ref<Folder>(root)
    const selectedId = ref<string | null>(null)

    // 示例数据
    const docs = new Folder('docs', '文档')
    docs.add(new File('report.doc', '报告.doc', 250))
    docs.add(new File('presentation.ppt', '演示文稿.ppt', 180))

    const images = new Folder('images', '图片')
    images.add(new File('photo1.jpg', '照片1.jpg', 120))
    images.add(new File('photo2.jpg', '照片2.jpg', 150))

    root.add(docs)
    root.add(images)

    // 计算属性
    const currentChildren = computed(() => currentFolder.value.children)

    const pathComponents = computed(() => {
      const path: Folder[] = []
      let folder: Folder | null = currentFolder.value

      while (folder) {
        path.unshift(folder)
        // 实际项目中需要维护parent引用
        folder = folder === root ? null : root
      }

      return path
    })

    // 操作方法
    const selectItem = (item: Component) => {
      selectedId.value = item.id
    }

    const navigateInto = (item: Component) => {
      if (item.type === 'folder') {
        currentFolder.value = item as Folder
        selectedId.value = null
      }
    }

    const navigateTo = (index: number) => {
      currentFolder.value = pathComponents.value[index]
      selectedId.value = null
    }

    const createFolder = () => {
      const name = prompt('输入文件夹名称')
      if (name) {
        const folder = new Folder(`folder-${Date.now()}`, name)
        currentFolder.value.add(folder)
      }
    }

    const createFile = () => {
      const name = prompt('输入文件名称')
      const size = Number(prompt('输入文件大小(KB)'))
      if (name && !isNaN(size)) {
        const file = new File(`file-${Date.now()}`, name, size)
        currentFolder.value.add(file)
      }
    }

    const deleteItem = () => {
      if (selectedId.value) {
        currentFolder.value.remove(selectedId.value)
        selectedId.value = null
      }
    }

    return {
      currentChildren,
      pathComponents,
      selectedId,
      selectItem,
      navigateInto,
      navigateTo,
      createFolder,
      createFile,
      deleteItem,
    }
  },
})
</script>

<style scoped>
.file-explorer {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

button {
  padding: 6px 12px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.breadcrumb {
  margin-bottom: 12px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
}

.breadcrumb span {
  cursor: pointer;
  color: #42b983;
}

.file-list {
  border: 1px solid #eee;
  border-radius: 4px;
  min-height: 300px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
}

.file-item:hover {
  background: #f0f7ff;
}

.file-item.selected {
  background: #e3f2fd;
}

.icon {
  margin-right: 8px;
  font-size: 1.2em;
}

.name {
  flex: 1;
}

.size {
  color: #666;
  font-size: 0.9em;
}
</style>
