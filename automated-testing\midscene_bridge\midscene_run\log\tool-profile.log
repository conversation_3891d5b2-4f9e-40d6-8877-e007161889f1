[2025-08-13T16:47:25.743+08:00] Getting page URL
[2025-08-13T16:47:25.769+08:00] URL end
[2025-08-13T16:47:25.769+08:00] Uploading test info to server
[2025-08-13T16:47:28.194+08:00] UploadTestInfoToServer end
[2025-08-13T16:47:28.194+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:47:28.311+08:00] GetElementsNodeTree end
[2025-08-13T16:47:28.391+08:00] ScreenshotBase64 end
[2025-08-13T16:47:28.391+08:00] ParseContextFromWebPage end
[2025-08-13T16:47:28.391+08:00] Traversing element tree
[2025-08-13T16:47:28.392+08:00] TraverseTree end
[2025-08-13T16:47:28.398+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:47:28.398+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:47:28.489+08:00] ResizeImgBase64 end
[2025-08-13T16:47:33.275+08:00] Getting page URL
[2025-08-13T16:47:33.275+08:00] URL end
[2025-08-13T16:47:33.275+08:00] Uploading test info to server
[2025-08-13T16:47:35.393+08:00] UploadTestInfoToServer end
[2025-08-13T16:47:35.393+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:47:35.427+08:00] GetElementsNodeTree end
[2025-08-13T16:47:35.485+08:00] ScreenshotBase64 end
[2025-08-13T16:47:35.486+08:00] ParseContextFromWebPage end
[2025-08-13T16:47:35.486+08:00] Traversing element tree
[2025-08-13T16:47:35.486+08:00] TraverseTree end
[2025-08-13T16:47:35.486+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:47:35.486+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:47:35.487+08:00] ResizeImgBase64 end
[2025-08-13T16:47:35.823+08:00] Getting page URL
[2025-08-13T16:47:35.823+08:00] URL end
[2025-08-13T16:47:35.823+08:00] Uploading test info to server
[2025-08-13T16:47:38.080+08:00] UploadTestInfoToServer end
[2025-08-13T16:47:38.080+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:47:38.110+08:00] GetElementsNodeTree end
[2025-08-13T16:47:38.167+08:00] ScreenshotBase64 end
[2025-08-13T16:47:38.167+08:00] ParseContextFromWebPage end
[2025-08-13T16:47:38.167+08:00] Traversing element tree
[2025-08-13T16:47:38.167+08:00] TraverseTree end
[2025-08-13T16:47:38.167+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:47:38.167+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:47:38.168+08:00] ResizeImgBase64 end
[2025-08-13T16:47:43.840+08:00] Getting page URL
[2025-08-13T16:47:43.840+08:00] URL end
[2025-08-13T16:47:43.840+08:00] Uploading test info to server
[2025-08-13T16:47:45.790+08:00] UploadTestInfoToServer end
[2025-08-13T16:47:45.790+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:47:45.816+08:00] GetElementsNodeTree end
[2025-08-13T16:47:45.863+08:00] ScreenshotBase64 end
[2025-08-13T16:47:45.863+08:00] ParseContextFromWebPage end
[2025-08-13T16:47:45.863+08:00] Traversing element tree
[2025-08-13T16:47:45.863+08:00] TraverseTree end
[2025-08-13T16:47:45.863+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:47:45.863+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:47:45.864+08:00] ResizeImgBase64 end
[2025-08-13T16:47:49.556+08:00] Getting page URL
[2025-08-13T16:47:49.556+08:00] URL end
[2025-08-13T16:47:49.556+08:00] Uploading test info to server
[2025-08-13T16:47:52.035+08:00] UploadTestInfoToServer end
[2025-08-13T16:47:52.035+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:47:52.063+08:00] GetElementsNodeTree end
[2025-08-13T16:47:52.112+08:00] ScreenshotBase64 end
[2025-08-13T16:47:52.112+08:00] ParseContextFromWebPage end
[2025-08-13T16:47:52.112+08:00] Traversing element tree
[2025-08-13T16:47:52.112+08:00] TraverseTree end
[2025-08-13T16:47:52.112+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:47:52.112+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:47:52.114+08:00] ResizeImgBase64 end
[2025-08-13T16:47:52.464+08:00] Getting page URL
[2025-08-13T16:47:52.464+08:00] URL end
[2025-08-13T16:47:52.464+08:00] Uploading test info to server
[2025-08-13T16:47:54.382+08:00] UploadTestInfoToServer end
[2025-08-13T16:47:54.382+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:47:54.410+08:00] GetElementsNodeTree end
[2025-08-13T16:47:54.454+08:00] ScreenshotBase64 end
[2025-08-13T16:47:54.454+08:00] ParseContextFromWebPage end
[2025-08-13T16:47:54.454+08:00] Traversing element tree
[2025-08-13T16:47:54.454+08:00] TraverseTree end
[2025-08-13T16:47:54.454+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:47:54.454+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:47:54.455+08:00] ResizeImgBase64 end
[2025-08-13T16:48:00.114+08:00] Getting page URL
[2025-08-13T16:48:00.114+08:00] URL end
[2025-08-13T16:48:00.114+08:00] Uploading test info to server
[2025-08-13T16:48:02.133+08:00] UploadTestInfoToServer end
[2025-08-13T16:48:02.133+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:48:02.162+08:00] GetElementsNodeTree end
[2025-08-13T16:48:02.214+08:00] ScreenshotBase64 end
[2025-08-13T16:48:02.214+08:00] ParseContextFromWebPage end
[2025-08-13T16:48:02.214+08:00] Traversing element tree
[2025-08-13T16:48:02.214+08:00] TraverseTree end
[2025-08-13T16:48:02.214+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:48:02.214+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:48:02.215+08:00] ResizeImgBase64 end
[2025-08-13T16:48:06.406+08:00] Getting page URL
[2025-08-13T16:48:06.406+08:00] URL end
[2025-08-13T16:48:06.406+08:00] Uploading test info to server
[2025-08-13T16:48:08.167+08:00] UploadTestInfoToServer end
[2025-08-13T16:48:08.167+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:48:08.191+08:00] GetElementsNodeTree end
[2025-08-13T16:48:08.236+08:00] ScreenshotBase64 end
[2025-08-13T16:48:08.236+08:00] ParseContextFromWebPage end
[2025-08-13T16:48:08.236+08:00] Traversing element tree
[2025-08-13T16:48:08.236+08:00] TraverseTree end
[2025-08-13T16:48:08.236+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:48:08.236+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:48:08.237+08:00] ResizeImgBase64 end
[2025-08-13T16:48:08.620+08:00] Getting page URL
[2025-08-13T16:48:08.620+08:00] URL end
[2025-08-13T16:48:08.620+08:00] Uploading test info to server
[2025-08-13T16:48:10.498+08:00] UploadTestInfoToServer end
[2025-08-13T16:48:10.498+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:48:10.542+08:00] GetElementsNodeTree end
[2025-08-13T16:48:10.618+08:00] ScreenshotBase64 end
[2025-08-13T16:48:10.618+08:00] ParseContextFromWebPage end
[2025-08-13T16:48:10.618+08:00] Traversing element tree
[2025-08-13T16:48:10.618+08:00] TraverseTree end
[2025-08-13T16:48:10.618+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:48:10.618+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:48:10.619+08:00] ResizeImgBase64 end
[2025-08-13T16:48:15.624+08:00] Getting page URL
[2025-08-13T16:48:15.624+08:00] URL end
[2025-08-13T16:48:15.624+08:00] Uploading test info to server
[2025-08-13T16:48:17.520+08:00] UploadTestInfoToServer end
[2025-08-13T16:48:17.520+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:48:17.561+08:00] GetElementsNodeTree end
[2025-08-13T16:48:17.620+08:00] ScreenshotBase64 end
[2025-08-13T16:48:17.620+08:00] ParseContextFromWebPage end
[2025-08-13T16:48:17.620+08:00] Traversing element tree
[2025-08-13T16:48:17.620+08:00] TraverseTree end
[2025-08-13T16:48:17.620+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:48:17.620+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:48:17.622+08:00] ResizeImgBase64 end
[2025-08-13T16:48:17.623+08:00] Getting page URL
[2025-08-13T16:48:17.623+08:00] URL end
[2025-08-13T16:48:17.623+08:00] Uploading test info to server
[2025-08-13T16:48:19.779+08:00] UploadTestInfoToServer end
[2025-08-13T16:48:19.779+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:48:19.828+08:00] GetElementsNodeTree end
[2025-08-13T16:48:19.881+08:00] ScreenshotBase64 end
[2025-08-13T16:48:19.881+08:00] ParseContextFromWebPage end
[2025-08-13T16:48:19.881+08:00] Traversing element tree
[2025-08-13T16:48:19.881+08:00] TraverseTree end
[2025-08-13T16:48:19.881+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:48:19.881+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:48:19.882+08:00] ResizeImgBase64 end
[2025-08-13T16:48:35.399+08:00] Getting page URL
[2025-08-13T16:48:35.399+08:00] URL end
[2025-08-13T16:48:35.403+08:00] Uploading test info to server
[2025-08-13T16:48:37.463+08:00] UploadTestInfoToServer end
[2025-08-13T16:48:37.463+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:48:37.505+08:00] GetElementsNodeTree end
[2025-08-13T16:48:37.566+08:00] ScreenshotBase64 end
[2025-08-13T16:48:37.566+08:00] ParseContextFromWebPage end
[2025-08-13T16:48:37.566+08:00] Traversing element tree
[2025-08-13T16:48:37.566+08:00] TraverseTree end
[2025-08-13T16:48:37.566+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:48:37.566+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:48:37.567+08:00] ResizeImgBase64 end
[2025-08-13T16:48:42.249+08:00] Getting page URL
[2025-08-13T16:48:42.249+08:00] URL end
[2025-08-13T16:48:42.249+08:00] Uploading test info to server
[2025-08-13T16:48:44.225+08:00] UploadTestInfoToServer end
[2025-08-13T16:48:44.225+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:48:44.264+08:00] GetElementsNodeTree end
[2025-08-13T16:48:44.331+08:00] ScreenshotBase64 end
[2025-08-13T16:48:44.331+08:00] ParseContextFromWebPage end
[2025-08-13T16:48:44.331+08:00] Traversing element tree
[2025-08-13T16:48:44.331+08:00] TraverseTree end
[2025-08-13T16:48:44.331+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:48:44.331+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:48:44.332+08:00] ResizeImgBase64 end
[2025-08-13T16:48:44.697+08:00] Getting page URL
[2025-08-13T16:48:44.697+08:00] URL end
[2025-08-13T16:48:44.697+08:00] Uploading test info to server
[2025-08-13T16:48:46.687+08:00] UploadTestInfoToServer end
[2025-08-13T16:48:46.687+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:48:46.740+08:00] GetElementsNodeTree end
[2025-08-13T16:48:46.805+08:00] ScreenshotBase64 end
[2025-08-13T16:48:46.805+08:00] ParseContextFromWebPage end
[2025-08-13T16:48:46.805+08:00] Traversing element tree
[2025-08-13T16:48:46.805+08:00] TraverseTree end
[2025-08-13T16:48:46.805+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:48:46.805+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:48:46.806+08:00] ResizeImgBase64 end
[2025-08-13T16:48:53.669+08:00] Getting page URL
[2025-08-13T16:48:53.669+08:00] URL end
[2025-08-13T16:48:53.669+08:00] Uploading test info to server
[2025-08-13T16:48:55.631+08:00] UploadTestInfoToServer end
[2025-08-13T16:48:55.631+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:48:55.668+08:00] GetElementsNodeTree end
[2025-08-13T16:48:55.721+08:00] ScreenshotBase64 end
[2025-08-13T16:48:55.721+08:00] ParseContextFromWebPage end
[2025-08-13T16:48:55.721+08:00] Traversing element tree
[2025-08-13T16:48:55.721+08:00] TraverseTree end
[2025-08-13T16:48:55.721+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:48:55.721+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:48:55.722+08:00] ResizeImgBase64 end
[2025-08-13T16:49:01.602+08:00] Getting page URL
[2025-08-13T16:49:01.602+08:00] URL end
[2025-08-13T16:49:01.602+08:00] Uploading test info to server
[2025-08-13T16:49:03.429+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:03.429+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:03.469+08:00] GetElementsNodeTree end
[2025-08-13T16:49:03.531+08:00] ScreenshotBase64 end
[2025-08-13T16:49:03.531+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:03.531+08:00] Traversing element tree
[2025-08-13T16:49:03.531+08:00] TraverseTree end
[2025-08-13T16:49:03.531+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:03.531+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:03.532+08:00] ResizeImgBase64 end
[2025-08-13T16:49:03.865+08:00] Getting page URL
[2025-08-13T16:49:03.865+08:00] URL end
[2025-08-13T16:49:03.865+08:00] Uploading test info to server
[2025-08-13T16:49:05.672+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:05.672+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:05.724+08:00] GetElementsNodeTree end
[2025-08-13T16:49:05.788+08:00] ScreenshotBase64 end
[2025-08-13T16:49:05.788+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:05.788+08:00] Traversing element tree
[2025-08-13T16:49:05.788+08:00] TraverseTree end
[2025-08-13T16:49:05.788+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:05.788+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:05.789+08:00] ResizeImgBase64 end
[2025-08-13T16:49:10.464+08:00] Getting page URL
[2025-08-13T16:49:10.464+08:00] URL end
[2025-08-13T16:49:10.464+08:00] Uploading test info to server
[2025-08-13T16:49:12.904+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:12.904+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:12.942+08:00] GetElementsNodeTree end
[2025-08-13T16:49:12.997+08:00] ScreenshotBase64 end
[2025-08-13T16:49:12.997+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:12.997+08:00] Traversing element tree
[2025-08-13T16:49:12.998+08:00] TraverseTree end
[2025-08-13T16:49:12.998+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:12.998+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:12.999+08:00] ResizeImgBase64 end
[2025-08-13T16:49:17.884+08:00] Getting page URL
[2025-08-13T16:49:17.884+08:00] URL end
[2025-08-13T16:49:17.884+08:00] Uploading test info to server
[2025-08-13T16:49:19.971+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:19.971+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:20.016+08:00] GetElementsNodeTree end
[2025-08-13T16:49:20.082+08:00] ScreenshotBase64 end
[2025-08-13T16:49:20.082+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:20.082+08:00] Traversing element tree
[2025-08-13T16:49:20.082+08:00] TraverseTree end
[2025-08-13T16:49:20.082+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:20.082+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:20.084+08:00] ResizeImgBase64 end
[2025-08-13T16:49:20.429+08:00] Getting page URL
[2025-08-13T16:49:20.429+08:00] URL end
[2025-08-13T16:49:20.429+08:00] Uploading test info to server
[2025-08-13T16:49:22.678+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:22.678+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:22.740+08:00] GetElementsNodeTree end
[2025-08-13T16:49:22.788+08:00] ScreenshotBase64 end
[2025-08-13T16:49:22.788+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:22.788+08:00] Traversing element tree
[2025-08-13T16:49:22.788+08:00] TraverseTree end
[2025-08-13T16:49:22.788+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:22.788+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:22.789+08:00] ResizeImgBase64 end
[2025-08-13T16:49:27.048+08:00] Getting page URL
[2025-08-13T16:49:27.048+08:00] URL end
[2025-08-13T16:49:27.048+08:00] Uploading test info to server
[2025-08-13T16:49:29.066+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:29.066+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:29.095+08:00] GetElementsNodeTree end
[2025-08-13T16:49:29.141+08:00] ScreenshotBase64 end
[2025-08-13T16:49:29.142+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:29.142+08:00] Traversing element tree
[2025-08-13T16:49:29.142+08:00] TraverseTree end
[2025-08-13T16:49:29.142+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:29.142+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:29.143+08:00] ResizeImgBase64 end
[2025-08-13T16:49:33.243+08:00] Getting page URL
[2025-08-13T16:49:33.243+08:00] URL end
[2025-08-13T16:49:33.243+08:00] Uploading test info to server
[2025-08-13T16:49:35.004+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:35.004+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:35.034+08:00] GetElementsNodeTree end
[2025-08-13T16:49:35.090+08:00] ScreenshotBase64 end
[2025-08-13T16:49:35.090+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:35.090+08:00] Traversing element tree
[2025-08-13T16:49:35.090+08:00] TraverseTree end
[2025-08-13T16:49:35.090+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:35.090+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:35.091+08:00] ResizeImgBase64 end
[2025-08-13T16:49:35.436+08:00] Getting page URL
[2025-08-13T16:49:35.436+08:00] URL end
[2025-08-13T16:49:35.436+08:00] Uploading test info to server
[2025-08-13T16:49:37.438+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:37.438+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:37.479+08:00] GetElementsNodeTree end
[2025-08-13T16:49:37.526+08:00] ScreenshotBase64 end
[2025-08-13T16:49:37.526+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:37.526+08:00] Traversing element tree
[2025-08-13T16:49:37.527+08:00] TraverseTree end
[2025-08-13T16:49:37.527+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:37.527+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:37.528+08:00] ResizeImgBase64 end
[2025-08-13T16:49:41.681+08:00] Getting page URL
[2025-08-13T16:49:41.681+08:00] URL end
[2025-08-13T16:49:41.681+08:00] Uploading test info to server
[2025-08-13T16:49:43.689+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:43.689+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:43.716+08:00] GetElementsNodeTree end
[2025-08-13T16:49:43.786+08:00] ScreenshotBase64 end
[2025-08-13T16:49:43.786+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:43.786+08:00] Traversing element tree
[2025-08-13T16:49:43.786+08:00] TraverseTree end
[2025-08-13T16:49:43.786+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:43.786+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:43.787+08:00] ResizeImgBase64 end
[2025-08-13T16:49:48.553+08:00] Getting page URL
[2025-08-13T16:49:48.553+08:00] URL end
[2025-08-13T16:49:48.553+08:00] Uploading test info to server
[2025-08-13T16:49:50.451+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:50.451+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:50.486+08:00] GetElementsNodeTree end
[2025-08-13T16:49:50.534+08:00] ScreenshotBase64 end
[2025-08-13T16:49:50.534+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:50.534+08:00] Traversing element tree
[2025-08-13T16:49:50.534+08:00] TraverseTree end
[2025-08-13T16:49:50.534+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:50.534+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:50.535+08:00] ResizeImgBase64 end
[2025-08-13T16:49:50.876+08:00] Getting page URL
[2025-08-13T16:49:50.876+08:00] URL end
[2025-08-13T16:49:50.876+08:00] Uploading test info to server
[2025-08-13T16:49:52.732+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:52.732+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:52.771+08:00] GetElementsNodeTree end
[2025-08-13T16:49:52.818+08:00] ScreenshotBase64 end
[2025-08-13T16:49:52.818+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:52.818+08:00] Traversing element tree
[2025-08-13T16:49:52.818+08:00] TraverseTree end
[2025-08-13T16:49:52.818+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:52.818+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:52.820+08:00] ResizeImgBase64 end
[2025-08-13T16:49:57.494+08:00] Getting page URL
[2025-08-13T16:49:57.494+08:00] URL end
[2025-08-13T16:49:57.494+08:00] Uploading test info to server
[2025-08-13T16:49:59.291+08:00] UploadTestInfoToServer end
[2025-08-13T16:49:59.291+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:49:59.322+08:00] GetElementsNodeTree end
[2025-08-13T16:49:59.374+08:00] ScreenshotBase64 end
[2025-08-13T16:49:59.374+08:00] ParseContextFromWebPage end
[2025-08-13T16:49:59.374+08:00] Traversing element tree
[2025-08-13T16:49:59.374+08:00] TraverseTree end
[2025-08-13T16:49:59.374+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:49:59.374+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:49:59.375+08:00] ResizeImgBase64 end
[2025-08-13T16:50:03.000+08:00] Getting page URL
[2025-08-13T16:50:03.000+08:00] URL end
[2025-08-13T16:50:03.000+08:00] Uploading test info to server
[2025-08-13T16:50:04.901+08:00] UploadTestInfoToServer end
[2025-08-13T16:50:04.901+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:50:04.926+08:00] GetElementsNodeTree end
[2025-08-13T16:50:04.976+08:00] ScreenshotBase64 end
[2025-08-13T16:50:04.977+08:00] ParseContextFromWebPage end
[2025-08-13T16:50:04.977+08:00] Traversing element tree
[2025-08-13T16:50:04.977+08:00] TraverseTree end
[2025-08-13T16:50:04.977+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:50:04.977+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:50:04.978+08:00] ResizeImgBase64 end
[2025-08-13T16:50:09.771+08:00] Getting page URL
[2025-08-13T16:50:09.771+08:00] URL end
[2025-08-13T16:50:09.771+08:00] Uploading test info to server
[2025-08-13T16:50:17.533+08:00] UploadTestInfoToServer end
[2025-08-13T16:50:17.533+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:50:17.564+08:00] GetElementsNodeTree end
[2025-08-13T16:50:17.608+08:00] ScreenshotBase64 end
[2025-08-13T16:50:17.608+08:00] ParseContextFromWebPage end
[2025-08-13T16:50:17.608+08:00] Traversing element tree
[2025-08-13T16:50:17.608+08:00] TraverseTree end
[2025-08-13T16:50:17.608+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:50:17.608+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:50:17.609+08:00] ResizeImgBase64 end
[2025-08-13T16:50:17.933+08:00] Getting page URL
[2025-08-13T16:50:17.933+08:00] URL end
[2025-08-13T16:50:17.933+08:00] Uploading test info to server
[2025-08-13T16:50:25.442+08:00] UploadTestInfoToServer end
[2025-08-13T16:50:25.442+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:50:25.476+08:00] GetElementsNodeTree end
[2025-08-13T16:50:25.523+08:00] ScreenshotBase64 end
[2025-08-13T16:50:25.523+08:00] ParseContextFromWebPage end
[2025-08-13T16:50:25.523+08:00] Traversing element tree
[2025-08-13T16:50:25.523+08:00] TraverseTree end
[2025-08-13T16:50:25.523+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:50:25.523+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:50:25.524+08:00] ResizeImgBase64 end
[2025-08-13T16:50:30.268+08:00] Getting page URL
[2025-08-13T16:50:30.268+08:00] URL end
[2025-08-13T16:50:30.268+08:00] Uploading test info to server
[2025-08-13T16:50:33.265+08:00] UploadTestInfoToServer end
[2025-08-13T16:50:33.265+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:50:33.294+08:00] GetElementsNodeTree end
[2025-08-13T16:50:33.359+08:00] ScreenshotBase64 end
[2025-08-13T16:50:33.359+08:00] ParseContextFromWebPage end
[2025-08-13T16:50:33.359+08:00] Traversing element tree
[2025-08-13T16:50:33.359+08:00] TraverseTree end
[2025-08-13T16:50:33.359+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:50:33.359+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:50:33.360+08:00] ResizeImgBase64 end
[2025-08-13T16:50:37.375+08:00] Getting page URL
[2025-08-13T16:50:37.375+08:00] URL end
[2025-08-13T16:50:37.375+08:00] Uploading test info to server
[2025-08-13T16:50:39.759+08:00] UploadTestInfoToServer end
[2025-08-13T16:50:39.759+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:50:39.785+08:00] GetElementsNodeTree end
[2025-08-13T16:50:39.839+08:00] ScreenshotBase64 end
[2025-08-13T16:50:39.839+08:00] ParseContextFromWebPage end
[2025-08-13T16:50:39.839+08:00] Traversing element tree
[2025-08-13T16:50:39.839+08:00] TraverseTree end
[2025-08-13T16:50:39.839+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:50:39.839+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:50:39.840+08:00] ResizeImgBase64 end
[2025-08-13T16:50:43.907+08:00] Getting page URL
[2025-08-13T16:50:43.907+08:00] URL end
[2025-08-13T16:50:43.907+08:00] Uploading test info to server
[2025-08-13T16:50:45.901+08:00] UploadTestInfoToServer end
[2025-08-13T16:50:45.901+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:50:45.929+08:00] GetElementsNodeTree end
[2025-08-13T16:50:45.981+08:00] ScreenshotBase64 end
[2025-08-13T16:50:45.981+08:00] ParseContextFromWebPage end
[2025-08-13T16:50:45.981+08:00] Traversing element tree
[2025-08-13T16:50:45.981+08:00] TraverseTree end
[2025-08-13T16:50:45.981+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:50:45.981+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:50:45.981+08:00] ResizeImgBase64 end
[2025-08-13T16:50:46.321+08:00] Getting page URL
[2025-08-13T16:50:46.321+08:00] URL end
[2025-08-13T16:50:46.321+08:00] Uploading test info to server
[2025-08-13T16:50:48.239+08:00] UploadTestInfoToServer end
[2025-08-13T16:50:48.239+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:50:48.274+08:00] GetElementsNodeTree end
[2025-08-13T16:50:48.320+08:00] ScreenshotBase64 end
[2025-08-13T16:50:48.320+08:00] ParseContextFromWebPage end
[2025-08-13T16:50:48.320+08:00] Traversing element tree
[2025-08-13T16:50:48.320+08:00] TraverseTree end
[2025-08-13T16:50:48.320+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:50:48.320+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:50:48.321+08:00] ResizeImgBase64 end
[2025-08-13T16:50:54.036+08:00] Getting page URL
[2025-08-13T16:50:54.036+08:00] URL end
[2025-08-13T16:50:54.036+08:00] Uploading test info to server
[2025-08-13T16:50:55.877+08:00] UploadTestInfoToServer end
[2025-08-13T16:50:55.877+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:50:55.909+08:00] GetElementsNodeTree end
[2025-08-13T16:50:55.973+08:00] ScreenshotBase64 end
[2025-08-13T16:50:55.973+08:00] ParseContextFromWebPage end
[2025-08-13T16:50:55.973+08:00] Traversing element tree
[2025-08-13T16:50:55.973+08:00] TraverseTree end
[2025-08-13T16:50:55.973+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:50:55.973+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:50:55.974+08:00] ResizeImgBase64 end
[2025-08-13T16:50:56.319+08:00] Getting page URL
[2025-08-13T16:50:56.319+08:00] URL end
[2025-08-13T16:50:56.319+08:00] Uploading test info to server
[2025-08-13T16:50:58.265+08:00] UploadTestInfoToServer end
[2025-08-13T16:50:58.265+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:50:58.290+08:00] GetElementsNodeTree end
[2025-08-13T16:50:58.337+08:00] ScreenshotBase64 end
[2025-08-13T16:50:58.337+08:00] ParseContextFromWebPage end
[2025-08-13T16:50:58.337+08:00] Traversing element tree
[2025-08-13T16:50:58.337+08:00] TraverseTree end
[2025-08-13T16:50:58.337+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:50:58.337+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:50:58.338+08:00] ResizeImgBase64 end
[2025-08-13T16:51:03.464+08:00] Getting page URL
[2025-08-13T16:51:03.464+08:00] URL end
[2025-08-13T16:51:03.464+08:00] Uploading test info to server
[2025-08-13T16:51:05.384+08:00] UploadTestInfoToServer end
[2025-08-13T16:51:05.384+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:51:05.412+08:00] GetElementsNodeTree end
[2025-08-13T16:51:05.462+08:00] ScreenshotBase64 end
[2025-08-13T16:51:05.462+08:00] ParseContextFromWebPage end
[2025-08-13T16:51:05.462+08:00] Traversing element tree
[2025-08-13T16:51:05.462+08:00] TraverseTree end
[2025-08-13T16:51:05.462+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:51:05.462+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:51:05.463+08:00] ResizeImgBase64 end
[2025-08-13T16:51:05.797+08:00] Getting page URL
[2025-08-13T16:51:05.797+08:00] URL end
[2025-08-13T16:51:05.797+08:00] Uploading test info to server
[2025-08-13T16:51:07.765+08:00] UploadTestInfoToServer end
[2025-08-13T16:51:07.765+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:51:07.799+08:00] GetElementsNodeTree end
[2025-08-13T16:51:07.847+08:00] ScreenshotBase64 end
[2025-08-13T16:51:07.847+08:00] ParseContextFromWebPage end
[2025-08-13T16:51:07.847+08:00] Traversing element tree
[2025-08-13T16:51:07.847+08:00] TraverseTree end
[2025-08-13T16:51:07.847+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:51:07.847+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:51:07.848+08:00] ResizeImgBase64 end
[2025-08-13T16:51:14.362+08:00] Getting page URL
[2025-08-13T16:51:14.362+08:00] URL end
[2025-08-13T16:51:14.362+08:00] Uploading test info to server
[2025-08-13T16:51:16.575+08:00] UploadTestInfoToServer end
[2025-08-13T16:51:16.575+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:51:16.603+08:00] GetElementsNodeTree end
[2025-08-13T16:51:16.653+08:00] ScreenshotBase64 end
[2025-08-13T16:51:16.653+08:00] ParseContextFromWebPage end
[2025-08-13T16:51:16.653+08:00] Traversing element tree
[2025-08-13T16:51:16.653+08:00] TraverseTree end
[2025-08-13T16:51:16.653+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:51:16.653+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:51:16.654+08:00] ResizeImgBase64 end
[2025-08-13T16:51:16.996+08:00] Getting page URL
[2025-08-13T16:51:16.996+08:00] URL end
[2025-08-13T16:51:16.996+08:00] Uploading test info to server
[2025-08-13T16:51:18.842+08:00] UploadTestInfoToServer end
[2025-08-13T16:51:18.842+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:51:18.873+08:00] GetElementsNodeTree end
[2025-08-13T16:51:18.919+08:00] ScreenshotBase64 end
[2025-08-13T16:51:18.919+08:00] ParseContextFromWebPage end
[2025-08-13T16:51:18.919+08:00] Traversing element tree
[2025-08-13T16:51:18.919+08:00] TraverseTree end
[2025-08-13T16:51:18.919+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:51:18.919+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:51:18.920+08:00] ResizeImgBase64 end
[2025-08-13T16:51:24.744+08:00] Getting page URL
[2025-08-13T16:51:24.744+08:00] URL end
[2025-08-13T16:51:24.744+08:00] Uploading test info to server
[2025-08-13T16:51:26.682+08:00] UploadTestInfoToServer end
[2025-08-13T16:51:26.683+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:51:26.717+08:00] GetElementsNodeTree end
[2025-08-13T16:51:26.770+08:00] ScreenshotBase64 end
[2025-08-13T16:51:26.770+08:00] ParseContextFromWebPage end
[2025-08-13T16:51:26.770+08:00] Traversing element tree
[2025-08-13T16:51:26.770+08:00] TraverseTree end
[2025-08-13T16:51:26.770+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:51:26.770+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:51:26.771+08:00] ResizeImgBase64 end
[2025-08-13T16:51:31.243+08:00] Getting page URL
[2025-08-13T16:51:31.243+08:00] URL end
[2025-08-13T16:51:31.243+08:00] Uploading test info to server
[2025-08-13T16:51:33.296+08:00] UploadTestInfoToServer end
[2025-08-13T16:51:33.296+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:51:33.341+08:00] GetElementsNodeTree end
[2025-08-13T16:51:33.387+08:00] ScreenshotBase64 end
[2025-08-13T16:51:33.387+08:00] ParseContextFromWebPage end
[2025-08-13T16:51:33.387+08:00] Traversing element tree
[2025-08-13T16:51:33.387+08:00] TraverseTree end
[2025-08-13T16:51:33.387+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:51:33.387+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:51:33.389+08:00] ResizeImgBase64 end
[2025-08-13T16:51:37.974+08:00] Getting page URL
[2025-08-13T16:51:37.974+08:00] URL end
[2025-08-13T16:51:37.974+08:00] Uploading test info to server
[2025-08-13T16:51:47.826+08:00] UploadTestInfoToServer end
[2025-08-13T16:51:47.826+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:51:47.856+08:00] GetElementsNodeTree end
[2025-08-13T16:51:47.904+08:00] ScreenshotBase64 end
[2025-08-13T16:51:47.904+08:00] ParseContextFromWebPage end
[2025-08-13T16:51:47.904+08:00] Traversing element tree
[2025-08-13T16:51:47.904+08:00] TraverseTree end
[2025-08-13T16:51:47.904+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:51:47.904+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:51:47.905+08:00] ResizeImgBase64 end
[2025-08-13T16:51:48.246+08:00] Getting page URL
[2025-08-13T16:51:48.246+08:00] URL end
[2025-08-13T16:51:48.246+08:00] Uploading test info to server
[2025-08-13T16:51:50.098+08:00] UploadTestInfoToServer end
[2025-08-13T16:51:50.098+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:51:50.137+08:00] GetElementsNodeTree end
[2025-08-13T16:51:50.187+08:00] ScreenshotBase64 end
[2025-08-13T16:51:50.187+08:00] ParseContextFromWebPage end
[2025-08-13T16:51:50.187+08:00] Traversing element tree
[2025-08-13T16:51:50.187+08:00] TraverseTree end
[2025-08-13T16:51:50.187+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:51:50.187+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:51:50.188+08:00] ResizeImgBase64 end
[2025-08-13T16:51:54.611+08:00] Getting page URL
[2025-08-13T16:51:54.611+08:00] URL end
[2025-08-13T16:51:54.611+08:00] Uploading test info to server
[2025-08-13T16:51:56.610+08:00] UploadTestInfoToServer end
[2025-08-13T16:51:56.610+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:51:56.651+08:00] GetElementsNodeTree end
[2025-08-13T16:51:56.707+08:00] ScreenshotBase64 end
[2025-08-13T16:51:56.707+08:00] ParseContextFromWebPage end
[2025-08-13T16:51:56.707+08:00] Traversing element tree
[2025-08-13T16:51:56.707+08:00] TraverseTree end
[2025-08-13T16:51:56.707+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:51:56.707+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:51:56.707+08:00] ResizeImgBase64 end
[2025-08-13T16:51:56.707+08:00] Getting page URL
[2025-08-13T16:51:56.707+08:00] URL end
[2025-08-13T16:51:56.707+08:00] Uploading test info to server
[2025-08-13T16:51:58.695+08:00] UploadTestInfoToServer end
[2025-08-13T16:51:58.695+08:00] Starting parallel operations: screenshot and element tree
[2025-08-13T16:51:58.725+08:00] GetElementsNodeTree end
[2025-08-13T16:51:58.780+08:00] ScreenshotBase64 end
[2025-08-13T16:51:58.780+08:00] ParseContextFromWebPage end
[2025-08-13T16:51:58.780+08:00] Traversing element tree
[2025-08-13T16:51:58.780+08:00] TraverseTree end
[2025-08-13T16:51:58.780+08:00] size: 1280x768 dpr: 1.0000000074505806
[2025-08-13T16:51:58.780+08:00] Resizing screenshot for high DPR display
[2025-08-13T16:51:58.781+08:00] ResizeImgBase64 end
