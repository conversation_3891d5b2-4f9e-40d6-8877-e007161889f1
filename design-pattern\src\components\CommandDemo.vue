<template>
  <div class="command-demo">
    <h2>命令模式演示 - 智能家居遥控器</h2>

    <div class="device-status">
      <h3>{{ lightStatus }}</h3>
    </div>

    <div class="controls">
      <button @click="turnOn">开灯</button>
      <button @click="turnOff">关灯</button>
      <button @click="undo" :disabled="!canUndo">撤销</button>
      <button @click="redo" :disabled="!canRedo">重做</button>
    </div>

    <div class="command-history">
      <h3>命令历史</h3>
      <ul>
        <li v-for="(item, index) in history" :key="index">
          {{ item }}
        </li>
      </ul>
    </div>

    <div class="result" v-if="lastResult">
      <p>{{ lastResult }}</p>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { LightReceiver, TurnOnCommand, TurnOffCommand } from '@/utils/commands/types'
import { RemoteControl } from '@/utils/commands/RemoteControl'

export default defineComponent({
  name: 'CommandDemo',
  setup() {
    const light = new LightReceiver()
    const remote = new RemoteControl()

    const lightStatus = ref(light.getStatus())
    const lastResult = ref('')
    const history = ref<string[]>([])

    const updateStatus = () => {
      lightStatus.value = light.getStatus()
    }

    const turnOn = () => {
      const command = new TurnOnCommand(light)
      lastResult.value = remote.submit(command)
      history.value = remote.getHistory()
      updateStatus()
    }

    const turnOff = () => {
      const command = new TurnOffCommand(light)
      lastResult.value = remote.submit(command)
      history.value = remote.getHistory()
      updateStatus()
    }

    const undo = () => {
      const result = remote.undo()
      if (result) {
        lastResult.value = `撤销操作: ${result}`
        history.value = remote.getHistory()
        updateStatus()
      }
    }

    const redo = () => {
      const result = remote.redo()
      if (result) {
        lastResult.value = `重做操作: ${result}`
        history.value = remote.getHistory()
        updateStatus()
      }
    }

    const canUndo = computed(() => history.value.length > 0)
    const canRedo = computed(() => remote.getHistory().length < history.value.length)

    return {
      lightStatus,
      lastResult,
      history,
      turnOn,
      turnOff,
      undo,
      redo,
      canUndo,
      canRedo,
    }
  },
})
</script>

<style scoped>
.command-demo {
  border: 1px solid #eee;
  padding: 20px;
  margin: 20px 0;
  border-radius: 8px;
  font-family: Arial, sans-serif;
  max-width: 500px;
}

.device-status {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f8f8;
  border-radius: 4px;
}

.controls {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

button {
  padding: 10px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.command-history {
  margin-top: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 4px;
}

.command-history ul {
  list-style-type: none;
  padding: 0;
}

.command-history li {
  padding: 5px 0;
  border-bottom: 1px solid #ddd;
}

.result {
  margin-top: 15px;
  padding: 10px;
  background: #e8f4fd;
  border-radius: 4px;
  color: #2c3e50;
}
</style>
