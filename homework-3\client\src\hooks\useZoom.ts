import { useState } from "react";

export function useZoom() {
  const [zoom, setZoom] = useState(1);
  const [offset, setOffset] = useState({ x: 0, y: 0 });

  function onWheel(e: React.WheelEvent) {
    e.preventDefault();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    setZoom((z) => Math.max(0.2, Math.min(2, z + delta)));
  }

  function onPan(dx: number, dy: number) {
    setOffset((o) => ({ x: o.x + dx, y: o.y + dy }));
  }

  return { zoom, offset, onWheel, onPan, setOffset };
}
