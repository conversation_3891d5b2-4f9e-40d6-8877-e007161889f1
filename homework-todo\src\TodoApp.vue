<!-- components/TodoInput.vue -->
<template>
  <div class="todo-container">
    <div class="todo-header">
      <h2>待办</h2>
    </div>

    <!-- 输入组件 -->
    <TodoInput @create="todoStore.addTodo" />

    <!-- 列表组件 -->
    <TodoList
      :todos="todoStore.todos"
      :showCompleted="todoStore.showCompleted"
      :completedCount="todoStore.completedCount"
      @toggle="todoStore.toggleTodo"
      @toggleShowCompleted="todoStore.toggleShowCompleted"
      @delete="todoStore.deleteTodo"
    />

    <!-- Date Picker Dialog -->
    <div v-if="showDatePicker" class="modal-overlay" @click="showDatePicker = false">
      <div class="date-picker-dialog" @click.stop>
        <div class="dialog-header">
          <h3>其他时间</h3>
          <button @click="showDatePicker = false" class="close-btn">✕</button>
        </div>

        <div class="date-picker-content">
          <div class="calendar-section">
            <div class="calendar-header">
              <button @click="previousMonth">‹</button>
              <span>{{ currentMonthYear }}</span>
              <button @click="nextMonth">›</button>
            </div>
            <div class="calendar-grid">
              <div class="calendar-weekdays">
                <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
              </div>
              <div class="calendar-days">
                <div
                  v-for="date in calendarDays"
                  :key="date.key"
                  class="calendar-day"
                  :class="{
                    'other-month': !date.isCurrentMonth,
                    selected: isSelectedDate(date.date),
                    today: isToday(date.date),
                  }"
                  @click="selectDate(date.date)"
                >
                  {{ date.day }}
                </div>
              </div>
            </div>
          </div>

          <div class="time-options">
            <div class="option-row">
              <label>
                <input type="checkbox" v-model="hasDeadline" />
                截止时间
              </label>
              <input
                v-model="deadlineTimeStr"
                :disabled="!hasDeadline"
                type="time"
                value="18:00"
                class="time-input"
              />
            </div>

            <div class="option-row">
              <label>
                <input type="checkbox" v-model="hasReminder" />
                设置提醒
              </label>
              <input
                v-model="reminderText"
                :disabled="!hasReminder"
                placeholder="提醒时间"
                class="reminder-input"
              />
            </div>
          </div>
        </div>

        <div class="dialog-footer">
          <button @click="showDatePicker = false" class="cancel-btn">取消</button>
          <button @click="confirmDateTime" class="confirm-btn">确认</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, provide } from 'vue'
import { useTodoStore } from './stores/todo'
import TodoInput from './components/TodoInput.vue'
import TodoList from './components/TodoList.vue'

// Use todo store
const todoStore = useTodoStore()

// Date picker local state (still needed for the calendar modal)
const showDatePicker = ref(false)
const selectedDate = ref(new Date())
const hasDeadline = ref(false)
const hasReminder = ref(false)
const deadlineTimeStr = ref('18:00')
const reminderText = ref('')
const currentDate = ref(new Date())

const weekdays = ['日', '一', '二', '三', '四', '五', '六']

// Provide date picker methods to child components
provide('openDatePicker', () => {
  showDatePicker.value = true
})

// Computed properties for calendar
const currentMonthYear = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth() + 1
  return `${year} 年 ${month} 月`
})

const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const firstDay = new Date(year, month, 1)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())

  const days = []
  const current = new Date(startDate)

  for (let i = 0; i < 42; i++) {
    days.push({
      date: new Date(current),
      day: current.getDate(),
      isCurrentMonth: current.getMonth() === month,
      key: current.getTime(),
    })
    current.setDate(current.getDate() + 1)
  }

  return days
})

// Date picker methods
const isSelectedDate = (date: Date) => {
  return date.toDateString() === selectedDate.value.toDateString()
}

const isToday = (date: Date) => {
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

const selectDate = (date: Date) => {
  selectedDate.value = new Date(date)
}

const previousMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
}

const nextMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
}

const confirmDateTime = () => {
  showDatePicker.value = false
}
</script>

<style scoped>
.todo-container {
  width: 60%;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.todo-header h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.date-picker-dialog {
  background: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
}

.date-picker-content {
  padding: 20px;
}

.calendar-section {
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.calendar-header button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
}

.calendar-grid {
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  overflow: hidden;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8f9fa;
}

.weekday {
  padding: 8px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.calendar-day {
  padding: 8px;
  text-align: center;
  cursor: pointer;
  border-right: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.calendar-day:hover {
  background-color: #f0f8ff;
}

.calendar-day.other-month {
  color: #ccc;
}

.calendar-day.selected {
  background-color: #409eff;
  color: white;
}

.calendar-day.today {
  background-color: #e6f7ff;
  font-weight: bold;
}

.time-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-row label {
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 80px;
}

.time-input,
.reminder-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
}

.time-input:disabled,
.reminder-input:disabled {
  background: #f5f5f5;
  color: #ccc;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px 20px;
  border-top: 1px solid #e1e5e9;
}

.cancel-btn,
.confirm-btn {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.cancel-btn {
  background: white;
  border: 1px solid #e1e5e9;
  color: #666;
}

.confirm-btn {
  background: #409eff;
  border: 1px solid #409eff;
  color: white;
}
</style>
