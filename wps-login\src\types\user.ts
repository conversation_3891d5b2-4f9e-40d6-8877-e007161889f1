/**
 * 用户相关类型定义
 */

// 用户数据类型
export interface User {
  userid: number;
  nickname: string;
  company_id: number;
  company_name: string;
  company_logo: string;
  company_custom_domain: string;
  is_company_account: boolean;
  avatar_url: string;
  status: number;
  reason: string;
  reason_v2?: {
    key: string;
    translate: boolean;
  };
  link_text?: string;
  link_text_v2?: {
    key: string;
    translate: boolean;
  };
  link_url?: string;
  is_current: boolean;
  is_login: boolean;
  loginmode: string;
  session_status: number;
  logout_reason?: string;
  need_tfa: boolean;
  default_select: boolean;
  last_active_time: number;
}

// 登录步骤类型
export type LoginStep = "phone" | "verification" | "account-select";

// 验证状态类型
export type CaptchaState = "idle" | "loading" | "completed";

// 登录表单Props接口
export interface LoginFormProps {
  onLoginSuccess?: (users: User[], selectedUserIds: number[]) => void;
  onBack?: () => void;
  showAutoLogin?: boolean;
  showBackButton?: boolean;
  title?: string;
  subtitle?: string;
  isMobile?: boolean;
  agreeTerms?: boolean;
  setAgreeTerms?: (agree: boolean) => void;
}

// 账号选择组件Props接口
export interface AccountSelectProps {
  users: User[];
  initialSelectedIds: number[];
  onBack: () => void;
  onConfirm: (selectedUserIds: number[]) => void;
  isMobile?: boolean;
}

// 隐私协议弹窗Props接口
export interface ConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAgree: () => void;
  onCancel: () => void;
}

// API响应类型
export interface LoginResponse {
  result: string;
  ssid?: string;
  msg?: string;
}

export interface UserListResponse {
  users: User[];
}
