import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import AccountSelect from "../AccountSelect";

// 准备模拟的用户数据
const mockUsers = [
  {
    userid: 1,
    nickname: "张三",
    company_id: 0, // 个人账号
    company_name: "",
    company_logo: "",
    avatar_url: "path/to/avatar1.png",
    is_login: false,
  },
  {
    userid: 2,
    nickname: "李四",
    company_id: 123, // 公司账号
    company_name: "WPS公司",
    company_logo: "path/to/company_logo.png",
    avatar_url: "path/to/avatar2.png",
    is_login: true, // 已登录
  },
  {
    userid: 3,
    nickname: "王五",
    company_id: 456, // 公司账号，但没有logo
    company_name: "金山文档",
    company_logo: "",
    avatar_url: "path/to/avatar3.png",
    is_login: false,
  },
];

describe("账号选择组件 (AccountSelect)", () => {
  const mockProps = {
    users: mockUsers,
    initialSelectedIds: [2], // 默认选中已登录的李四
    onBack: vi.fn(),
    onConfirm: vi.fn(),
    isMobile: false,
  };

  it("应该能正确渲染用户列表", () => {
    render(<AccountSelect {...mockProps} />);
    expect(screen.getByText("张三")).toBeInTheDocument();
    expect(screen.getByText("李四")).toBeInTheDocument();
    expect(screen.getByText("王五")).toBeInTheDocument();
  });

  it("应该根据 company_id 决定显示公司 logo 还是个人头像", () => {
    render(<AccountSelect {...mockProps} />);
    const images = screen.getAllByRole("img");

    // 李四 (userid: 2) 应该显示公司 logo
    expect(
      images.some(
        (img) => img.getAttribute("src") === "path/to/company_logo.png"
      )
    ).toBe(true);
    // 王五 (userid: 3) 虽然是公司账号但没有 logo，应该显示个人头像
    expect(
      images.some((img) => img.getAttribute("src") === "path/to/avatar3.png")
    ).toBe(true);
  });

  it("点击未登录的账号时，应该能切换选中状态", () => {
    const onConfirmMock = vi.fn();
    render(<AccountSelect {...mockProps} onConfirm={onConfirmMock} />);

    // 初始时，确认按钮应该可用 (因为有一个已选)
    const confirmButton = screen.getByRole("button", { name: "确认登录" });
    expect(confirmButton).not.toBeDisabled();

    // 点击张三 (未登录)
    const userZhangSan = screen.getByText("张三").closest(".account-item");
    fireEvent.click(userZhangSan);

    // 点击后，张三应该被选中
    expect(userZhangSan).toHaveClass("selected");

    // 再次点击张三，应该取消选中
    fireEvent.click(userZhangSan);
    expect(userZhangSan).not.toHaveClass("selected");
  });

  it("点击已登录的账号时，不应该改变其选中状态", () => {
    render(<AccountSelect {...mockProps} />);
    const userLiSi = screen.getByText("李四").closest(".account-item");

    // 李四默认是选中的
    expect(userLiSi).toHaveClass("selected");

    // 点击已登录的李四
    fireEvent.click(userLiSi);

    // 李四应该依然是选中状态
    expect(userLiSi).toHaveClass("selected");
  });

  it("当没有账号被选中时，“确认登录”按钮应该被禁用", () => {
    // 初始时没有任何账号被选中
    render(<AccountSelect {...mockProps} initialSelectedIds={[]} />);
    const confirmButton = screen.getByRole("button", { name: "确认登录" });
    expect(confirmButton).toBeDisabled();
  });

  it("点击“确认登录”按钮时，应该带着所有选中的用户 ID 触发 onConfirm 回调", () => {
    const onConfirmMock = vi.fn();
    render(<AccountSelect {...mockProps} onConfirm={onConfirmMock} />);

    // 额外选中张三
    fireEvent.click(screen.getByText("张三").closest(".account-item"));

    // 点击确认
    fireEvent.click(screen.getByRole("button", { name: "确认登录" }));

    // 确认回调被调用，并且参数应该是 [2, 1] (顺序不重要)
    expect(onConfirmMock).toHaveBeenCalledTimes(1);
    expect(onConfirmMock.mock.calls[0][0]).toEqual(
      expect.arrayContaining([1, 2])
    );
  });
});
