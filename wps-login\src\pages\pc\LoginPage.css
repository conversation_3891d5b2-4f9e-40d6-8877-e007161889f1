/* Main Login Page Styles */
.login-container {
  min-height: 100vh;
  width: 100%;
  background-color: #fff;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 5vh;
  overflow-y: auto;
  box-sizing: border-box;
}
.login-content {
  display: flex;
  width: 100%;
  max-width: 992px;
  background: white;
  min-height: 500px;
  justify-content: center;
  margin: 0 auto;
  position: relative;
}
/* 左侧Logo区域 */
.logo-section {
  width: 400px;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 192px;
}
.logo-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.logo {
  width: 400px;
  height: 500px;
  background-image: url(../../assets/logo.png);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
} /* 右侧登录区域 */
.login-section {
  width: 400px;
  height: 500px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  overflow: hidden; /* 防止内容溢出 */
  position: relative; /* 确保定位正确 */
}
.login-form {
  width: 100%;
  height: 100%;
  max-width: 320px;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* 均匀分布内容 */
}
.login-header {
  text-align: center;
  margin-bottom: 1.5rem; /* 减小间距 */
}
.login-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 0.5rem;
}
.login-subtitle {
  color: #6b7280;
}
.qr-container {
  background: #fff;
  padding: 0.75rem; /* 减小内边距 */
  margin-bottom: 0.5rem; /* 减小间距 */
  display: flex;
  align-items: center;
  justify-content: center;
}
.qr-placeholder {
  width: 190px;
  height: 190px;
  background-image: url(../../assets/qrcode.png);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.checkbox-group {
  width: 100%;
  margin-bottom: 1rem; /* 减小间距 */
}
.checkbox-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem; /* 减小间距 */
}
.checkbox {
  margin-right: 0.5rem;
}
.checkbox-label {
  font-size: 0.875rem;
  color: #6b7280;
}
.checkbox-label a {
  color: #3b82f6;
  text-decoration: none;
}
.checkbox-label a:hover {
  text-decoration: underline;
}
.login-methods {
  margin: 5px auto 0; /* 减小上边距 */
  justify-content: space-around; /* 均匀分布 */
  display: flex;
  width: 100%;
  max-width: 280px; /* 减小最大宽度，确保不超出边框 */
}
.login-methods .login-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  width: 50px; /* 减小宽度，确保不超出边框 */
  margin: 0;
  padding: 0;
}
.login-methods .login-icon {
  display: block;
  width: 38px;
  height: 36px;
  background-image: url(../../assets/login.png);
  background-repeat: no-repeat;
  background-size: 46px 752px; /* 设置为原始图片尺寸 46×752像素 */
  background-position-x: 0;
}
.qq-icon {
  background-position-y: -192px; /* QQ图标位于从上往下192像素处 */
}
.phone-icon {
  background-position-y: -228px; /* 手机图标位于从上往下228像素处 */
}
.sso-icon {
  background-position-y: -264px; /* 专属账号图标位于从上往下264像素处 */
}
.more-icon {
  background-position-y: -300px; /* 更多图标位于从上往下300像素处 */
}
.login_icon_txt {
  display: block;
  line-height: 1.2;
  margin-top: 4px;
  color: #737579;
  font-size: 12px;
  text-align: center;
  width: 50px; /* 修正拼写错误，并与login-item宽度一致 */
  white-space: nowrap; /* 防止文字换行 */
  overflow: visible; /* 允许文字溢出 */
} /* Responsive Design */
@media (max-width: 1024px) {
  .login-content {
    max-width: 800px;
  }
  .logo-section {
    margin-right: 0;
  }
} /* 850px - 640px 之间的响应式样式：保持双列布局但缩小尺寸 */
@media (max-width: 850px) and (min-width: 641px) {
  .login-content {
    max-width: 95%;
    padding: 0 10px;
    justify-content: space-between; /* 确保左右两部分均匀分布 */
  }
  .logo-section {
    width: 50%;
    margin-right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 300px; /* 确保logo区域有足够的最小宽度 */
  }
  .logo {
    width: 100%;
    height: 500px; /* 保持logo高度 */
    background-size: contain; /* 确保logo自适应宽度 */
    background-position: center; /* 确保logo居中显示 */
    max-width: 100%; /* 确保logo不超过父容器宽度 */
    display: block; /* 确保logo显示 */
    opacity: 1; /* 确保logo可见 */
  }
  .login-section {
    width: 50%;
    padding: 1.5rem;
  } /* 防止隐私协议文本换行 */
  .checkbox-label {
    font-size: 12px; /* 减小字体大小 */
    white-space: normal; /* 允许文本换行但控制位置 */
    overflow-wrap: break-word; /* 允许长单词断行 */
    hyphens: auto; /* 在适当的位置添加连字符 */
    max-width: 100%; /* 确保不超过父容器 */
  }
  .checkbox-item {
    display: flex;
    align-items: flex-start; /* 从顶部对齐，使文本能更好地显示 */
  }
  .login-form {
    max-width: 300px; /* 适当缩小表单宽度 */
  }
}
