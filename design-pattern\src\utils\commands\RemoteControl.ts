// 具体调用者
import type { Command } from './types'

export class RemoteControl {
  private commandHistory: Command[] = []
  private undoHistory: Command[] = []

  submit(command: Command): string {
    const result = command.execute()
    this.commandHistory.push(command)
    return result
  }

  undo(): string | null {
    if (this.commandHistory.length === 0) return null

    const command = this.commandHistory.pop()!
    const result = command.undo()
    this.undoHistory.push(command)
    return result
  }

  redo(): string | null {
    if (this.undoHistory.length === 0) return null

    const command = this.undoHistory.pop()!
    const result = command.execute()
    this.commandHistory.push(command)
    return result
  }

  getHistory(): string[] {
    return this.commandHistory.map((_, i) => `命令 #${i + 1}`)
  }
}
