import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import './App.css';  // 引入外部CSS文件

function App() {
  return (
    <div className="container">
      <BrowserRouter>
        <h1>微前端项目</h1>
        <button 
          className="link-button" 
          onClick={() => window.location.href = '/'}
        >
          主页
        </button>
        <button 
          className="link-button" 
          onClick={() => window.location.href = '/react-sub'}
        >
          微前端 React 子应用
        </button>
        <button 
          className="link-button" 
          onClick={() => window.location.href = '/vue-sub'}
        >
          微前端 Vue 子应用
        </button>
      </BrowserRouter>

      {/* 微前端子应用挂载容器 */}
      <div id="react-sub" className="sub-app-container" />
      <div id="vue-sub" className="sub-app-container" />
    </div>
  );
}

export default App;
