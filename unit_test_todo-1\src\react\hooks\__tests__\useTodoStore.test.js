import { renderHook, act } from "@testing-library/react";
import { useTodoStore } from "../useTodoStore";

describe("useTodoStore Hook", () => {
  test("应该初始化为空数组", () => {
    const { result } = renderHook(() => useTodoStore());
    expect(result.current.todos).toEqual([]);
    expect(result.current.totalCount).toBe(0);
    expect(result.current.completedCount).toBe(0);
    expect(result.current.uncompletedCount).toBe(0);
  });

  test("应该能添加新待办事项", () => {
    const { result } = renderHook(() => useTodoStore());

    act(() => {
      const success = result.current.addTodo("测试新增待办项");
      expect(success).toBe(true);
    });
    expect(result.current.todos.length).toBe(1);
    expect(result.current.todos[0].text).toBe("测试新增待办项");
    expect(result.current.todos[0].completed).toBe(false);
    expect(result.current.totalCount).toBe(1);
    expect(result.current.uncompletedCount).toBe(1);
  });

  test("添加空待办事项应该失败", () => {
    const { result } = renderHook(() => useTodoStore());

    act(() => {
      const success = result.current.addTodo("");
      expect(success).toBe(false);
    });

    expect(result.current.todos.length).toBe(0);
  });

  test("应该能切换待办事项状态", () => {
    const { result } = renderHook(() => useTodoStore());

    // 添加待办事项
    act(() => {
      result.current.addTodo("测试待办");
    });

    const todoId = result.current.todos[0].id;

    // 切换待办事项状态
    act(() => {
      result.current.toggleTodo(todoId);
    });

    expect(result.current.todos[0].completed).toBe(true);
    expect(result.current.completedCount).toBe(1);
    expect(result.current.uncompletedCount).toBe(0);

    // 再次切换状态
    act(() => {
      result.current.toggleTodo(todoId);
    });

    expect(result.current.todos[0].completed).toBe(false);
  });

  test("应该能删除待办事项", () => {
    const { result } = renderHook(() => useTodoStore());

    // 添加待办事项
    act(() => {
      result.current.addTodo("测试待办1");
      result.current.addTodo("测试待办2");
    });

    expect(result.current.todos.length).toBe(2);

    const todoId = result.current.todos[0].id;

    // 删除第一个待办事项
    act(() => {
      result.current.deleteTodo(todoId);
    });

    expect(result.current.todos.length).toBe(1);
    expect(result.current.todos[0].text).toBe("测试待办2");
  });

  test("应该能更新待办事项", () => {
    const { result } = renderHook(() => useTodoStore());

    // 添加待办事项
    act(() => {
      result.current.addTodo("测试待办");
    });

    const todoId = result.current.todos[0].id;

    // 更新待办事项
    act(() => {
      result.current.updateTodo(todoId, { text: "已更新待办" });
    });

    expect(result.current.todos[0].text).toBe("已更新待办");
  });

  test("应该能清除已完成的待办事项", () => {
    const { result } = renderHook(() => useTodoStore());

    // 添加三个待办事项，其中两个已完成
    act(() => {
      result.current.addTodo("待办1");
      result.current.addTodo("待办2");
      result.current.addTodo("待办3");
    });
    act(() => {
      const todo1Id = result.current.todos[0].id;
      const todo2Id = result.current.todos[1].id;
      result.current.toggleTodo(todo1Id);
      result.current.toggleTodo(todo2Id);
    });

    expect(result.current.todos.length).toBe(3);
    expect(result.current.completedCount).toBe(2);

    // 清除已完成
    act(() => {
      result.current.clearCompleted();
    });

    expect(result.current.todos.length).toBe(1);
    expect(result.current.completedCount).toBe(0);
    expect(result.current.todos[0].text).toBe("待办3");
  });

  test("filteredTodos方法应该能正确过滤", () => {
    const { result } = renderHook(() => useTodoStore());

    // 添加三个待办事项，其中一个已完成
    act(() => {
      result.current.addTodo("待办1");
      result.current.addTodo("待办2");
      result.current.addTodo("待办3");
    });
    act(() => {
      const todo1Id = result.current.todos[0].id;
      result.current.toggleTodo(todo1Id);
    });
    // 测试过滤
    expect(result.current.filteredTodos("all").length).toBe(3);
    expect(result.current.filteredTodos("active").length).toBe(2);
    expect(result.current.filteredTodos("completed").length).toBe(1);
  });
});
