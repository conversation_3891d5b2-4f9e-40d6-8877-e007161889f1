/* 账号选择页面通用样式 */
.account-select-container {
  width: 100%;
}

/* 头部样式 */
.account-select-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 44px;
  margin-bottom: 1rem;
}

.back-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 5px;
  color: #333;
}

.back-button:before {
  width: 14px;
  height: 13px;
  content: url("@/assets/arrowL.svg");
  margin-right: 4px;
  display: inline-block;
  vertical-align: middle;
}

.account-select-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  padding: 0 40px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.account-select-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  text-align: center;
  font-weight: normal;
}

.accounts-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
  max-height: 300px;
  overflow-y: auto;
}

.account-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
}

.account-item:hover:not(.logged-in) {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.account-item.selected {
  border-color: #3b82f6;
  background-color: #f0f7ff;
}

.account-item.logged-in {
  opacity: 0.7;
  cursor: not-allowed;
}

.account-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: white;
  font-size: 16px;
}

.account-info {
  flex: 1;
}

.account-name {
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
}

.account-user {
  font-size: 14px;
  color: #6b7280;
}

.account-status {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
}

.account-check {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.account-footer {
  margin-top: 20px;
}

.selected-info {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  margin-bottom: 15px;
}

.confirm-login-btn {
  width: 100%;
  height: 40px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.confirm-login-btn:hover:not(:disabled) {
  background-color: #2563eb;
}

.confirm-login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* 移动端样式调整 */
.account-select-container.mobile .account-select-title {
  font-size: 16px;
}

.account-select-container.mobile .account-select-subtitle {
  font-size: 13px;
}

.account-select-container.mobile .accounts-list {
  gap: 8px;
}

.account-select-container.mobile .account-item {
  padding: 8px;
}

.account-select-container.mobile .account-name {
  font-size: 14px;
}

.account-select-container.mobile .account-user {
  font-size: 13px;
}

.account-select-container.mobile .account-icon {
  width: 36px;
  height: 36px;
  font-size: 14px;
}

/* PC端样式调整 */
:not(.mobile).account-select-container {
  max-width: 320px;
}

:not(.mobile).account-select-container .account-select-title {
  font-size: 18px;
}

:not(.mobile).account-select-container .account-icon {
  width: 44px;
  height: 44px;
}
