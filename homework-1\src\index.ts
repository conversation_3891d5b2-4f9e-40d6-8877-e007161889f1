import Quill from 'quill'
import 'quill/dist/quill.snow.css'
import './style.css'
import { Mention, MentionBlot } from 'quill-mention'
import { fileTypeSVGs, formatTime } from './util'
import { fetchMentionData } from './request'
import { MentionListResponse } from './interface'

Quill.register({ 'blots/mention': MentionBlot, 'modules/mention': Mention })

const mentionDataStore: { [id: string]: any } = {}

const quill = new Quill('#editor', {
  theme: 'snow',
  placeholder: '请输入消息...',
  formats: ['mention'], // 添加"mention"到格式列表
  modules: {
    toolbar: false,
    mention: {
      allowedChars: /^[A-Za-z0-9_@\u4e00-\u9fa5]*$/, // 允许的字符
      mentionDenotationChars: ['@', '#'],
      source: async (
        searchTerm: string,
        renderList: any,
        mentionChar: string,
      ) => {
        if (mentionChar === '@') {
          const results = await fetchMentionData(
            'https://woa.wps.cn/woa/api/v3/search/chats',
            {
              keyword: '',
              page_token: '',
              count: 20,
              search_type: 104,
              search_scopes: [1, 2, 3],
              relation_version: 1,
            },
            true, // 表示使用 qs 序列化（数组格式）
          )
          const mentionList = results.data as MentionListResponse
          const formattedResults = mentionList?.contacts?.list.map(
            (item: any) =>
              (mentionDataStore[item.id] = {
                ...item,
                id: item.id,
                value: item.name,
                avatar: item.avatars?.[0] || '',
                department_show: String(item.department_show || '未知部门'),
                mentionChar,
              }),
          )
          renderList(formattedResults || [], searchTerm)
        }
        if (mentionChar === '#') {
          const results = await fetchMentionData(
            'https://woa.wps.cn/woa/api/v1/roaming',
            {
              offset: 0,
              count: 20,
              without_sid: true,
            },
          )
          const mentionList = results.data as any
          const formattedResults = mentionList.map((item: any) => ({
            ...item,
            id: item.fileid,
            value: item.name,
            mentionChar,
          }))
          renderList(formattedResults || [], searchTerm)
        }
      },
      renderItem: (item: any) => {
        if (item.mentionChar === '@') {
          const div: HTMLDivElement = document.createElement('div')
          div.className = 'ql-mention-list-item'
          const avatarDiv: HTMLDivElement = document.createElement('div')
          avatarDiv.className = 'avatar'
          const avatar: HTMLImageElement = document.createElement('img')
          avatar.src = item.avatar
          avatar.alt = item.name
          avatarDiv.appendChild(avatar)
          const contactInfo: HTMLDivElement = document.createElement('div')
          contactInfo.className = 'contact-info"'
          const name: HTMLSpanElement = document.createElement('span')
          name.className = 'contact-name'
          name.textContent = item.value
          const department: HTMLSpanElement = document.createElement('span')
          department.className = 'contact-department'
          department.textContent = item.department_show
          department.title = item.department_show
          contactInfo.appendChild(name)
          contactInfo.appendChild(department)

          div.appendChild(avatarDiv)
          div.appendChild(contactInfo)
          return div // 返回渲染的项
        } else if (item.mentionChar === '#') {
          const div = document.createElement('div')
          div.className = 'ql-mention-list-item'

          // 左侧图标容器
          const iconDiv = document.createElement('div')
          iconDiv.className = 'icon'
          const iconSvg = fileTypeSVGs[item?.app_type] || fileTypeSVGs['ext']
          const icon = document.createElement('div')
          icon.className = 'doc-icon'
          icon.innerHTML = iconSvg
          iconDiv.appendChild(icon)
          // 右侧文档信息容器
          const info = document.createElement('div')
          info.className = 'doc-info'
          const name = document.createElement('span')
          name.className = 'doc-name'
          name.textContent = item.value
          // 添加 title 属性用于悬停显示完整名称
          name.title = item.value
          const meta = document.createElement('span')
          meta.className = 'doc-meta'
          meta.textContent = `${formatTime(item.ctime)} | ${item.file_src || '未知来源'}`
          meta.title = `${formatTime(item.ctime)} | ${item.file_src || '未知来源'}`
          info.appendChild(name)
          info.appendChild(meta)
          div.appendChild(iconDiv)
          div.appendChild(info)

          return div
        }
      },
      onSelect: async (item: any, insertItem: any) => {
        if (item.denotationChar === '@') {
          const cantactItem = mentionDataStore[item.id]
          item.avatar = cantactItem.avatars?.[0] || ''
          item.department_sho = String(
            cantactItem.department_show || '未知部门',
          )
          insertItem(item)
        }
        if (item.denotationChar === '#') {
          const results = await fetchMentionData(
            'https://woa.wps.cn/woa/api/v1/chats/47935857/file/permission',
            {
              fileid: item.id,
              chatid: 47935857,
              cid: 47935857,
            },
          )
          item.link = results.data?.successes?.link_url
          const dotIndex = item.value.lastIndexOf('.')
          if (dotIndex > 0) {
            item.value = item.value.substring(0, dotIndex)
          }
          insertItem(item)
        } else {
          insertItem(item)
        }
      },
    },
  },
})

window.addEventListener(
  'mention-clicked',
  (event: any) => {
    const value = event.value
    if (value?.denotationChar === '@') {
      const targetElement = event?.event?.target as HTMLElement
      // 获取该元素的位置
      const rect = targetElement.getBoundingClientRect()
      const x = rect.right // 元素的右边缘位置
      const y = rect.bottom // 元素的底部边缘位置
      // 显示 mention 卡片，传递计算后的坐标信息
      showMentionCard({
        id: value.id,
        name: value.value,
        avatar: value.avatar || '',
        x: x,
        y: y,
      })
    } else if (value?.denotationChar === '#') {
      // 跳转到文档链接
      const docUrl = value.link
      if (docUrl) {
        window.open(docUrl, '_blank')
      } else {
        alert('暂无文档链接')
      }
    }
  },
  false,
)

function showMentionCard(user: {
  id: string
  name: string
  avatar: string
  x: number
  y: number
}) {
  const card = document.getElementById('mention-card')!
  const avatar = card.querySelector('.mention-avatar') as HTMLImageElement
  const name = card.querySelector('.mention-name')!
  const id = card.querySelector('.mention-id')!

  avatar.src = user.avatar
  name.textContent = user.name
  id.textContent = `ID: ${user.id}`

  card.style.left = `${user.x}px`
  card.style.top = `${user.y}px`
  card.style.display = 'flex'
}

// 点击空白区域关闭弹窗  修复了mention点击事件冒泡导致的弹窗不显示问题
document.addEventListener('click', (e) => {
  const mentionCard = document.getElementById('mention-card')
  // 忽略来自mention元素的点击
  if ((e.target as HTMLElement).closest('.mention')) {
    return // 如果点击的是mention元素，不隐藏弹窗
  }
  const target = e.target as Node
  if (mentionCard && !mentionCard.contains(target)) {
    mentionCard.style.display = 'none'
  }
})
