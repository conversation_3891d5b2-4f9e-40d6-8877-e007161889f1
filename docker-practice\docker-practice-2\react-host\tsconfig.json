{
  "compilerOptions": {
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "erasableSyntaxOnly": true,
    "noUncheckedSideEffectImports": true,
    "allowUnusedLabels": true, // 对于未使用的标签
    "noUnusedLocals": false,   // 禁用未使用局部变量的错误报告
    "noUnusedParameters": false // 禁用未使用参数的错误报告
  },
   "include": ["src/**/*"]
}
