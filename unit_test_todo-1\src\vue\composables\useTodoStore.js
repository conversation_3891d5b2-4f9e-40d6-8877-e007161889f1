import { ref, computed } from 'vue'

export function useTodoStore() {
  const todos = ref([])
  
  const addTodo = (text) => {
    if (!text.trim()) return false
    const newTodo = {
      id: Date.now(),
      text: text.trim(),
      completed: false,
      createdAt: new Date()
    }
    todos.value.push(newTodo)
    return true
  }
  
  const toggleTodo = (id) => {
    const todo = todos.value.find(todo => todo.id === id)
    if (todo) {
      todo.completed = !todo.completed
      return true
    }
    return false
  }
  
  const deleteTodo = (id) => {
    const initialLength = todos.value.length
    todos.value = todos.value.filter(todo => todo.id !== id)
    return todos.value.length !== initialLength
  }
  
  const clearCompleted = () => {
    const completedCount = todos.value.filter(todo => todo.completed).length
    todos.value = todos.value.filter(todo => !todo.completed)
    return completedCount
  }
  
  const updateTodo = (id, updates) => {
    const todo = todos.value.find(todo => todo.id === id)
    if (todo) {
      Object.assign(todo, updates)
      return true
    }
    return false
  }
  
  // Computed properties
  const uncompletedCount = computed(() => {
    return todos.value.filter(todo => !todo.completed).length
  })
  
  const completedCount = computed(() => {
    return todos.value.filter(todo => todo.completed).length
  })
  
  const totalCount = computed(() => todos.value.length)
  
  const filteredTodos = computed(() => {
    return (filter) => {
      switch (filter) {
        case 'active':
          return todos.value.filter(todo => !todo.completed)
        case 'completed':
          return todos.value.filter(todo => todo.completed)
        default:
          return todos.value
      }
    }
  })
  
  return {
    todos,
    addTodo,
    toggleTodo,
    deleteTodo,
    clearCompleted,
    updateTodo,
    uncompletedCount,
    completedCount,
    totalCount,
    filteredTodos
  }
} 