import React, { useState, useEffect, forwardRef } from "react";
import CustomIcons from "../icons/SvgIcons";
import * as Popover from "@radix-ui/react-popover";
import * as Separator from "@radix-ui/react-separator";
import { useTextFormatting } from "../../hooks/useTextFormatting";
import ColorPicker from "../ui/ColorPicker";
import { FontBoldIcon, FontItalicIcon } from "@radix-ui/react-icons";

interface MenuBarProps {
  onFormatText?: (format: string, value?: any) => void;
}

const MenuBar = forwardRef<HTMLDivElement, MenuBarProps>(
  ({ onFormatText = () => {} }, ref) => {
    const { selectedNode } = useTextFormatting();
    const [activeFormats, setActiveFormats] = useState<string[]>([]);

    // 组件挂载时根据当前节点状态设置活跃格式
    useEffect(() => {
      const formats = [];
      if (selectedNode?.isBold) formats.push("bold");
      if (selectedNode?.isItalic) formats.push("italic");
      setActiveFormats(formats);
    }, [selectedNode]);

    const toggleFormat = (format: string, value?: any) => {
      // 确保直接调用传入的onFormatText回调
      if (onFormatText) {
        onFormatText(format, value);
      }

      if (format !== "fontColor") {
        setActiveFormats((prev) =>
          prev.includes(format)
            ? prev.filter((f) => f !== format)
            : [...prev, format]
        );
      }
    };

    const formatButtons = [
      { id: "fontType", type: "popover", icon: "font-type", label: "字体类型" },
      { id: "bold", type: "button", icon: "bold", label: "粗体" },
      { id: "italic", type: "button", icon: "italic", label: "斜体" },
      { id: "underline", type: "button", icon: "underline", label: "下划线" },
      {
        id: "strikethrough",
        type: "button",
        icon: "strikethrough",
        label: "删除线",
      },
      {
        id: "fontColor",
        type: "popover",
        icon: "font-color",
        label: "字体颜色",
      },
      { id: "formula", type: "button", icon: "formula", label: "公式" },
      {
        id: "bulletList",
        type: "button",
        icon: "bullet-list",
        label: "无序列表",
      },
      {
        id: "numberedList",
        type: "button",
        icon: "numbered-list",
        label: "有序列表",
      },
    ];

    return (
      <div
        ref={ref}
        className="bg-white rounded-lg shadow-lg p-2 flex items-center space-x-1 relative mindmap-menubar"
        style={{
          position: "absolute",
          top: "100%",
          left: "50%",
          transform: "translateX(-50%)",
          marginTop: "8px",
          zIndex: 9999,
        }}
      >
        {formatButtons.map((button, index) => {
          // 根据实际状态判断按钮是否激活
          const isActive =
            activeFormats.includes(button.id) ||
            (button.id === "bold" && selectedNode?.isBold) ||
            (button.id === "italic" && selectedNode?.isItalic);
          const showSeparator = index === 4 || index === 5; // 在删除线后和字体颜色后添加分隔符

          return (
            <React.Fragment key={button.id}>
              {button.type === "popover" && button.id === "fontColor" ? (
                // 直接使用ColorPicker组件
                <ColorPicker
                  selectedColor={selectedNode?.fontColor || "#000000"}
                  onColorChange={(color) => {
                    toggleFormat("fontColor", color);
                  }}
                />
              ) : button.type === "popover" ? (
                <Popover.Root>
                  <Popover.Trigger asChild>
                    <button
                      className={`h-8 w-8 p-0 rounded flex items-center justify-center transition-colors ${
                        isActive
                          ? "bg-blue-100 text-blue-700 hover:bg-blue-200"
                          : "hover:bg-gray-100"
                      }`}
                      title={button.label}
                      onClick={(e) => e.stopPropagation()}
                    >
                      {button.icon ? (
                        <CustomIcons type={button.icon} className="w-3 h-3" />
                      ) : (
                        <span className="w-3 h-3" />
                      )}
                    </button>
                  </Popover.Trigger>
                  <Popover.Portal>
                    <Popover.Content className="bg-white rounded-lg shadow-lg p-2 z-50">
                      {/* 其他弹出菜单内容留空，只保留字体颜色的功能 */}
                      <Popover.Arrow className="fill-white" />
                    </Popover.Content>
                  </Popover.Portal>
                </Popover.Root>
              ) : (
                <button
                  className={`h-8 w-8 p-0 rounded flex items-center justify-center transition-colors ${
                    isActive
                      ? "bg-blue-100 text-blue-700 hover:bg-blue-200"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFormat(button.id);
                  }}
                  title={button.label}
                >
                  {button.icon ? (
                    button.id === "bold" ? (
                      <FontBoldIcon className="w-4 h-4" />
                    ) : button.id === "italic" ? (
                      <FontItalicIcon className="w-4 h-4" />
                    ) : (
                      <CustomIcons type={button.icon} className="w-3 h-3" />
                    )
                  ) : button.id === "underline" ? (
                    <span className="text-sm font-bold relative">
                      U
                      <span className="absolute bottom-0 left-0 right-0 h-0.5 bg-current"></span>
                    </span>
                  ) : button.id === "strikethrough" ? (
                    <span className="text-sm font-bold relative">
                      S
                      <span className="absolute top-1/2 left-0 right-0 h-0.5 bg-current transform -translate-y-1/2"></span>
                    </span>
                  ) : button.id === "formula" ? (
                    <span className="text-sm font-bold">∑</span>
                  ) : button.id === "bulletList" ? (
                    <span className="flex flex-col items-start text-xs leading-none">
                      <span>•</span>
                      <span>•</span>
                    </span>
                  ) : button.id === "numberedList" ? (
                    <span className="flex flex-col items-start text-xs leading-none">
                      <span>1.</span>
                      <span>2.</span>
                    </span>
                  ) : (
                    <span className="w-3 h-3" />
                  )}
                </button>
              )}

              {showSeparator && (
                <Separator.Root
                  className="bg-gray-300 w-px h-6"
                  orientation="vertical"
                />
              )}
            </React.Fragment>
          );
        })}
      </div>
    );
  }
);

export default MenuBar;
