import { useState } from 'react'
import reactLogo from './assets/react.svg'
import "./App.css";

function App() {
  const [count, setCount] = useState(0);
  const handleFetch = async () => {
    try {
      const response = await fetch("/api/v1/data");
      const data = await response.json();
      console.log("GET /api/v1/data response:", data);
    } catch (error) {
      console.error("Error fetching /api/v1/data:", error);
    }
  };
  return (
    <>
      <h3>微服务子应用React-sub</h3>
      <div>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>React</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <button onClick={handleFetch}>调用 /api/v1/data 接口</button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>
    </>
  );
}

export default App;
