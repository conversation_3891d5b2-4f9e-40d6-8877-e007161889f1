import type { OrderState } from './types'
import { PendingPaymentState } from './types'

export class OrderContext {
  private state: OrderState

  constructor() {
    this.state = new PendingPaymentState(this)
    // this 在构造函数中总是指向当前实例。
    // 在状态模式中，将 this 传给状态对象，是为了让状态能回调到上下文（OrderContext），实现状态切换和数据共享。
    // 在状态模式中，状态对象需要访问上下文（OrderContext）的某些方法，如 setState 和 getCurrentState。
  }

  setState(state: OrderState): void {
    console.log(`状态变更: ${this.state.constructor.name} -> ${state.constructor.name}`)
    this.state = state
  }

  getCurrentState(): string {
    return this.state.constructor.name.replace('State', '')
  }

  cancelOrder(): string {
    return this.state.cancelOrder()
  }

  verifyPayment(): string {
    return this.state.verifyPayment()
  }

  shipOrder(): string {
    return this.state.shipOrder()
  }
}
