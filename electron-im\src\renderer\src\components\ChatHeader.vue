<!-- 聊天头部组件 -->
<template>
  <div class="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
    <div class="flex items-center gap-3">
      <UserAvatar
        :name="currentContact?.name || '用户'"
        size="medium"
      />
      <div>
        <h3 class="font-medium text-base text-gray-900 m-0">
          {{ currentContact?.name || '选择聊天' }}
        </h3>
        <p class="text-xs text-gray-500 m-0">{{ currentContact?.status || '离线' }}</p>
      </div>
    </div>
    <div class="flex items-center gap-2">
      <button
        class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-sm hover:bg-gray-100"
        @click="$emit('more-options')"
      >
        ⋯
      </button>
      <button
        class="bg-transparent border-none cursor-pointer p-2 rounded text-red-600 text-sm hover:bg-red-50"
        @click="$emit('logout')"
      >
        退出登录
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import UserAvatar from './UserAvatar.vue'
import type { User } from '../api'

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
}

interface Props {
  currentContact: Contact | null
}

defineProps<Props>()

defineEmits<{
  'more-options': []
  logout: []
}>()
</script>
