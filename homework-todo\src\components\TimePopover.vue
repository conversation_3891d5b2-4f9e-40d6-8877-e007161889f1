<template>
  <el-popover v-model:visible="visible" placement="bottom-start" :width="400" trigger="manual">
    <template #reference>
      <slot name="trigger">
        <div style="display: none"></div>
      </slot>
    </template>

    <div class="time-popover-content">
      <!-- 使用中文日历组件 -->
      <div class="calendar-section">
        <ChineseCalendar v-model="selectedDate" @change="onDateChange" />
      </div>

      <div class="popover-section">
        <div class="checkbox-row">
          <el-checkbox v-model="localEnableTime" size="small">截止时间</el-checkbox>
        </div>
        <el-time-select
          v-if="localEnableTime"
          v-model="localCustomTime"
          placeholder="选择时间"
          start="00:00"
          step="00:30"
          end="23:30"
          style="width: 100%; margin-top: 8px"
          size="small"
        />
      </div>

      <div class="popover-section">
        <div class="checkbox-row">
          <el-checkbox v-model="localEnableReminder" size="small">设置提醒</el-checkbox>
        </div>

        <!-- 提醒时间输入框 -->
        <div v-if="localEnableReminder" style="margin-top: 8px">
          <el-popover
            v-model:visible="showReminderPopover"
            placement="right-start"
            :width="200"
            trigger="click"
          >
            <template #reference>
              <el-input
                :value="selectedReminderText || '提醒时间'"
                placeholder="提醒时间"
                readonly
                size="small"
                style="cursor: pointer"
                class="reminder-input"
              />
            </template>

            <div class="reminder-popover-content">
              <div class="reminder-option" @click="selectReminderOption('today', '09:00')">
                截止当天 09:00
              </div>
              <div class="reminder-option" @click="selectReminderOption('yesterday', '18:00')">
                截止前一天 18:00
              </div>
              <div class="reminder-option custom" @click="openCustomReminderPopover">自定义</div>
            </div>
          </el-popover>
        </div>
      </div>

      <div class="popover-footer">
        <el-button size="small" @click="handleCancel">取消</el-button>
        <el-button type="primary" size="small" @click="handleConfirm">确认</el-button>
      </div>
    </div>
  </el-popover>

  <!-- 自定义提醒时间 popover -->
  <el-popover
    v-model:visible="showCustomReminderPopover"
    placement="bottom"
    :width="250"
    trigger="manual"
  >
    <div class="custom-reminder-content">
      <div class="custom-section">
        <label class="custom-label">日期选择：</label>
        <el-select v-model="customReminderDateOption" size="small" style="width: 100%">
          <el-option label="截至当天" value="today" />
          <el-option label="截至前一天" value="yesterday" />
        </el-select>
      </div>

      <div class="custom-section">
        <label class="custom-label">时间选择：</label>
        <el-select v-model="customReminderTimeOption" size="small" style="width: 100%">
          <el-option v-for="time in timeOptions" :key="time" :label="time" :value="time" />
        </el-select>
      </div>

      <div class="custom-footer">
        <el-button size="small" @click="closeCustomReminderPopover">取消</el-button>
        <el-button type="primary" size="small" @click="confirmCustomReminder">确认</el-button>
      </div>
    </div>
  </el-popover>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import ChineseCalendar from './ChineseCalendar.vue'

interface Props {
  modelValue?: boolean
  customDate?: string
  customTime?: string
  enableTime?: boolean
  enableReminder?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (
    e: 'confirm',
    data: {
      date: string
      time: string
      enableTime: boolean
      enableReminder: boolean
      reminderText: string
    },
  ): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  customDate: '',
  customTime: '00:00',
  enableTime: false,
  enableReminder: false,
})

const emit = defineEmits<Emits>()

// 本地状态
const visible = ref(false)
const localCustomDate = ref('')
const localCustomTime = ref('00:00')
const localEnableTime = ref(false)
const localEnableReminder = ref(false)

// 日历相关状态
const selectedDate = ref(new Date())

// 提醒相关状态
const showReminderPopover = ref(false)
const showCustomReminderPopover = ref(false)
const selectedReminderText = ref('')
const customReminderDateOption = ref('today')
const customReminderTimeOption = ref('00:00')

// 生成时间选项（每隔半小时）
const timeOptions = computed(() => {
  const options = []
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      options.push(timeStr)
    }
  }
  return options
})

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal) {
      // 初始化本地状态
      if (props.customDate) {
        selectedDate.value = new Date(props.customDate)
        localCustomDate.value = props.customDate
      }
      localCustomTime.value = props.customTime
      localEnableTime.value = props.enableTime
      localEnableReminder.value = props.enableReminder
      selectedReminderText.value = ''
    }
  },
)

// 监听 visible 变化，同步到父组件
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 监听选中日期变化
watch(selectedDate, (newDate) => {
  if (newDate) {
    localCustomDate.value = newDate.toISOString().split('T')[0]
  }
})

// 日历日期变化处理
function onDateChange(date: Date) {
  selectedDate.value = date
}

// 提醒相关方法
function selectReminderOption(dateOption: 'today' | 'yesterday', time: string) {
  const dateText = dateOption === 'today' ? '当天' : '前一天'
  selectedReminderText.value = `截止${dateText} ${time}`
  showReminderPopover.value = false
}

function openCustomReminderPopover() {
  showReminderPopover.value = false
  showCustomReminderPopover.value = true
}

function closeCustomReminderPopover() {
  showCustomReminderPopover.value = false
}

function confirmCustomReminder() {
  const dateText = customReminderDateOption.value === 'today' ? '当天' : '前一天'
  selectedReminderText.value = `截止${dateText} ${customReminderTimeOption.value}`
  showCustomReminderPopover.value = false
}

function handleCancel() {
  visible.value = false
  // 重置状态
  selectedDate.value = new Date()
  localCustomDate.value = ''
  localCustomTime.value = '00:00'
  localEnableTime.value = false
  localEnableReminder.value = false
  selectedReminderText.value = ''
  emit('cancel')
}

function handleConfirm() {
  emit('confirm', {
    date: localCustomDate.value,
    time: localCustomTime.value,
    enableTime: localEnableTime.value,
    enableReminder: localEnableReminder.value,
    reminderText: selectedReminderText.value,
  })
  visible.value = false
}
</script>

<style scoped>
.option-btn {
  padding: 6px 12px;
  border: 1px solid #e1e5e9;
  background: white;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.option-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.option-btn svg {
  vertical-align: middle;
  flex-shrink: 0;
}

/* Popover 内容样式 */
.time-popover-content {
  padding: 4px;
}

/* 日历样式 */
.calendar-section {
  margin-bottom: 16px;
}

.popover-section {
  margin-bottom: 16px;
}

.popover-section:last-child {
  margin-bottom: 0;
}

.checkbox-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

/* 提醒输入框样式 */
.reminder-input {
  cursor: pointer;
}

.reminder-input :deep(.el-input__inner) {
  cursor: pointer;
  background-color: #f8f9fa;
}

.reminder-input :deep(.el-input__inner):hover {
  border-color: #409eff;
}

.popover-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid #e1e5e9;
}

/* 提醒选项样式 */
.reminder-popover-content {
  padding: 4px;
}

.reminder-option {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s;
  margin-bottom: 4px;
}

.reminder-option:hover {
  background: #f0f8ff;
  color: #409eff;
}

.reminder-option.custom {
  background: #409eff;
  color: white;
}

.reminder-option.custom:hover {
  background: #337ecc;
}

/* 自定义提醒时间样式 */
.custom-reminder-content {
  padding: 12px;
}

.custom-section {
  margin-bottom: 12px;
}

.custom-label {
  display: block;
  font-size: 13px;
  color: #333;
  margin-bottom: 6px;
}

.custom-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 8px;
  border-top: 1px solid #e1e5e9;
}
</style>
