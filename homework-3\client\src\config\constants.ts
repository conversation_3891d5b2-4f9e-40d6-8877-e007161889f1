// 布局配置
export const LAYOUT_CONFIG = {
  // 画布中心点
  CENTER_X: 400,
  CENTER_Y: 300,

  // 节点尺寸配置
  NODE_DIMENSIONS: {
    LEVEL_1: { width: 120, height: 50, rx: 25 },
    LEVEL_2: { width: 100, height: 40, rx: 20 },
    LEVEL_3: { width: 80, height: 35, rx: 8 },
  },

  // 间距配置
  SPACING: {
    VERTICAL_BASE: 80,
    HORIZONTAL_PADDING: 130,
    NODE_MARGIN: 20,
    BUTTON_OFFSET: 15,
  },

  // 层级限制
  MAX_LEVEL: 3,

  // 自动布局参数
  AUTO_LAYOUT: {
    LEVEL_2_SPACING: 80,
    LEVEL_3_SPACING: 60,
    MIN_VERTICAL_GAP: 10,
  },
} as const;

// 样式配置
export const STYLE_CONFIG = {
  // 节点颜色
  NODE_COLORS: {
    LEVEL_1: {
      fill: "#00a59a",
      textColor: "#ffffff",
    },
    LEVEL_2: {
      fill: "#eeeeee",
      textColor: "#000000",
    },
    LEVEL_3: {
      fill: "#ffffff",
      textColor: "#000000",
    },
  },

  // 字体配置
  FONT_SIZES: {
    LEVEL_1: 18,
    LEVEL_2: 16,
    LEVEL_3: 14,
  },

  // 边框配置
  BORDER: {
    DEFAULT_WIDTH: 0,
    SELECTED_WIDTH: 2,
    SELECTED_COLOR: "#007acc",
  },

  // 连线配置
  EDGE_STYLES: {
    DEFAULT_STROKE: "#666666",
    DEFAULT_WIDTH: 2,
    SELECTED_STROKE: "#007acc",
    SELECTED_WIDTH: 3,
  },
} as const;

// 画布配置
export const CANVAS_CONFIG = {
  // 默认画布尺寸
  DEFAULT_SIZE: {
    width: 800,
    height: 600,
  },

  // 最大画布尺寸
  MIN_SIZE: {
    width: 1000,
    height: 700,
  },

  // 视口配置
  VIEWPORT: {
    DEFAULT_ZOOM: 1,
    MIN_ZOOM: 0.1,
    MAX_ZOOM: 3,
    ZOOM_STEP: 0.1,
    DEFAULT_POSITION: { x: 0, y: 0 },
  },

  // 画布边距
  PADDING: 150,
};

// 交互配置
export const INTERACTION_CONFIG = {
  // 双击延迟
  DOUBLE_CLICK_DELAY: 300,

  // 拖拽阈值
  DRAG_THRESHOLD: 5,

  // 动画持续时间
  ANIMATION_DURATION: 200,

  // 节流延迟
  THROTTLE_DELAY: 16,

  // 防抖延迟
  DEBOUNCE_DELAY: 300,
} as const;

// AI配置
export const AI_CONFIG = {
  // API配置
  API: {
    BASE_URL: "http://localhost:3001",
    ENDPOINTS: {
      GENERATE: "/api/ai/generate-mindmap",
    },
    TIMEOUT: 30000,
  },

  // 生成配置
  GENERATION: {
    MAX_CHILDREN: 8,
    DEFAULT_PROMPT_SUFFIX: "，请生成思维导图的子主题",
  },

  // 进度提示
  PROGRESS_MESSAGES: {
    CONNECTING: "正在连接AI服务...",
    GENERATING: "AI正在生成内容...",
    PROCESSING: "正在处理生成结果...",
    COMPLETE: "生成完成",
    ERROR: "生成失败",
  },
} as const;

// 编辑配置
export const EDIT_CONFIG = {
  // 默认文本
  DEFAULT_TEXTS: {
    ROOT: "中心主题",
    CHILD: "新主题",
    SIBLING: "同级主题",
  },

  // 输入框配置
  INPUT: {
    MAX_LENGTH: 100,
    MIN_LENGTH: 1,
    PLACEHOLDER: "输入内容...",
    ERROR_MESSAGE: "文字大小最多100个字符，最少1个字符",
  },

  // 菜单栏配置
  MENU_BAR: {
    HIDE_DELAY: 100,
    BUTTON_SIZE: 24,
  },
} as const;

// 历史记录配置
export const HISTORY_CONFIG = {
  MAX_HISTORY_SIZE: 50,
  BATCH_DELAY: 500,
} as const;

// 数据属性
export const DATA_ATTRIBUTES = {
  NODE_ID: "data-node-id",
  EDGE_ID: "data-edge-id",
  BUTTON_TYPE: "data-button-type",
} as const;

// 键盘快捷键
export const KEYBOARD_SHORTCUTS = {
  UNDO: "ctrl+z",
  REDO: "ctrl+y",
  DELETE: "Delete",
  ESCAPE: "Escape",
  ENTER: "Enter",
  TAB: "Tab",
} as const;

// 存储配置
export const STORAGE_CONFIG = {
  PERSISTENCE_KEY: "mindmap-store",
  VERSION: 1,
} as const;
