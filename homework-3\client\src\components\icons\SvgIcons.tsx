import React from "react";

export type CustomIconType =
  | "node-style"
  | "shape"
  | "node-bg"
  | "line-type"
  | "line-color"
  | "line-width"
  | "border-width"
  | "border-color"
  | "border-type"
  | "clear-style"
  | "canvas"
  | "style"
  | "structure"
  | "theme-spacing"
  | "theme-width"
  | "help"
  | "fold"
  | "unfold"
  | "font-color"
  | "font-size"
  | "font-type"
  | "bold"
  | "italic"
  | "alignleft"
  | "aligncenter"
  | "alignright"
  | "child-theme"
  | "same-theme"
  | "parent-theme"
  | "summary"
  | "frame"
  | "image"
  | "hyperlink"
  | "watermark"
  | "canvas-color"
  | "canvas-width"
  | "canvas-height"
  | "canvas-background"
  | "undo"
  | "redo"
  | "format"
  | "underline"
  | "strikethrough"
  | "formula"
  | "bullet-list"
  | "numbered-list"
  | "focus"
  | "ai-creation"
  | string;

interface CustomIconProps extends React.SVGProps<SVGSVGElement> {
  type: CustomIconType;
  disabled?: boolean;
}

const CustomIcons: React.FC<CustomIconProps> = ({
  type,
  disabled = false,
  ...props
}) => {
  switch (type) {
    case "node-style":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M80 288c0-44.16 35.84-80 80-80h320a48 48 0 0 1 0 96H176v416H480a48 48 0 0 1 0 96h-320c-44.16 0-80-35.84-80-80v-448z"
            fill="currentColor"
          ></path>
          <path
            d="M448 160A96 96 0 0 1 544 64h256A96 96 0 0 1 896 160v192A96 96 0 0 1 800 448h-256A96 96 0 0 1 448 352v-192z m352 0h-256v192h256v-192zM672 640a128 128 0 1 0 0 256 128 128 0 0 0 0-256zM448 768a224 224 0 1 1 448 0 224 224 0 0 1-448 0z"
            fill="currentColor"
          ></path>
        </svg>
      );
    case "shape":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M841.34 959.36H182.66c-65.06 0-117.99-52.94-117.99-118.02V182.69c0-65.08 52.94-118.04 117.99-118.04h658.68c65.06 0 117.99 52.96 117.99 118.04v658.65c0 65.08-52.93 118.02-117.99 118.02zM182.66 142.17c-22.31 0-40.51 18.18-40.51 40.51v658.65c0 22.34 18.2 40.49 40.51 40.49h658.68c22.31 0 40.51-18.15 40.51-40.49V182.69c0-22.34-18.2-40.51-40.51-40.51H182.66z"
            fill={disabled ? "currentColor" : "#6C6D6E"}
          ></path>
        </svg>
      );
    case "node-bg":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M868.096 412.352a38.4 38.4 0 0 1 4.48 48.96l-4.48 5.312-167.936 167.808-3.84 2.752-4.096 2.368-172.736 68.672a38.4 38.4 0 0 1-36.352-4.352l-4.992-4.16-6.784-6.848-32 32.064a38.4 38.4 0 0 1-32.64 10.88l-5.824-1.344-193.664-59.712a38.4 38.4 0 0 1-19.84-59.264l3.968-4.608 99.008-99.008-2.24-2.24a38.4 38.4 0 0 1-10.432-35.2l1.92-6.208 69.568-173.44 3.2-4.992 3.008-3.584 166.144-166.144a38.4 38.4 0 0 1 48.96-4.48l5.376 4.48 292.224 292.288zM417.088 638.528l-72.448-72.32-53.888 53.888 110.72 34.112 15.616-15.68z m189.952-47.744L397.248 380.928 360.32 473.152l154.24 154.368 92.48-36.736z m179.584-151.36L548.672 201.6 437.568 312.64l237.952 237.952 111.104-111.168zM128 800h768v115.2H128z"
            fill="#505050"
          ></path>
        </svg>
      );
    case "line-type":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M903.606857 727.808c-102.838857-19.748571-175.872-33.718857-219.062857-41.984-64.804571-12.397714-73.874286-52.150857-88.685714-89.380571-14.848-37.193143-17.042286-37.449143-34.852572-87.917715l-82.432-233.618285c-21.430857-58.989714-58.806857-72.411429-116.114285-72.411429H252.854857A93.732571 93.732571 0 0 0 166.838857 146.285714 93.403429 93.403429 0 0 0 73.142857 239.396571a93.403429 93.403429 0 0 0 93.696 93.110858c39.094857 0 72.557714-23.771429 86.601143-57.6h98.925714c24.466286 0 50.066286 0 60.781715 32.512l79.908571 222.061714a1795.364571 1795.364571 0 0 0 44.544 112.164571c23.405714 54.125714 21.796571 82.724571 103.570286 103.094857 54.491429 13.531429 131.949714 28.598857 232.265143 45.129143l30.171428-62.061714zM166.838857 275.602286a36.315429 36.315429 0 0 1-36.425143-36.205715c0-20.004571 16.310857-36.205714 36.425143-36.205714s36.425143 16.201143 36.425143 36.205714c0 20.004571-16.310857 36.205714-36.425143 36.205715z"
            fill="#333333"
          ></path>
          <path
            d="M789.394286 622.409143l142.994285 108.324571a17.554286 17.554286 0 0 1 0 27.977143l-142.994285 108.324572a8.777143 8.777143 0 0 1-14.08-6.948572V629.394286a8.777143 8.777143 0 0 1 14.08-6.985143z"
            fill="#333333"
          ></path>
        </svg>
      );
    case "line-color":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M320 368a48 48 0 0 1 48 48v320c0 26.496 21.504 48 48 48H576a48 48 0 0 1 0 96H416a144 144 0 0 1-144-144v-320A48 48 0 0 1 320 368z"
            fill={disabled ? "currentColor" : "#008A81"}
          ></path>
          <path
            d="M64 160A96 96 0 0 1 160 64h320A96 96 0 0 1 576 160v192A96 96 0 0 1 480 448h-320A96 96 0 0 1 64 352v-192z m416 0h-320v192h320v-192zM576 672A96 96 0 0 1 672 576h256a96 96 0 0 1 96 96v192a96 96 0 0 1-96 96h-256A96 96 0 0 1 576 864v-192z m352 0h-256v192h256v-192z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
        </svg>
      );
    case "line-width":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M320 368c44.16 0 80 35.84 80 80v239.488c0 8.832 7.168 16 16 16H576a80 80 0 1 1 0 160H416a176 176 0 0 1-176-176V448c0-44.16 35.84-80 80-80z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
          <path
            d="M64 160A96 96 0 0 1 160 64h320A96 96 0 0 1 576 160v192A96 96 0 0 1 480 448h-320A96 96 0 0 1 64 352v-192z m416 0h-320v192h320v-192zM576 672A96 96 0 0 1 672 576h256a96 96 0 0 1 96 96v192a96 96 0 0 1-96 96h-256A96 96 0 0 1 576 864v-192z m352 0h-256v192h256v-192z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
        </svg>
      );
    case "border-width":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M9.78 92.908v833.607h1003.806V92.908H9.779z m937.81 767.62H79.236V158.895H947.59V860.53z"
            fill={disabled ? "currentColor" : "#040000"}
          ></path>
          <path
            d="M133.161 191.898v628.684h757.043V191.898H133.161z m707.267 578.918h-654.88V241.664h654.88v529.152z"
            fill={disabled ? "currentColor" : "#040000"}
          ></path>
        </svg>
      );
    case "border-color":
      return (
        <svg viewBox="0 0 1170 1024" {...props}>
          <path
            d="M104.594286 95.232A29.257143 29.257143 0 0 1 131.803429 68.022857h906.971428a29.257143 29.257143 0 0 1 27.209143 27.209143v680.228571h54.418286v-680.228571A86.089143 86.089143 0 0 0 1033.947429 9.069714H131.803429A89.161143 89.161143 0 0 0 45.641143 95.232v680.228571h54.418286v-680.228571z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
          <path
            d="M1133.714286 784.310857h-81.627429V95.085714a19.529143 19.529143 0 0 0-18.139428-18.139428H131.657143a19.529143 19.529143 0 0 0-18.139429 18.139428v689.078857H36.571429V95.085714A95.744 95.744 0 0 1 131.657143 0h906.971428a95.744 95.744 0 0 1 95.085715 95.085714z m-54.418286-18.139428h36.571429V95.085714a71.606857 71.606857 0 0 0-72.557715-72.557714H131.657143a71.606857 71.606857 0 0 0-72.411429 72.557714v671.012572h36.571429V95.085714a40.667429 40.667429 0 0 1 40.813714-40.813714h906.971429a40.667429 40.667429 0 0 1 40.813714 40.813714v671.012572z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
          <path
            d="M897.901714 149.577143H272.310857a86.089143 86.089143 0 0 0-86.162286 86.162286v539.501714h54.418286V235.739429a29.257143 29.257143 0 0 1 27.209143-27.209143h625.664a29.257143 29.257143 0 0 1 27.209143 27.209143v539.501714h54.418286V235.739429a76.214857 76.214857 0 0 0-77.165715-86.162286z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
          <path
            d="M993.133714 784.164571h-81.627428v-548.571428a19.529143 19.529143 0 0 0-18.139429-18.139429H272.164571a19.529143 19.529143 0 0 0-18.139428 18.139429v548.571428H177.078857v-548.571428a98.889143 98.889143 0 0 1 95.085714-95.085714h625.664a98.889143 98.889143 0 0 1 95.085715 95.085714z m-54.418285-18.139428h36.571428V235.593143a71.606857 71.606857 0 0 0-72.557714-72.557714H272.164571a71.606857 71.606857 0 0 0-72.411428 72.557714v525.897143h36.571428v-525.897143a40.667429 40.667429 0 0 1 40.813715-40.813714h625.664a40.667429 40.667429 0 0 1 40.813714 40.813714v530.432z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
          <path
            d="M1144.905143 682.788571a24.795429 24.795429 0 0 1 25.380571 24.064v292.937143a24.795429 24.795429 0 0 1-25.380571 24.137143H25.380571A24.722286 24.722286 0 0 1 0 999.936V706.925714a23.259429 23.259429 0 0 1 7.314286-17.042285 26.258286 26.258286 0 0 1 17.993143-7.314286z"
            fill={disabled ? "currentColor" : "#0096FF"}
          ></path>
        </svg>
      );
    case "border-type":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M64 704h64v128l128-0.064V896H128a64 64 0 0 1-64-64v-128z m384 127.936V896H320v-64.064h128z m192 0V896H512v-64.064h128zM896 704v128a64 64 0 0 1-64 64h-128v-64.064L832 832v-128h64z m0-64h-64V512h64v128zM64 512h64v128H64V512z m832-64h-64V320h64v128zM64 320l64-0.064V448H64V320z m768-256a64 64 0 0 1 64 64v128h-64V128l-128-0.064V64h128z m-192 0v63.936H512V64h128zM448 64v63.936H320V64h128z m-192 63.936L128 128v127.936L64 256V128a64 64 0 0 1 64-64h128v63.936z"
            fill={disabled ? "currentColor" : "#3D4757"}
          ></path>
        </svg>
      );
    case "clear-style":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M380.544 372.664L156.992 596.208l249.952 265.464h29.72v-0.152h37.52L667.92 660.128 380.544 372.664z m63.84-63.92L731.76 596.208l127.752-127.752-287.384-287.464-127.76 127.76z m157.472 552.776h316.176v90.392H511.536l-149.68 0.08L61.2 628.096c-0.52-0.528-1.048-1-1.544-1.552a46.8 46.8 0 0 1-1.464-1.696c-0.472-0.568-0.88-1.176-1.312-1.776-0.44-0.592-0.904-1.16-1.312-1.776-0.416-0.608-0.784-1.296-1.16-1.92a45.344 45.344 0 0 1-1.08-1.936c-0.352-0.656-0.688-1.264-1.008-1.928-0.32-0.672-0.64-1.328-0.92-2.008-0.288-0.68-0.528-1.392-0.776-2.08a44.816 44.816 0 0 1-0.696-2.088c-0.216-0.704-0.44-1.44-0.616-2.16-0.176-0.72-0.32-1.44-0.464-2.16a44.536 44.536 0 0 1-0.384-2.16c-0.112-0.736-0.232-1.512-0.232-2.24 0-0.736-0.16-1.424-0.16-2.16 0-0.744-0.072-1.504-0.072-2.24 0-0.744 0.08-1.504 0.08-2.24 0-0.744 0.152-1.432 0.152-2.16 0-0.736 0.12-1.52 0.232-2.24 0.104-0.736 0.24-1.44 0.384-2.16 0.144-0.728 0.288-1.448 0.464-2.16a44.472 44.472 0 0 1 1.312-4.24c0.248-0.704 0.488-1.416 0.776-2.096 0.28-0.68 0.608-1.336 0.92-2a46.096 46.096 0 0 1 3.248-5.792c0.4-0.616 0.872-1.184 1.312-1.776 0.44-0.6 0.84-1.2 1.312-1.776 0.464-0.576 0.968-1.152 1.464-1.704 0.496-0.544 1.024-1.016 1.544-1.544L540.176 85.2a45.168 45.168 0 0 1 63.84 0l351.296 351.304a45.16 45.16 0 0 1 0 63.84L601.856 861.52z"
            fill="currentColor"
          ></path>
        </svg>
      );
    case "canvas-color":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M211.2 448h537.6l-243.2 281.6h-64c-64-44.8-96-64-96-64s-57.6-64-179.2-185.6l44.8-32z"
            fill={disabled ? "currentColor" : "#909AA9"}
          ></path>
          <path
            d="M864 736c38.4 0 70.4-32 70.4-70.4 0-25.6-25.6-70.4-70.4-134.4-44.8 64-70.4 108.8-70.4 134.4 0 38.4 32 70.4 70.4 70.4z"
            fill={disabled ? "currentColor" : "#3D4757"}
          ></path>
          <path
            d="M544 134.4L185.6 480l268.8 243.2 332.8-352-243.2-236.8z m44.8-32l236.8 230.4c25.6 25.6 25.6 64 0 89.6l-326.4 339.2c-25.6 25.6-64 25.6-89.6 0L160 531.2c-25.6-25.6-32-64-6.4-89.6l339.2-332.8c32-32 70.4-32 96-6.4z"
            fill={disabled ? "currentColor" : "#3D4757"}
          ></path>
          <path
            d="M128 256h384v64H128z"
            fill={disabled ? "currentColor" : "#3D4757"}
          ></path>
        </svg>
      );
    case "style":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M853.973333 143.274667H657.066667a36.266667 36.266667 0 0 0-35.328 27.989333 112.682667 112.682667 0 0 1-219.306667 0 36.266667 36.266667 0 0 0-35.498667-27.989333H169.984a42.026667 42.026667 0 0 0-42.026667 42.026666v240.725334c0 23.210667 18.816 42.026667 42.026667 42.026666h71.637333v370.688c0 23.210667 18.816 42.026667 42.026667 42.026667h456.533333a42.026667 42.026667 0 0 0 42.026667-42.026667v-370.645333h71.637333a42.026667 42.026667 0 0 0 42.026667-42.026667V185.344a42.026667 42.026667 0 0 0-41.898667-42.069333z m-26.752 256h-113.664v412.672H310.442667V399.274667H196.778667V212.053333h146.432a181.589333 181.589333 0 0 0 337.578666 0h146.432v187.221334z"
            fill="currentColor"
          ></path>
        </svg>
      );
    case "structure":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M298.666667 384m68.266666 0l290.133334 0q68.266667 0 68.266666 68.266667l0 119.466666q0 68.266667-68.266666 68.266667l-290.133334 0q-68.266667 0-68.266666-68.266667l0-119.466666q0-68.266667 68.266666-68.266667Z"
            fill={disabled ? "currentColor" : "#FFFFFF"}
          ></path>
          <path
            d="M413.098667 640l-2.005334 3.498667-5.162666 9.173333-5.546667 9.813333c-53.333333 92.586667-101.76 128.682667-217.514667 130.986667L170.666667 793.6l-4.138667-0.341333-3.541333-0.853334A42.624 42.624 0 0 1 85.333333 768a42.666667 42.666667 0 0 1 77.653334-24.405333A25.6 25.6 0 0 1 170.666667 742.4c100.010667 0 136.277333-22.741333 180.693333-97.493333l3.456-5.973334A68.352 68.352 0 0 1 298.666667 571.733333v-119.466666a68.266667 68.266667 0 0 1 55.978666-67.157334l0.170667-0.042666-3.456-5.973334C306.944 304.341333 270.72 281.6 170.666667 281.6a24.832 24.832 0 0 1-7.68-1.194667A42.624 42.624 0 0 1 85.333333 256a42.666667 42.666667 0 0 1 77.653334-24.405333A25.6 25.6 0 0 1 170.666667 230.4c124.586667 0 174.506667 35.370667 229.717333 131.157333l5.546667 9.813334 5.12 9.130666 2.090666 3.498667h197.717334l1.024-1.706667 1.024-1.792 5.162666-9.173333C675.584 268.16 724.565333 230.4 853.333333 230.4a25.6 25.6 0 0 1 7.68 1.194667 42.666667 42.666667 0 1 1-0.042666 48.810666A24.832 24.832 0 0 1 853.333333 281.6c-102.613333 0-138.112 23.893333-184.149333 103.424l-5.12-0.682667A68.266667 68.266667 0 0 1 725.333333 452.266667v119.466666a68.266667 68.266667 0 0 1-55.978666 67.157334h-0.213334l3.498667 5.973333C717.056 719.701333 753.28 742.4 853.333333 742.4a25.6 25.6 0 0 1 7.68 1.194667 42.666667 42.666667 0 1 1-0.042666 48.810666l-3.498667 0.853334L853.333333 793.6c-124.586667 0-174.506667-35.370667-229.717333-131.157333l-5.546667-9.813334-5.12-9.130666-2.090666-3.498667H413.098667z m243.968-204.8H366.933333a17.066667 17.066667 0 0 0-16.725333 13.653333l-0.341333 3.413334v119.466666a17.066667 17.066667 0 0 0 13.653333 16.725334l3.413333 0.341333h290.133334a17.066667 17.066667 0 0 0 16.725333-13.653333l0.341333-3.413334v-119.466666a17.066667 17.066667 0 0 0-13.653333-16.725334l-3.413333-0.341333z"
            fill={disabled ? "currentColor" : "#262626"}
          ></path>
        </svg>
      );
    case "theme-spacing":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M240 64a48 48 0 0 1 34.368 14.528l128 131.584A48 48 0 0 1 333.632 277.12l-44.928-46.208-0.512 543.616 45.44-46.72a48 48 0 0 1 68.864 66.944l-128 131.584a48 48 0 0 1-68.864 0l-128-131.584a48 48 0 0 1 68.864-66.944l45.696 46.976 0.512-545.216-46.208 47.552a48 48 0 1 1-68.864-66.944l128-131.584A48 48 0 0 1 240 64z"
            fill="#1F69E0"
          ></path>
          <path
            d="M512.256 848a48 48 0 0 1 48-48h352a48 48 0 0 1 0 96h-352a48 48 0 0 1-48-48zM512.256 496a48 48 0 0 1 48-48h352a48 48 0 0 1 0 96h-352a48 48 0 0 1-48-48zM512 144a48 48 0 0 1 48-48h352.256a48 48 0 0 1 0 96H560A48 48 0 0 1 512 144z"
            fill="#333333"
          ></path>
        </svg>
      );
    case "theme-width":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M272 160a48 48 0 0 0-48 48V512H128V208A144 144 0 0 1 272 64h480A144 144 0 0 1 896 208V512h-96V208a48 48 0 0 0-48-48h-480z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
          <path
            d="M273.92 622.08a48 48 0 0 1 0 67.84l-46.08 46.08h568.32l-46.08-46.08a48 48 0 1 1 67.84-67.84l128 128a48 48 0 0 1 0 67.84l-128 128a48 48 0 1 1-67.84-67.84l46.08-46.08H227.84l46.08 46.08a48 48 0 1 1-67.84 67.84l-128-128a48 48 0 0 1 0-67.84l128-128a48 48 0 0 1 67.84 0z"
            fill={disabled ? "currentColor" : "#1F69E0"}
          ></path>
        </svg>
      );

    case "help":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M514.5 960.2C268.3 960.2 68 759.9 68 513.7S268.3 67.2 514.5 67.2c97.5 0 190.1 30.8 267.8 89.2 15.2 11.4 18.2 32.9 6.8 48.1-11.4 15.2-32.9 18.2-48.1 6.9-65.7-49.4-144-75.5-226.5-75.5-208.3 0-377.8 169.5-377.8 377.8s169.5 377.8 377.8 377.8S892.3 722 892.3 513.7c0-48.7-9.1-96-27.1-140.8-7.1-17.6 1.5-37.6 19.1-44.7 17.6-7.1 37.6 1.5 44.7 19.1 21.3 52.9 32 108.9 32 166.4 0 246.2-200.3 446.5-446.5 446.5z"
            fill="#4D4D4D"
          ></path>
          <path
            d="M834.2 267.9m-39.6 0a39.6 39.6 0 1 0 79.2 0 39.6 39.6 0 1 0-79.2 0Z"
            fill="#4D4D4D"
          ></path>
          <path
            d="M423.5 452.8v0.7c-0.3 11.6-6.7 22.1-16.9 27.7-10.2 5.6-22.7 5.3-32.7-0.6-10.4-6.1-16.6-17.6-15.8-29.7 0-47.7 13.6-87.1 40.4-118.1 29.4-33.4 70-49.8 121.3-49.8 45.7 0 82.6 13.1 110.8 39.6 27 25.2 40.4 59.2 40.4 101.4 0 30.6-9.6 58.7-28.3 83.2-7.8 9.8-24.7 26-55.3 53.4-12.4 10.2-23.2 22.2-31.9 35.7-8.3 13.8-12.7 29.5-12.8 45.6l1.3 19.5c0.2 3.1-1 6.2-3.1 8.5-2.2 2.3-5.2 3.6-8.3 3.6h-42.8c-3 0-6-1.2-8.1-3.3-2.1-2.1-3.3-5-3.3-8v-19.5c0-21.4 4.6-41.3 13.4-58.8 10.7-22.7 32.9-49 65.1-76.7 20-19.8 26.2-26.3 30.4-31.4 13.1-16.6 19.4-33.2 19.4-50.4 0-25.7-7.5-46.1-21.8-60.8-15-14.9-37.1-22.3-67-22.3-33.3 0-57.2 10.6-73.6 32.2-13.8 17.1-20.4 42.1-20.4 76.7l-0.4 1.6z m91 330.8c-17.9 0.1-32.6-14.4-32.7-32.4 0.1-17.9 14.7-32.4 32.7-32.3 17.9-0.1 32.5 14.4 32.7 32.3-0.1 18-14.7 32.4-32.7 32.4z m0 0"
            fill="#4D4D4D"
          ></path>
        </svg>
      );

    case "undo":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M289.6384 256H614.4a307.2 307.2 0 1 1 0 614.4H204.8a51.2 51.2 0 0 1 0-102.4h409.6a204.8 204.8 0 1 0 0-409.6H286.0032l59.2384 59.2384A51.2 51.2 0 1 1 272.7936 489.984L128 345.2416a51.2 51.2 0 0 1 0-72.448L272.7936 128a51.2 51.2 0 0 1 72.448 72.3968L289.6384 256z"
            fill={disabled ? "currentColor" : "#666666"}
          ></path>
        </svg>
      );
    case "redo":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M737.9968 256l-55.6032-55.6032A51.2 51.2 0 1 1 754.8416 128l144.7936 144.7936a51.2 51.2 0 0 1 0 72.448L754.8416 489.984a51.2 51.2 0 0 1-72.448-72.3968L741.632 358.4H409.6a204.8 204.8 0 1 0 0 409.6h409.6a51.2 51.2 0 0 1 0 102.4H409.6A307.2 307.2 0 1 1 409.6 256h328.3968z"
            fill={disabled ? "currentColor" : "#666666"}
          ></path>
        </svg>
      );

    case "format":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M452.225 146.9L111.637 487.488a33.223 33.223 0 0 1-47.132 0c-13.073-13.073-12.9-34.059 0-47.132L405.093 99.768a33.223 33.223 0 0 1 47.132 0 33.223 33.223 0 0 1 0 47.132z m472.007 471.835L583.644 959.323a33.223 33.223 0 0 1-47.132 0c-13.073-13.073-12.901-34.06 0-47.132L877.1 571.603a33.223 33.223 0 0 1 47.132 0c13.073 13.073 12.9 34.23 0 47.132z m-473.039 2.064L348.157 723.835a33.223 33.223 0 0 1-47.132 0c-13.073-13.073-12.901-34.058 0-47.131L404.06 573.667a33.223 33.223 0 0 1 47.132 0c13.073 13.073 13.073 34.23 0 47.132z m-152.749-95.812l-74.654 74.654a33.223 33.223 0 0 1-47.132 0c-13.073-13.073-12.9-34.059 0-47.132l74.655-74.654a33.223 33.223 0 0 1 47.131 0c12.902 13.073 12.902 34.231 0 47.132zM545.285 772l-74.654 74.653a33.223 33.223 0 0 1-47.132 0c-13.073-13.073-12.901-34.058 0-47.131l74.654-74.655a33.223 33.223 0 0 1 47.132 0 33.223 33.223 0 0 1 0 47.132z"
            fill={disabled ? "currentColor" : "#666666"}
          ></path>
          <path
            d="M536.34 959.323L64.505 487.66a33.223 33.223 0 0 1 0-47.132c13.073-13.073 34.06-12.9 47.132 0l471.835 471.835a33.223 33.223 0 0 1 0 47.132c-13.073 12.9-34.059 12.9-47.132-0.172zM732.264 763.57L260.43 291.737a33.223 33.223 0 0 1 0-47.132c13.073-13.073 34.06-12.901 47.132 0L779.224 716.44a33.223 33.223 0 0 1 0 47.132c-12.901 13.073-34.059 12.9-46.96 0z m180.099-544.08L804.682 111.637a33.223 33.223 0 0 1 0-47.132c13.073-13.073 34.059-12.9 47.132 0l107.68 107.853a33.223 33.223 0 0 1 0 47.132c-13.072 13.073-34.23 13.073-47.131 0zM587.256 328.719L405.437 146.9a33.223 33.223 0 0 1 0-47.132c13.073-13.073 34.06-12.9 47.132 0L634.56 281.76a33.223 33.223 0 0 1 0 47.132c-13.073 12.901-34.23 12.901-47.304-0.172zM877.1 618.563l-181.819-181.82a33.223 33.223 0 0 1 0-47.131c13.073-13.073 34.059-12.901 47.132 0l181.99 181.99a33.223 33.223 0 0 1 0 47.133c-13.072 12.9-34.23 12.9-47.303-0.172z"
            fill={disabled ? "currentColor" : "#666666"}
          ></path>
          <path
            d="M959.323 219.49L742.24 436.572a33.223 33.223 0 0 1-47.132 0c-13.073-13.073-12.901-34.059 0-47.132L912.19 172.358a33.223 33.223 0 0 1 47.132 0c13.073 12.901 13.073 34.059 0 47.132zM851.642 111.637L634.388 328.72a33.223 33.223 0 0 1-47.132 0c-13.073-13.073-12.9-34.059 0-47.132L804.51 64.505a33.223 33.223 0 0 1 47.132 0c13.073 13.073 12.9 34.06 0 47.132z"
            fill={disabled ? "currentColor" : "#666666"}
          ></path>
        </svg>
      );
    case "font-color":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M321.024 736l66.624-190.272h260.416l66.56 190.272H832L569.088 32H454.912L192 736h129.024z m298.24-278.08h-202.88L524.992 149.12h-14.08l108.416 308.8z"
            fill={disabled ? "currentColor" : "#4a5565"}
          ></path>
          <path
            d="M128 832h768v128H128z"
            fill={disabled ? "currentColor" : "#22B09F"}
          ></path>
        </svg>
      );
    case "font-type":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M896 179.2V358.4h-51.2V230.4h-665.6V358.4h-51.2V179.2z"
            fill="#333333"
          ></path>
          <path d="M537.6 204.8v665.6h-51.2V204.8z" fill="#333333"></path>
          <path d="M614.4 844.8v51.2H409.6v-51.2z" fill="#333333"></path>
        </svg>
      );
    case "font-size":
      return (
        <svg viewBox="0 0 1024 1024" version="1.1" {...props}>
          <path
            d="M896 179.2V358.4h-51.2V230.4h-665.6V358.4h-51.2V179.2z"
            fill="#7D7E80"
          ></path>
          <path d="M537.6 204.8v665.6h-51.2V204.8z" fill="#7D7E80"></path>
          <path d="M614.4 844.8v51.2H409.6v-51.2z" fill="#7D7E80"></path>
        </svg>
      );
    case "child-theme":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M128 160A96 96 0 0 1 224 64h576A96 96 0 0 1 896 160v256A96 96 0 0 1 800 512h-576A96 96 0 0 1 128 416v-256z m672 0h-576v256h576v-256z"
            fill={disabled ? "currentColor" : "#4a5565"}
          ></path>
          <path d="M464 672v-192h96v192h-96z" fill="#4a5565"></path>
          <path
            d="M192 736A96 96 0 0 1 288 640h448a96 96 0 0 1 96 96v128a96 96 0 0 1-96 96h-448A96 96 0 0 1 192 864v-128z m544 0h-448v128h448v-128z"
            fill={disabled ? "currentColor" : "#008A81"}
          ></path>
        </svg>
      );
    case "parent-theme":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M64 160A96 96 0 0 1 160 64h512A96 96 0 0 1 768 160v256A96 96 0 0 1 672 512h-512A96 96 0 0 1 64 416v-256z m608 0h-512v256h512v-256z"
            fill={disabled ? "currentColor" : "#4a5565"}
          ></path>
          <path
            d="M176 704V448h96v256c0 26.496 21.504 48 48 48h96v96H320A144 144 0 0 1 176 704z"
            fill={disabled ? "currentColor" : "#4a5565"}
          ></path>
          <path
            d="M384 736A96 96 0 0 1 480 640h384a96 96 0 0 1 96 96v128a96 96 0 0 1-96 96h-384A96 96 0 0 1 384 864v-128z m480 0h-384v128h384v-128z"
            fill={disabled ? "currentColor" : "#008A81"}
          ></path>
        </svg>
      );
    case "same-theme":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M96 48a48 48 0 0 1 48 48v576c0 26.496 21.504 48 48 48h352a48 48 0 0 1 0 96H192A144 144 0 0 1 48 672v-576A48 48 0 0 1 96 48z"
            fill={disabled ? "currentColor" : "#4a5565"}
          ></path>
          <path
            d="M80 256A48 48 0 0 1 128 208h384a48 48 0 0 1 0 96H128A48 48 0 0 1 80 256z"
            fill={disabled ? "currentColor" : "#4a5565"}
          ></path>
          <path
            d="M512 160A96 96 0 0 1 608 64h320A96 96 0 0 1 1024 160v192A96 96 0 0 1 928 448h-320A96 96 0 0 1 512 352v-192z m416 0h-320v192h320v-192z"
            fill={disabled ? "currentColor" : "#4a5565"}
          ></path>
          <path
            d="M512 672A96 96 0 0 1 608 576h320a96 96 0 0 1 96 96v192a96 96 0 0 1-96 96h-320A96 96 0 0 1 512 864v-192z m416 0h-320v192h320v-192z"
            fill={disabled ? "currentColor" : "#008A81"}
          ></path>
        </svg>
      );
    case "summary":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M512 64a160 160 0 0 1 159.68 149.504l0.32 10.496v96.64h-64V224a96 96 0 0 0-86.784-95.552L512 128H320V64h192zM512 960a160 160 0 0 0 159.68-149.504l0.32-10.496v-96.832h-64V800a96 96 0 0 1-86.784 95.552L512 896H320v64h192z"
            fill={disabled ? "currentColor" : "#3D4757"}
          ></path>
          <path
            d="M672 320a160 160 0 0 0 149.504 159.68L832 480v64a224 224 0 0 1-223.68-211.712L608 320h64z"
            fill={disabled ? "currentColor" : "#3D4757"}
          ></path>
          <path
            d="M672 704a160 160 0 0 1 149.504-159.68L832 544v-64a224 224 0 0 0-223.68 211.712L608 704h64z"
            fill={disabled ? "currentColor" : "#3D4757"}
          ></path>
        </svg>
      );
    case "frame":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M320 352h384a64 64 0 0 1 64 64v192a64 64 0 0 1-64 64H320a64 64 0 0 1-64-64v-192a64 64 0 0 1 64-64z m32 64a32 32 0 0 0-32 32v128a32 32 0 0 0 32 32h320a32 32 0 0 0 32-32v-128a32 32 0 0 0-32-32H352zM96 96h64v64H96V96z m0 128h64v64H96V224z m0 128h64v64H96v-64z m0 128h64v64H96v-64z m0 128h64v64H96v-64z m0 128h64v64H96v-64z m0 128h64v64H96v-64zM864 224h64v64h-64V224z m0 128h64v64h-64v-64z m0 128h64v64h-64v-64z m0 128h64v64h-64v-64z m0 128h64v64h-64v-64z m0 128h64v64h-64v-64zM608 96h64v64h-64V96z m128 0h64v64h-64V96z m128 0h64v64h-64V96zM480 96h64v64h-64V96z m-128 0h64v64h-64V96zM224 96h64v64H224V96z m384 768h64v64h-64v-64z m128 0h64v64h-64v-64z m-256 0h64v64h-64v-64z m-128 0h64v64h-64v-64z m-128 0h64v64H224v-64z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
        </svg>
      );
    case "hyperlink":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M573.44 640a187.68 187.68 0 0 1-132.8-55.36L416 560l45.28-45.28 24.64 24.64a124.32 124.32 0 0 0 170.08 5.76l1.44-1.28a49.44 49.44 0 0 0 4-3.84l101.28-101.28a124.16 124.16 0 0 0 0-176l-1.92-1.92a124.16 124.16 0 0 0-176 0l-51.68 51.68a49.44 49.44 0 0 0-3.84 4l-20 24.96-49.92-40L480 276.32a108.16 108.16 0 0 1 8.64-9.28l51.68-51.68a188.16 188.16 0 0 1 266.72 0l1.92 1.92a188.16 188.16 0 0 1 0 266.72l-101.28 101.28a112 112 0 0 1-8.48 7.84 190.24 190.24 0 0 1-125.28 48z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
          <path
            d="M350.72 864a187.36 187.36 0 0 1-133.28-55.36l-1.92-1.92a188.16 188.16 0 0 1 0-266.72l101.28-101.28a112 112 0 0 1 8.48-7.84 188.32 188.32 0 0 1 258.08 7.84L608 464l-45.28 45.28-24.64-24.64A124.32 124.32 0 0 0 368 478.88l-1.44 1.28a49.44 49.44 0 0 0-4 3.84l-101.28 101.28a124.16 124.16 0 0 0 0 176l1.92 1.92a124.16 124.16 0 0 0 176 0l51.68-51.68a49.44 49.44 0 0 0 3.84-4l20-24.96 50.08 40-20.8 25.12a108.16 108.16 0 0 1-8.64 9.28l-51.68 51.68A187.36 187.36 0 0 1 350.72 864z"
            fill={disabled ? "currentColor" : "#333333"}
          ></path>
        </svg>
      );
    case "watermark":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M439.466667 904.533333c-119.466667 0-315.733333-64-315.733334-294.4 0-221.866667 213.333333-418.133333 324.266667-490.666666 8.533333-4.266667 21.333333-4.266667 29.866667 0 85.333333 64 204.8 200.533333 251.733333 324.266666 4.266667 12.8 0 29.866667-17.066667 34.133334-12.8 4.266667-29.866667 0-34.133333-17.066667-42.666667-102.4-140.8-221.866667-217.6-285.866667-106.666667 76.8-281.6 247.466667-281.6 435.2 0 230.4 234.666667 238.933333 260.266667 238.933334 17.066667 0 29.866667 12.8 29.866666 25.6s-17.066667 29.866667-29.866666 29.866666z"
            fill={disabled ? "currentColor" : "#6D7582"}
          ></path>
          <path
            d="M755.2 682.666667c-34.133333 0-64-21.333333-93.866667-42.666667-46.933333-34.133333-76.8-59.733333-149.333333 17.066667-12.8 12.8-29.866667 12.8-38.4 0s-12.8-29.866667 0-38.4c110.933333-110.933333 174.933333-59.733333 226.133333-21.333334 46.933333 38.4 76.8 59.733333 157.866667-38.4 8.533333-12.8 25.6-12.8 38.4-4.266666 12.8 8.533333 12.8 25.6 4.266667 38.4-59.733333 64-106.666667 89.6-145.066667 89.6zM755.2 853.333333c-34.133333 0-64-21.333333-93.866667-42.666666-46.933333-34.133333-76.8-59.733333-149.333333 17.066666-12.8 12.8-29.866667 12.8-38.4 0s-12.8-29.866667 0-38.4c110.933333-110.933333 174.933333-59.733333 226.133333-21.333333 46.933333 38.4 76.8 59.733333 157.866667-38.4 8.533333-12.8 25.6-12.8 38.4-4.266667 12.8 8.533333 12.8 25.6 4.266667 38.4-59.733333 64-106.666667 89.6-145.066667 89.6z"
            fill={disabled ? "currentColor" : "#6D7582"}
          ></path>
        </svg>
      );
    case "fold":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M962.22 231.73l-898.58 0 0-84.24L962.22000001 147.49zM63.65 358.1L260.21 498.5l-196.56 140.4zM962.22000001 540.61l-589.69000001 0 0-84.24L962.22 456.37zM962.22 849.11l-898.58 0 0-84.24L962.22 764.86999999z"
            fill={disabled ? "currentColor" : "#2c2c2c"}
          ></path>
        </svg>
      );
    case "unfold":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M911.2 668H400.8c-24.8 0-44.8-21.6-44.8-48s20-48 44.8-48h510.4c24.8 0 44.8 21.6 44.8 48s-20 48-44.8 48z m0-224H400.8c-24.8 0-44.8-21.6-44.8-48s20-48 44.8-48h510.4c24.8 0 44.8 21.6 44.8 48s-20 48-44.8 48zM908 228H126.4c-26.4 0-49.6-20-50.4-46.4-0.8-27.2 20.8-49.6 48-49.6h781.6c26.4 0 49.6 20 50.4 46.4 0.8 27.2-20.8 49.6-48 49.6zM76 667.2V348.8L299.2 508 76 667.2c0 0.8 0 0.8 0 0zM124 788h781.6c26.4 0 49.6 20 50.4 46.4 0.8 27.2-20.8 49.6-48 49.6H126.4c-26.4 0-49.6-20-50.4-46.4-0.8-27.2 20.8-49.6 48-49.6z"
            fill={disabled ? "currentColor" : "#2c2c2c"}
          ></path>
        </svg>
      );
    case "bold":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M256 465.454545h298.193455A144.128 144.128 0 0 0 698.181818 321.466182v-14.568727A144.128 144.128 0 0 0 554.193455 162.909091H256v302.545454z m-69.818182 69.818182V149.387636A56.366545 56.366545 0 0 1 242.478545 93.090909h311.71491C672.093091 93.090909 768 188.997818 768 306.897455v14.568727a213.271273 213.271273 0 0 1-69.073455 157.230545C793.227636 511.441455 861.090909 601.181091 861.090909 706.466909v6.702546C861.090909 846.056727 752.965818 954.181818 620.078545 954.181818H244.363636A58.228364 58.228364 0 0 1 186.181818 896V535.272727z m69.818182 349.090909h364.078545C714.472727 884.363636 791.272727 807.563636 791.272727 713.169455v-6.702546C791.272727 612.072727 714.472727 535.272727 620.078545 535.272727H256v349.090909z"
            fill="#333333"
          ></path>
        </svg>
      );
    case "italic":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M768 85.792h-288a32 32 0 0 0 0 64h96.32l-230.336 704H256a32 32 0 0 0 0 64h288a32 32 0 0 0 0-64h-93.728l230.528-704H768a32 32 0 0 0 0-64z"
            fill="#333333"
          ></path>
        </svg>
      );
    case "underline":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M512 811.296a312 312 0 0 0 312-312V89.6h-112v409.696a200 200 0 1 1-400 0V89.6h-112v409.696a312 312 0 0 0 312 312zM864 885.792H160a32 32 0 0 0 0 64h704a32 32 0 0 0 0-64z"
            fill="#333333"
          ></path>
        </svg>
      );

    case "strikethrough":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M334.908235 542.117647L512 78.908235 689.091765 542.117647h60.235294L542.117647 0h-60.235294L274.672941 542.117647h60.235294zM205.402353 722.823529L90.352941 1024h60.235294l115.049412-301.176471h-60.235294zM758.362353 722.823529L873.411765 1024h60.235294l-115.049412-301.176471h-60.235294z"
            fill="#333333"
          ></path>
          <path
            d="M90.352941 602.352941h843.294118v60.235294H90.352941z"
            fill="#333333"
          ></path>
        </svg>
      );
    case "formula":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M512 928H128a32 32 0 0 1-26.88-49.92L345.6 512 101.12 145.92A32 32 0 0 1 128 96h384a32 32 0 0 1 0 64H187.52l223.36 334.08a33.28 33.28 0 0 1 0 35.84L187.52 864H512a32 32 0 0 1 0 64zM640 928a36.48 36.48 0 0 1-17.92-5.12 32.64 32.64 0 0 1-8.96-44.8l256-384a32 32 0 0 1 53.76 35.84l-256 384a33.28 33.28 0 0 1-26.88 14.08z"
            fill="#333333"
          ></path>
          <path
            d="M896 928a33.28 33.28 0 0 1-26.88-14.08l-256-384a32 32 0 1 1 53.76-35.84l256 384a32.64 32.64 0 0 1-8.96 44.8 36.48 36.48 0 0 1-17.92 5.12z"
            fill="#333333"
          ></path>
        </svg>
      );
    case "bullet-list":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path d="M288 160h640v96H288zM288 464h640v96H288zM288 768h640v96H288z"></path>
          <path d="M160 208m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"></path>
          <path d="M160 512m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"></path>
          <path d="M160 816m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"></path>
        </svg>
      );

    case "numbered-list":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path d="M99.232 288h54.848V86.848H63.264v54.88h35.968zM64 461.728h64l-64 91.424V608h128v-54.848H128l64-91.424v-54.88H64zM64 781.728h73.152V800H95.36v54.848h41.792v18.304H64V928h128v-201.152H64zM288 160h640v96H288zM288 464h640v96H288zM288 768h640v96H288z"></path>
        </svg>
      );
    case "focus":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M515.12 930.87c-56.53 0-111.39-11.08-163.05-32.93-49.88-21.1-94.67-51.29-133.13-89.75-38.46-38.46-68.65-83.25-89.75-133.13C107.34 623.39 96.26 568.53 96.26 512s11.08-111.39 32.93-163.05c21.1-49.88 51.29-94.67 89.75-133.13s83.25-68.65 133.13-89.75c51.66-21.85 106.52-32.93 163.05-32.93 56.53 0 111.39 11.08 163.05 32.93 49.88 21.1 94.67 51.3 133.13 89.75 38.46 38.46 68.65 83.25 89.75 133.13 21.85 51.66 32.93 106.52 32.93 163.05 0 56.53-11.08 111.39-32.93 163.05-21.1 49.88-51.29 94.67-89.75 133.13-38.46 38.46-83.25 68.65-133.13 89.75-51.66 21.86-106.52 32.94-163.05 32.94z m0-761.74c-189.06 0-342.87 153.81-342.87 342.87s153.81 342.87 342.87 342.87S857.99 701.06 857.99 512 704.18 169.13 515.12 169.13z"
            fill="#2A2A2A"
          ></path>
          <path
            d="M512 383.16c-20.99 0-38-17.01-38-38V47.92c0-20.99 17.01-38 38-38s38 17.01 38 38v297.24c0 20.98-17.01 38-38 38zM512 1014.08c-20.99 0-38-17.01-38-38V678.84c0-20.99 17.01-38 38-38s38 17.01 38 38v297.24c0 20.99-17.01 38-38 38zM976.08 550H678.84c-20.99 0-38-17.01-38-38s17.01-38 38-38h297.24c20.99 0 38 17.01 38 38s-17.01 38-38 38zM345.16 550H47.92c-20.99 0-38-17.01-38-38s17.01-38 38-38h297.24c20.99 0 38 17.01 38 38s-17.02 38-38 38z"
            fill="#2A2A2A"
          ></path>
        </svg>
      );
    case "ai-creation":
      return (
        <svg viewBox="0 0 1218 1024" {...props}>
          <path
            d="M326.183722 206.500257c19.568414 0 36.788619 9.523295 47.529504 24.177863a41.528523 41.528523 0 0 1 17.394146 18.698707l1.348046 3.391858 249.910392 711.072687c8.088278 22.916787-6.348863 48.529667-32.17917 57.22674-24.525746 8.262219-50.529994-1.652444-60.009803-22.351478l-1.304561-3.348373-64.793194-184.247491H160.460996l-64.706223 184.247491c-8.088278 22.916787-35.527543 34.440409-61.314364 25.699851-24.569231-8.262219-38.788945-31.787802-33.222819-53.791396l1.000164-3.47883L252.08466 252.81217a43.267938 43.267938 0 0 1 30.439756-26.91744A58.574787 58.574787 0 0 1 326.140237 206.500257h0.043485z m459.553336 201.511181c25.960763 0 47.225106 12.958639 49.312404 29.396106l0.130456 2.609122v549.046217c0 17.698544-22.177536 32.005229-49.44286 32.005229-25.960763 0-47.225106-12.915153-49.268918-29.396107l-0.217427-2.609122V440.060152c0-17.698544 22.177536-32.005229 49.486345-32.005229zM322.226554 350.741212l-127.760002 363.494165h255.563489L322.226554 350.697727zM1038.430514 105.52724a14.958966 14.958966 0 0 1 9.697236 9.56678l38.658489 117.801853 120.889315 42.87657a14.958966 14.958966 0 0 1-0.869708 28.482914l-118.410648 33.266304-36.658163 116.018954a14.958966 14.958966 0 0 1-28.439428 0.130456l-38.701975-117.758368-117.323515-37.223473a14.958966 14.958966 0 0 1-0.217426-28.395943l115.975468-39.049858 36.658162-115.975468a14.958966 14.958966 0 0 1 18.742193-9.740721zM772.387051 0.553569a10.436488 10.436488 0 0 1 6.653261 6.827202l22.04708 70.489776 72.490103 24.569232a10.436488 10.436488 0 0 1-0.391368 19.959782l-72.185706 21.48177-23.569067 70.794174a10.436488 10.436488 0 0 1-19.872812-0.173941l-22.04708-70.489777-70.228865-21.220858a10.436488 10.436488 0 0 1-0.434853-19.872811l70.794174-24.873629 23.525582-70.881145a10.436488 10.436488 0 0 1 13.219551-6.609775z"
            fill="#d45eac"
          ></path>
        </svg>
      );
    case "stop":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M512 1024A512 512 0 1 1 512 0a512 512 0 0 1 0 1024z m3.008-92.992a416 416 0 1 0 0-832 416 416 0 0 0 0 832zM320 320h384v384H320V320z"
            fill="#262626"
          ></path>
        </svg>
      );

    case "rotate-ccw":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M306.56 420.928H86.912a46.272 46.272 0 0 1-47.104-47.04V154.112c0-26.688 20.416-47.104 47.104-47.104s47.104 20.48 47.104 47.104v172.672H306.56c26.624 0 47.04 20.416 47.04 47.104a46.272 46.272 0 0 1-47.04 47.04zM932.864 916.992a46.272 46.272 0 0 1-47.04-47.104v-172.672h-172.672a46.272 46.272 0 0 1-47.104-47.104c0-26.688 20.416-47.104 47.104-47.104h219.712c26.688 0 47.104 20.48 47.104 47.104v219.776a46.272 46.272 0 0 1-47.104 47.104z"
            fill="#172B4D"
          ></path>
          <path
            d="M932.864 559.104A46.272 46.272 0 0 1 885.824 512 375.168 375.168 0 0 0 155.904 389.568c-7.872 25.088-36.096 37.696-59.648 29.824-25.088-7.872-37.696-36.096-29.824-59.648A471.296 471.296 0 0 1 510.592 42.688 469.632 469.632 0 0 1 980.032 512a46.272 46.272 0 0 1-47.104 47.104zM510.656 981.312A469.632 469.632 0 0 1 41.344 512c0-26.688 20.352-47.104 47.04-47.104s47.104 20.48 47.104 47.104a375.168 375.168 0 0 0 729.92 122.432c7.872-25.088 36.096-37.696 59.648-29.824 25.088 7.872 37.632 36.096 29.824 59.648a471.296 471.296 0 0 1-444.224 317.056z"
            fill="#172B4D"
          ></path>
        </svg>
      );
    case "check":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M369.792 704.32L930.304 128 1024 223.616 369.984 896l-20.288-20.864-0.128 0.128L0 516.8 96.128 423.68l273.664 280.64z"
            fill="#262626"
          ></path>
        </svg>
      );
    case "trash2":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path
            d="M896 256h-213.333333V184.746667A103.253333 103.253333 0 0 0 576 85.333333h-128A103.253333 103.253333 0 0 0 341.333333 184.746667V256H128a42.666667 42.666667 0 0 0 0 85.333333h42.666667v469.333334a128 128 0 0 0 128 128h426.666666a128 128 0 0 0 128-128V341.333333h42.666667a42.666667 42.666667 0 0 0 0-85.333333zM426.666667 682.666667a42.666667 42.666667 0 0 1-85.333334 0v-170.666667a42.666667 42.666667 0 0 1 85.333334 0z m0-497.92c0-6.826667 8.96-14.08 21.333333-14.08h128c12.373333 0 21.333333 7.253333 21.333333 14.08V256h-170.666666zM682.666667 682.666667a42.666667 42.666667 0 0 1-85.333334 0v-170.666667a42.666667 42.666667 0 0 1 85.333334 0z"
            fill="#231F20"
          ></path>
        </svg>
      );

    case "sparkles":
      return (
        <svg viewBox="0 0 1024 1024" {...props}>
          <path d="M488.009143 231.497143c5.12 0 7.698286-2.998857 8.996571-7.716572 13.275429-71.570286 12.434286-73.289143 87.003429-87.424 5.12-0.859429 8.137143-3.84 8.137143-8.996571 0-5.138286-2.998857-8.155429-8.155429-8.996571-74.130286-14.994286-71.990857-16.713143-86.985143-87.442286-1.28-4.699429-3.858286-7.698286-8.996571-7.698286-5.156571 0-7.716571 2.998857-9.014857 7.698286-14.994286 70.729143-12.434286 72.429714-87.003429 87.442286-4.699429 0.841143-8.137143 3.84-8.137143 8.996571 0 5.138286 3.437714 8.137143 8.137143 8.996571 74.587429 14.994286 73.728 15.853714 87.003429 87.424 1.28 4.717714 3.858286 7.716571 8.996571 7.716572zM280.576 526.354286c8.137143 0 13.714286-5.138286 14.573714-12.854857 15.414857-114.432 19.273143-114.432 137.563429-137.142858 7.716571-1.28 13.293714-6.436571 13.293714-14.573714 0-7.716571-5.577143-13.275429-13.293714-14.573714-118.290286-16.274286-122.569143-20.132571-137.563429-136.704-0.859429-7.716571-6.436571-13.293714-14.573714-13.293714-7.716571 0-13.293714 5.577143-14.153143 13.714285-14.134857 114.852571-20.132571 114.432-137.563428 136.283429-7.716571 1.718857-13.293714 6.857143-13.293715 14.573714 0 8.576 5.577143 13.293714 14.994286 14.573714 116.589714 18.852571 121.728 21.851429 135.862857 136.283429 0.859429 8.576 6.436571 13.714286 14.153143 13.714286z m290.56 474.422857c11.154286 0 19.291429-8.137143 21.430857-19.712 30.427429-234.843429 63.433143-270.427429 295.716572-296.137143 11.995429-1.28 20.150857-10.294857 20.150857-21.430857 0-11.154286-8.155429-19.712-20.150857-21.430857-232.283429-25.709714-265.289143-61.275429-295.716572-296.137143-2.139429-11.574857-10.276571-19.291429-21.430857-19.291429-11.136 0-19.273143 7.716571-20.992 19.291429-30.427429 234.861714-63.853714 270.427429-295.716571 296.137143-12.434286 1.718857-20.571429 10.294857-20.571429 21.430857 0 11.154286 8.137143 20.150857 20.571429 21.430857 231.424 30.427429 263.570286 61.714286 295.716571 296.137143 1.718857 11.574857 9.856 19.712 20.992 19.712z"></path>
        </svg>
      );
    default:
      return null;
  }
};

export default CustomIcons;
