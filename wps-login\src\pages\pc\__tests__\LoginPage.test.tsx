import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import LoginPage from "../loginPage";
import type { User } from "@/types/user";

// 模拟依赖组件
vi.mock("@/components/phoneLogin/PhoneLogin", () => ({
  default: ({ onBack }) => (
    <div data-testid="mock-phone-login">
      <span>手机登录页面</span>
      <button onClick={onBack}>返回</button>
    </div>
  ),
}));

vi.mock("@/components/moreLogin/MoreLogin", () => ({
  default: ({ onBack }) => (
    <div data-testid="mock-more-login">
      <span>更多登录方式</span>
      <button onClick={onBack}>返回</button>
    </div>
  ),
}));

vi.mock("@/components/common/consent/ConsentModal", () => ({
  default: ({ isOpen, onClose, onAgree, onCancel }) =>
    isOpen ? (
      <div data-testid="mock-consent-modal">
        <span>隐私协议弹窗</span>
        <button data-testid="agree-button" onClick={onAgree}>
          同意
        </button>
        <button onClick={onCancel}>取消</button>
        <button onClick={onClose}>关闭</button>
      </div>
    ) : null,
}));

describe("PC端登录页面 (LoginPage)", () => {
  // 模拟 sessionStorage
  const sessionStorageMock = (() => {
    let store: Record<string, string> = {};
    return {
      getItem: (key: string) => store[key] || null,
      setItem: (key: string, value: string) => {
        store[key] = value.toString();
      },
      removeItem: (key: string) => {
        delete store[key];
      },
      clear: () => {
        store = {};
      },
    };
  })();

  // 替换原生的 sessionStorage
  Object.defineProperty(window, "sessionStorage", {
    value: sessionStorageMock,
  });

  beforeEach(() => {
    // 清除 sessionStorage
    sessionStorageMock.clear();
  });

  it("应该正确渲染PC端登录页面", () => {
    render(<LoginPage />);

    // 验证页面标题和副标题
    expect(screen.getByText("微信扫码登录")).toBeInTheDocument();
    expect(
      screen.getByText("使用金山办公在线服务账号登录")
    ).toBeInTheDocument();

    // 验证二维码占位符
    expect(document.querySelector(".qr-placeholder")).toBeInTheDocument();

    // 验证复选框
    expect(screen.getByLabelText("自动登录")).toBeInTheDocument();
    expect(screen.getByLabelText(/已阅读并同意/)).toBeInTheDocument();

    // 验证登录方式
    expect(screen.getByText("QQ账号")).toBeInTheDocument();
    expect(screen.getByText("手机")).toBeInTheDocument();
    expect(screen.getByText("专属账号")).toBeInTheDocument();
    expect(screen.getByText("更多")).toBeInTheDocument();
  });

  it("点击登录方式复选框应该改变其状态", () => {
    render(<LoginPage />);

    const autoLoginCheckbox = screen.getByLabelText("自动登录");
    const agreeTermsCheckbox = screen.getByLabelText(/已阅读并同意/);

    // 默认状态应为未选中
    expect(autoLoginCheckbox).not.toBeChecked();
    expect(agreeTermsCheckbox).not.toBeChecked();

    // 点击复选框
    fireEvent.click(autoLoginCheckbox);
    fireEvent.click(agreeTermsCheckbox);

    // 状态应变为选中
    expect(autoLoginCheckbox).toBeChecked();
    expect(agreeTermsCheckbox).toBeChecked();
  });

  it("在未同意隐私协议时点击手机登录方式应显示隐私协议弹窗", () => {
    render(<LoginPage />);

    // 点击手机登录方式
    fireEvent.click(screen.getByText("手机"));

    // 验证隐私协议弹窗显示
    expect(screen.getByTestId("mock-consent-modal")).toBeInTheDocument();

    // 验证sessionStorage中记录了lastClickedMethod
    expect(sessionStorageMock.getItem("lastClickedMethod")).toBe("phone");
  });

  it("在未同意隐私协议时点击更多登录方式应显示隐私协议弹窗", () => {
    render(<LoginPage />);

    // 点击更多登录方式
    fireEvent.click(screen.getByText("更多"));

    // 验证隐私协议弹窗显示
    expect(screen.getByTestId("mock-consent-modal")).toBeInTheDocument();

    // 验证sessionStorage中记录了lastClickedMethod
    expect(sessionStorageMock.getItem("lastClickedMethod")).toBe("more");
  });

  it("在未同意隐私协议时点击QQ登录方式不应显示隐私协议弹窗", () => {
    render(<LoginPage />);

    // 点击QQ登录方式
    fireEvent.click(screen.getByText("QQ账号"));

    // 验证隐私协议弹窗未显示
    expect(screen.queryByTestId("mock-consent-modal")).not.toBeInTheDocument();
  });

  it("同意隐私协议后点击手机登录方式应显示手机登录页面", () => {
    render(<LoginPage />);

    // 先同意隐私协议
    const agreeTermsCheckbox = screen.getByLabelText(/已阅读并同意/);
    fireEvent.click(agreeTermsCheckbox);

    // 点击手机登录方式
    fireEvent.click(screen.getByText("手机"));

    // 验证显示手机登录页面
    expect(screen.getByTestId("mock-phone-login")).toBeInTheDocument();
    expect(screen.getByText("手机登录页面")).toBeInTheDocument();
  });

  it("同意隐私协议后点击更多登录方式应显示更多登录页面", () => {
    render(<LoginPage />);

    // 先同意隐私协议
    const agreeTermsCheckbox = screen.getByLabelText(/已阅读并同意/);
    fireEvent.click(agreeTermsCheckbox);

    // 点击更多登录方式
    fireEvent.click(screen.getByText("更多"));

    // 验证显示更多登录页面
    expect(screen.getByTestId("mock-more-login")).toBeInTheDocument();
    expect(screen.getByText("更多登录方式")).toBeInTheDocument();
  });

  it("在手机登录页面点击返回按钮应返回主登录页", () => {
    render(<LoginPage />);

    // 先同意隐私协议并进入手机登录页面
    const agreeTermsCheckbox = screen.getByLabelText(/已阅读并同意/);
    fireEvent.click(agreeTermsCheckbox);
    fireEvent.click(screen.getByText("手机"));

    // 验证显示手机登录页面
    expect(screen.getByTestId("mock-phone-login")).toBeInTheDocument();

    // 点击返回按钮
    fireEvent.click(screen.getByText("返回"));

    // 验证返回主登录页面
    expect(screen.getByText("微信扫码登录")).toBeInTheDocument();
    expect(screen.queryByTestId("mock-phone-login")).not.toBeInTheDocument();
  });

  it("在更多登录页面点击返回按钮应返回主登录页", () => {
    render(<LoginPage />);

    // 先同意隐私协议并进入更多登录页面
    const agreeTermsCheckbox = screen.getByLabelText(/已阅读并同意/);
    fireEvent.click(agreeTermsCheckbox);
    fireEvent.click(screen.getByText("更多"));

    // 验证显示更多登录页面
    expect(screen.getByTestId("mock-more-login")).toBeInTheDocument();

    // 点击返回按钮
    fireEvent.click(screen.getByText("返回"));

    // 验证返回主登录页面
    expect(screen.getByText("微信扫码登录")).toBeInTheDocument();
    expect(screen.queryByTestId("mock-more-login")).not.toBeInTheDocument();
  });

  it("在协议弹窗点击同意后应该跳转到之前点击的页面（手机登录）", async () => {
    render(<LoginPage />);

    // 点击手机登录方式但未同意隐私协议
    fireEvent.click(screen.getByText("手机"));

    // 验证显示隐私协议弹窗
    expect(screen.getByTestId("mock-consent-modal")).toBeInTheDocument();

    // 点击同意按钮
    fireEvent.click(screen.getByTestId("agree-button"));

    // 验证跳转到手机登录页面
    await waitFor(() => {
      expect(screen.getByTestId("mock-phone-login")).toBeInTheDocument();
    });

    // 验证 sessionStorage 中的 lastClickedMethod 被清除
    expect(sessionStorageMock.getItem("lastClickedMethod")).toBeNull();
  });

  it("在协议弹窗点击同意后应该跳转到之前点击的页面（更多登录）", async () => {
    render(<LoginPage />);

    // 点击更多登录方式但未同意隐私协议
    fireEvent.click(screen.getByText("更多"));

    // 验证显示隐私协议弹窗
    expect(screen.getByTestId("mock-consent-modal")).toBeInTheDocument();

    // 点击同意按钮
    fireEvent.click(screen.getByTestId("agree-button"));

    // 验证跳转到更多登录页面
    await waitFor(() => {
      expect(screen.getByTestId("mock-more-login")).toBeInTheDocument();
    });

    // 验证 sessionStorage 中的 lastClickedMethod 被清除
    expect(sessionStorageMock.getItem("lastClickedMethod")).toBeNull();
  });

  it("在协议弹窗点击取消或关闭按钮应关闭弹窗而不跳转", () => {
    render(<LoginPage />);

    // 点击手机登录方式但未同意隐私协议
    fireEvent.click(screen.getByText("手机"));

    // 验证显示隐私协议弹窗
    expect(screen.getByTestId("mock-consent-modal")).toBeInTheDocument();

    // 点击取消按钮
    fireEvent.click(screen.getByText("取消"));

    // 验证弹窗关闭
    expect(screen.queryByTestId("mock-consent-modal")).not.toBeInTheDocument();

    // 验证未跳转到手机登录页面
    expect(screen.queryByTestId("mock-phone-login")).not.toBeInTheDocument();
    expect(screen.getByText("微信扫码登录")).toBeInTheDocument();

    // 验证 sessionStorage 中的 lastClickedMethod 未被清除
    expect(sessionStorageMock.getItem("lastClickedMethod")).toBe("phone");
  });

  it("隐私协议链接应正确显示", () => {
    render(<LoginPage />);

    // 检查隐私协议链接
    const privacyLinks = screen.getAllByRole("link");
    expect(privacyLinks.length).toBe(2);
    expect(privacyLinks[0].textContent).toBe(" 隐私政策");
    expect(privacyLinks[1].textContent).toBe(" 在线服务协议");
  });
});
