export interface OrderState {
  cancelOrder(): string
  verifyPayment(): string
  shipOrder(): string
}

// 具体状态 - 待支付状态
export class PendingPaymentState implements OrderState {
  constructor(private order: OrderContext) {}

  cancelOrder(): string {
    this.order.setState(new CancelledState(this.order))
    return '订单已取消'
  }

  verifyPayment(): string {
    if (Math.random() > 0.5) {
      // 模拟支付验证
      this.order.setState(new PaidState(this.order))
      return '支付验证成功，订单已支付'
    }
    return '支付验证失败，请重试'
  }

  shipOrder(): string {
    return '订单未支付，不能发货'
  }
}

// 具体状态 - 已支付状态
export class PaidState implements OrderState {
  constructor(private order: OrderContext) {}

  cancelOrder(): string {
    return '订单已支付，不能直接取消，请联系客服'
  }

  verifyPayment(): string {
    return '订单已支付，无需重复验证'
  }

  shipOrder(): string {
    this.order.setState(new ShippedState(this.order))
    return '订单已发货'
  }
}

// 具体状态 - 已发货状态
export class ShippedState implements OrderState {
  constructor(private order: OrderContext) {}

  cancelOrder(): string {
    return '订单已发货，不能取消'
  }

  verifyPayment(): string {
    return '订单已发货，支付已验证'
  }

  shipOrder(): string {
    return '订单已发货，无需重复操作'
  }
}

// 具体状态 - 已取消状态
export class CancelledState implements OrderState {
  constructor(private order: OrderContext) {}

  cancelOrder(): string {
    return '订单已取消，无需重复操作'
  }

  verifyPayment(): string {
    return '订单已取消，无法验证支付'
  }

  shipOrder(): string {
    return '订单已取消，无法发货'
  }
}
