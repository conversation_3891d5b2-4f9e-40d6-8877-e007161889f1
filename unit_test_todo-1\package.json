{"name": "todo_test", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "vite", "build": "react-tsc && vite build", "preview": "vite preview", "test": "jest --coverage", "test:watch": "jest --watch", "type-check": "react-tsc --noEmit"}, "keywords": [], "type": "module", "author": "", "license": "ISC", "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "vue": "^3.5.17"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/preset-react": "^7.23.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.11", "@types/node": "^20.4.5", "@vitejs/plugin-react": "^4.6.0", "@vitejs/plugin-vue": "^6.0.0", "babel-jest": "^29.7.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.0", "typescript": "^5.1.6", "vite": "^4.4.7"}}