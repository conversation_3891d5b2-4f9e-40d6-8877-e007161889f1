import Toolbar from "./components/toolbars/Toolbar";
import Canvas from "./components/canvas/Canvas";
import * as Toast from "@radix-ui/react-toast";

export default function App() {
  return (
    <Toast.Provider swipeDirection="up">
      <div className="flex flex-col h-screen">
        <Toolbar />
        <div className="flex-1 min-h-0">
          <Canvas />
        </div>
      </div>
      <Toast.Viewport className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[1001] flex flex-col gap-2 w-auto max-w-[100vw] m-0 list-none outline-none" />
    </Toast.Provider>
  );
}
