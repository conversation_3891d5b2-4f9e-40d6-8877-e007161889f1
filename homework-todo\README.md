# Vue3 Todo App

 Vue 3 + TypeScript + Pinia

 ![表单演示](./todo.gif)

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 `http://localhost:5173` 查看应用

## 项目结构

```
src/
├── components/           # 可复用组件
│   ├── TodoInput.vue    # 待办输入组件
│   ├── TodoList.vue     # 待办列表组件
│   ├── TimePopover.vue  # 时间选择弹窗
│   └── ChineseCalendar.vue # 中文日历组件
├── stores/              # Pinia 状态管理
│   └── todo.ts         # 待办事项 store
├── assets/             # 静态资源
├── TodoApp.vue         # 主应用组件
├── App.vue            # 根组件
└── main.ts            # 应用入口
```

## 健壮性设计

### 1. TypeScript 类型声明

#### 核心数据类型

```typescript
// Todo 数据模型
export interface Todo {
  id: string          // 唯一标识符
  text: string        // 待办内容
  completed: boolean  // 完成状态
  dueDate?: string   // 截止日期 (可选)
  time?: string      // 截止时间 (可选)
}
```

#### 组件 Props 类型

```typescript
// TodoList 组件 Props
interface TodoListProps {
  todos: Todo[]
  showCompleted: boolean
  completedCount?: number
}

// TodoInput 组件 Emits
interface TodoInputEmits {
  create: (todo: { text: string; dueDate?: string; time?: string }) => void
}
```

#### 事件类型

```typescript

const emit = defineEmits<{
  toggle: (id: string) => void
  delete: (id: string) => void
  toggleShowCompleted: () => void
}>()
```

```typescript
const props = defineProps<{
  todos: Todo[]
  showCompleted: boolean
  completedCount?: number
}>()
```

### 2. 数据持久化安全性

#### localStorage 错误处理

```typescript
const loadTodos = (): Todo[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error('Error loading todos from localStorage:', error)
    return [] // 降级处理，返回空数组
  }
}

const saveTodos = (todos: Todo[]) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(todos))
  } catch (error) {
    console.error('Error saving todos to localStorage:', error)
    // 静默失败，不中断用户操作
  }
}
```

### 3. 输入验证和边界处理

#### 用户输入验证

```typescript
function createTodo() {
  if (!text.value.trim()) return // 防止空内容提交
}
```

#### 数组边界检查

```typescript
const deleteTodo = (id: string) => {
  const index = todos.value.findIndex(todo => todo.id === id)
  if (index > -1) { // 确保索引有效
    todos.value.splice(index, 1)
    saveTodos(todos.value)
  }
}
```
