import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu } from "@/components/ui/icons";
import { Sidebar } from "@/components/builder/SideBar";
import { CenterPanel } from "@/components/builder/CenterPanel";
import { RightPanel } from "@/components/builder/RightPanel";
import type { FormConfig, FormField } from "@/components/configurable-form";
import { ConfigurableForm } from "@/components/ConfigurableForm";

type AppMode = "builder" | "list" | "viewer";

function App() {
  const [mode, setMode] = useState<AppMode>("builder");
  const [formConfig, setFormConfig] = useState<FormConfig>({
    title: "新建表单",
    submitButtonText: "提交",
    submitUrl: "/submit",
    fields: [],
  });
  const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);
  const [savedForms, setSavedForms] = useState<
    { id: string; title: string; config: FormConfig }[]
  >([]);
  const [viewingFormId, setViewingFormId] = useState<string | null>(null);

  // 加载已保存的表单列表
  useEffect(() => {
    const stored = localStorage.getItem("savedForms");
    if (stored) {
      setSavedForms(JSON.parse(stored));
    }
  }, []);

  // 添加字段处理函数
  const handleAddField = (type: FormField["type"]) => {
    const newField: FormField = {
      id: `field_${Date.now()}`,
      type,
      label: getDefaultLabel(type),
      required: false,
      ...(type === "text" && { placeholder: "请输入" }),
      ...(type === "select" && {
        placeholder: "请选择",
        options: [
          { label: "选项1", value: "option1" },
          { label: "选项2", value: "option2" },
        ],
      }),
      ...(type === "radio" && {
        options: [
          { label: "选项1", value: "option1" },
          { label: "选项2", value: "option2" },
        ],
      }),
      ...(type === "imageUpload" && { maxFiles: 1, maxSizeMB: 10 }),
    };

    setFormConfig((prev) => ({
      ...prev,
      fields: [...prev.fields, newField],
    }));
    setSelectedFieldId(newField.id);
  };

  const getDefaultLabel = (type: string): string => {
    switch (type) {
      case "text":
        return "填空题";
      case "select":
        return "下拉选择题";
      case "radio":
        return "单选题";
      case "imageUpload":
        return "图片上传题";
      default:
        return "题目";
    }
  };

  // 表单配置更新处理函数
  const handleFormConfigChange = (newConfig: FormConfig) => {
    setFormConfig(newConfig);
  };

  // 字段更新处理函数
  const handleFieldUpdate = (fieldId: string, updates: Partial<FormField>) => {
    setFormConfig((prev) => ({
      ...prev,
      fields: prev.fields.map((field) =>
        field.id === fieldId ? { ...field, ...updates } : field
      ),
    }));
  };

  // 字段删除处理函数
  const handleFieldDelete = (fieldId: string) => {
    setFormConfig((prev) => ({
      ...prev,
      fields: prev.fields.filter((field) => field.id !== fieldId),
    }));
    if (selectedFieldId === fieldId) {
      setSelectedFieldId(null);
    }
  };

  // 获取选中的字段
  const selectedField = selectedFieldId
    ? formConfig.fields.find((field) => field.id === selectedFieldId) || null
    : null;

  // 发布表单
  const handlePublish = () => {
    if (!formConfig.title.trim()) {
      alert("请输入表单标题");
      return;
    }
    if (formConfig.fields.length === 0) {
      alert("请至少添加一个字段");
      return;
    }

    const formId = `form_${Date.now()}`;
    const newForm = {
      id: formId,
      title: formConfig.title,
      config: formConfig,
    };

    const updatedForms = [...savedForms, newForm];
    setSavedForms(updatedForms);
    localStorage.setItem("savedForms", JSON.stringify(updatedForms));

    alert("表单发布成功！");
    setMode("list");
  };

  // 预览表单
  const handlePreview = () => {
    if (formConfig.fields.length === 0) {
      alert("请先添加字段");
      return;
    }
    // 可以在这里实现预览模态框或新页面
    alert("预览功能暂时使用右侧实时预览");
  };

  // 查看表单
  const handleViewForm = (formId: string) => {
    setViewingFormId(formId);
    setMode("viewer");
  };

  // 返回构建器
  const handleBackToBuilder = () => {
    setMode("builder");
    setViewingFormId(null);
    // 重置表单配置
    setFormConfig({
      title: "新建表单",
      submitButtonText: "提交",
      submitUrl: "/submit",
      fields: [],
    });
    setSelectedFieldId(null);
  };

  // 返回列表
  const handleBackToList = () => {
    setMode("list");
    setViewingFormId(null);
  };

  if (mode === "list") {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">我的表单</h1>
            <Button
              onClick={handleBackToBuilder}
              className="bg-blue-600 hover:bg-blue-700"
            >
              新建表单
            </Button>
          </div>

          {savedForms.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">暂无已发布的表单</p>
              <Button onClick={handleBackToBuilder} variant="outline">
                创建第一个表单
              </Button>
            </div>
          ) : (
            <div className="grid gap-4">
              {savedForms.map((form) => (
                <div
                  key={form.id}
                  className="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => handleViewForm(form.id)}
                >
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {form.title}
                  </h3>
                  <p className="text-gray-600">
                    包含 {form.config.fields.length} 个字段
                  </p>
                  <p className="text-sm text-gray-400">点击查看表单</p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  if (mode === "viewer" && viewingFormId) {
    const form = savedForms.find((f) => f.id === viewingFormId);
    if (!form) {
      return <div>表单不存在</div>;
    }

    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white border-b px-4 py-3">
          <Button onClick={handleBackToList} variant="ghost">
            ← 返回列表
          </Button>
        </div>
        <ConfigurableForm config={form.config} />
      </div>
    );
  }

  // Builder 模式
  return (
    <div className="flex h-screen flex-col">
      {/* 顶部导航栏 */}
      <header className="flex h-16 items-center justify-between border-b bg-white px-4 shadow-sm">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-bold">新建表单</h1>
          <nav className="flex space-x-4">
            <Button variant="ghost" className="font-semibold text-blue-600">
              编辑
            </Button>
            <Button variant="ghost" onClick={() => setMode("list")}>
              我的表单
            </Button>
          </nav>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handlePreview}>
            预览
          </Button>
          <Button
            onClick={handlePublish}
            className="bg-blue-600 hover:bg-blue-700"
          >
            发布表单
          </Button>
          <Button variant="ghost" size="icon">
            <Menu className="h-5 w-5" />
          </Button>
        </div>
      </header>

      {/* 主内容区域 */}
      <div className="flex flex-1 overflow-hidden">
        <Sidebar onAddField={handleAddField} />
        <CenterPanel
          formConfig={formConfig}
          onFormConfigChange={handleFormConfigChange}
          selectedFieldId={selectedFieldId}
          onFieldSelect={setSelectedFieldId}
        />
        <RightPanel
          selectedField={selectedField}
          onFieldUpdate={handleFieldUpdate}
          onFieldDelete={handleFieldDelete}
        />
      </div>
    </div>
  );
}

export default App;
