.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 1rem;
}

.modal-container {
  background: white;
  border-radius: 12px; /* 稍微增加圆角 */
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 90vw; /* 宽度为视口宽度的90% */
  max-width: 360px; /* 但最大不超过480px */
  display: flex; /* 使用Flexbox布局 */
  flex-direction: column; /* 垂直排列 */
  max-height: 85vh; /* 弹窗最大高度为视口高度的85% */
  overflow: hidden; /* 隐藏容器本身的溢出 */
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #f3f4f6;
}

.close-icon {
  font-size: 24px;
  line-height: 24px;
  color: #9ca3af;
  font-weight: 300;
}

/* Modal Divider */
.modal-divider {
  height: 1px;
  background-color: #e5e7eb;
  margin: 0 1.5rem;
}

/* Modal Content */
.modal-content {
  padding: 1.5rem;
  overflow-y: auto; /* 当内容超出时，只让这个区域滚动 */
  flex-shrink: 1; /* 允许该区域在空间不足时收缩 */
}

.modal-text {
  font-size: 0.875rem;
  line-height: 1.6;
  color: #374151;
  margin: 0;
}

.modal-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.modal-link:hover {
  text-decoration: underline;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  gap: 0.75rem;
  padding: 0 1.5rem 1.5rem 1.5rem;
  justify-content: flex-end;
}

.cancel-button {
  padding: 0.5rem 1.5rem;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.cancel-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.agree-button {
  padding: 0.5rem 1.5rem;
  border: none;
  background-color: #3b82f6;
  color: white;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  min-width: 80px;
}

.agree-button:hover {
  background-color: #2563eb;
}

/* Responsive Design */
@media (max-width: 640px) {
  .modal-container {
    width: calc(100% - 2rem);
    margin: 1rem;
  }

  .modal-header {
    padding: 1rem 1rem 0.75rem 1rem;
  }

  .modal-content {
    padding: 1rem;
  }

  .modal-footer {
    padding: 0 1rem 1rem 1rem;
    flex-direction: column-reverse;
  }

  .cancel-button,
  .agree-button {
    width: 100%;
    padding: 0.75rem;
  }
}
