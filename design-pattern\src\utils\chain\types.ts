/* eslint-disable @typescript-eslint/no-explicit-any */
// 处理者接口和抽象类
export interface Handler {
  setNext(handler: Handler): Handler
  handle(request: Request): Promise<Response>
}

export type Request = {
  type: 'auth' | 'validation' | 'logging' | 'processing'
  data: any
  user?: {
    role: string
    permissions: string[]
  }
}

export type Response = {
  success: boolean
  message: string
  data?: any
}
