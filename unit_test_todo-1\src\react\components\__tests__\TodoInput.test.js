import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import TodoInput from "../TodoInput";

// 模拟todoUtils，以控制验证结果
jest.mock("../../utils/todoUtils", () => ({
  validateTodoText: jest
    .fn()
    .mockImplementation((text) => text && text.trim().length > 0),
}));

describe("TodoInput组件", () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    // 重置mock函数
    jest.clearAllMocks();
  });

  test("渲染默认占位符和按钮文本", () => {
    render(<TodoInput onSubmit={mockOnSubmit} />);

    expect(
      screen.getByPlaceholderText("Add a new todo...")
    ).toBeInTheDocument();
    expect(screen.getByText("Add")).toBeInTheDocument();
  });

  test("自定义占位符和按钮文本", () => {
    render(
      <TodoInput
        placeholder="请输入待办事项"
        buttonText="添加"
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByPlaceholderText("请输入待办事项")).toBeInTheDocument();
    expect(screen.getByText("添加")).toBeInTheDocument();
  });

  test("输入文本应该更新输入值", () => {
    render(<TodoInput onSubmit={mockOnSubmit} />);

    const inputElement = screen.getByPlaceholderText("Add a new todo...");
    fireEvent.change(inputElement, { target: { value: "测试待办事项" } });

    expect(inputElement).toHaveValue("测试待办事项");
  });

  test("输入为空时提交按钮应该禁用", () => {
    const { validateTodoText } = require("../../utils/todoUtils");
    validateTodoText.mockReturnValue(false);

    render(<TodoInput onSubmit={mockOnSubmit} />);

    const submitButton = screen.getByText("Add");
    expect(submitButton).toBeDisabled();
  });

  test("输入有效时提交按钮应该启用", () => {
    const { validateTodoText } = require("../../utils/todoUtils");
    validateTodoText.mockReturnValue(true);

    render(<TodoInput onSubmit={mockOnSubmit} />);

    const inputElement = screen.getByPlaceholderText("Add a new todo...");
    fireEvent.change(inputElement, { target: { value: "测试待办事项" } });

    const submitButton = screen.getByText("Add");
    expect(submitButton).not.toBeDisabled();
  });

  test("点击添加按钮应该调用onSubmit并清空输入", () => {
    const { validateTodoText } = require("../../utils/todoUtils");
    validateTodoText.mockReturnValue(true);

    render(<TodoInput onSubmit={mockOnSubmit} />);

    const inputElement = screen.getByPlaceholderText("Add a new todo...");
    fireEvent.change(inputElement, { target: { value: "测试待办事项" } });

    const submitButton = screen.getByText("Add");
    fireEvent.click(submitButton);

    expect(mockOnSubmit).toHaveBeenCalledWith("测试待办事项");
    expect(inputElement).toHaveValue("");
  });

  test("按下Enter键应该提交表单", () => {
    const { validateTodoText } = require("../../utils/todoUtils");
    validateTodoText.mockReturnValue(true);

    render(<TodoInput onSubmit={mockOnSubmit} />);

    const inputElement = screen.getByPlaceholderText("Add a new todo...");
    fireEvent.change(inputElement, { target: { value: "测试待办事项" } });
    fireEvent.keyDown(inputElement, { key: "Enter" });

    expect(mockOnSubmit).toHaveBeenCalledWith("测试待办事项");
    expect(inputElement).toHaveValue("");
  });

  test("按下Escape键应该取消并清空输入", () => {
    render(<TodoInput onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

    const inputElement = screen.getByPlaceholderText("Add a new todo...");
    fireEvent.change(inputElement, { target: { value: "测试待办事项" } });
    fireEvent.keyDown(inputElement, { key: "Escape" });

    expect(mockOnCancel).toHaveBeenCalledTimes(1);
    expect(inputElement).toHaveValue("");
  });

  test("输入超过最大长度不应该接受更多字符", () => {
    render(<TodoInput maxLength={10} onSubmit={mockOnSubmit} />);

    const inputElement = screen.getByPlaceholderText("Add a new todo...");
    expect(inputElement).toHaveAttribute("maxLength", "10");
  });

  test("测试useImperativeHandle中的focus方法", () => {
    // 1. 保存原始React方法
    const originalUseImperativeHandle = React.useImperativeHandle;

    // 2. 创建一个可以捕获方法的mock
    let capturedMethods = null;
    React.useImperativeHandle = (ref, createInstance) => {
      capturedMethods = createInstance();
    };

    // 3. 替换react useRef
    const originalUseRef = React.useRef;
    const mockInput = { focus: jest.fn() };
    const mockInputRef = { current: mockInput };
    React.useRef = jest.fn(() => mockInputRef);

    // 4. 渲染组件
    render(<TodoInput onSubmit={mockOnSubmit} />);

    // 5. 验证方法是否被正确捕获
    expect(capturedMethods).not.toBeNull();
    expect(typeof capturedMethods.focus).toBe("function");

    // 6. 调用focus方法
    capturedMethods.focus();

    // 8. 恢复原始方法
    React.useImperativeHandle = originalUseImperativeHandle;
    React.useRef = originalUseRef;
  });

  test("测试useImperativeHandle中的clear方法", () => {
    // 保存原始React方法
    const originalUseImperativeHandle = React.useImperativeHandle;
    const originalUseState = React.useState;

    // 创建捕获方法的mock
    let capturedMethods = null;
    React.useImperativeHandle = (ref, createInstance) => {
      capturedMethods = createInstance();
    };

    // 创建setState的mock
    const setInputValueMock = jest.fn();
    React.useState = jest.fn(() => ["测试文本", setInputValueMock]);

    // 渲染组件
    render(<TodoInput onSubmit={mockOnSubmit} />);

    // 验证方法是否被正确捕获
    expect(capturedMethods).not.toBeNull();
    expect(typeof capturedMethods.clear).toBe("function");

    // 调用clear方法
    capturedMethods.clear();

    // 恢复原始方法
    React.useImperativeHandle = originalUseImperativeHandle;
    React.useState = originalUseState;
  });
});
