export interface Command {
  execute(): void
  undo(): void
}

// 接收者 - 知道如何执行操作
export class LightReceiver {
  private isOn = false

  turnOn(): string {
    this.isOn = true
    return '电灯已打开'
  }

  turnOff(): string {
    this.isOn = false
    return '电灯已关闭'
  }

  getStatus(): string {
    return this.isOn ? '电灯当前状态: 开启' : '电灯当前状态: 关闭'
  }
}

// 具体命令 - 开灯命令
export class TurnOnCommand implements Command {
  constructor(private light: LightReceiver) {}

  execute(): string {
    return this.light.turnOn()
  }

  undo(): string {
    return this.light.turnOff()
  }
}

// 具体命令 - 关灯命令
export class TurnOffCommand implements Command {
  constructor(private light: LightReceiver) {}

  execute(): string {
    return this.light.turnOff()
  }

  undo(): string {
    return this.light.turnOn()
  }
}
