import React from "react";
import "./MoreLogin.css";

interface MoreLoginProps {
  onBack: () => void; // 返回主登录页的回调函数
}

const MoreLogin: React.FC<MoreLoginProps> = ({ onBack }) => {
  return (
    <div className="more-login-section">
      <div className="more-login-form">
        <div className="more-login-header">
          <button className="back-button" onClick={onBack}>
            <span>返回</span>
          </button>
          <h2 className="more-login-title">更多登录方式</h2>
        </div>

        <div className="more-login-content">
          <div
            className="login-option"
            onClick={() => {
              alert("账号密码");
            }}
          >
            <div className="login-option-icon small_account-icon"></div>
            <span className="login-option-text">账号密码</span>
          </div>
          <div
            className="login-option"
            onClick={() => {
              alert("WPS扫码");
            }}
          >
            <div className="login-option-icon small_scan-icon"></div>
            <span className="login-option-text">WPS扫码</span>
          </div>

          <div
            className="login-option"
            onClick={() => {
              alert("华为账号");
            }}
          >
            <div className="login-option-icon small_huawei-icon"></div>
            <span className="login-option-text">华为账号</span>
          </div>
          <div
            className="login-option"
            onClick={() => {
              alert("钉钉账号");
            }}
          >
            <div className="login-option-icon small_dingtalk-icon"></div>
            <span className="login-option-text">钉钉账号</span>
          </div>
          <div
            className="login-option"
            onClick={() => {
              alert("Apple账号");
            }}
          >
            <div className="login-option-icon small_apple-icon"></div>
            <span className="login-option-text">Apple账号</span>
          </div>
          <div
            className="login-option"
            onClick={() => {
              alert("小米账号");
            }}
          >
            <div className="login-option-icon small_xiaomi-icon"></div>
            <span className="login-option-text">小米账号</span>
          </div>
          <div
            className="login-option"
            onClick={() => {
              alert("微博账号");
            }}
          >
            <div className="login-option-icon small_sina-icon"></div>
            <span className="login-option-text">微博账号</span>
          </div>
          <div
            className="login-option"
            onClick={() => {
              alert("教育云账号");
            }}
          >
            <div className="login-option-icon small_jiaoyuyun-icon"></div>
            <span className="login-option-text">教育云账号</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MoreLogin;
