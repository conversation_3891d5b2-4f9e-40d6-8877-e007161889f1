import { useState, useEffect } from "react";
import * as Toast from "@radix-ui/react-toast";
import CustomIcons from "../icons/SvgIcons";

interface AIProgressCardProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onUse?: () => void;
  onDiscard?: () => void;
  onRegenerate?: () => void;
  onStop?: () => void;
  isAIProcessing?: boolean;
  aiProgress?: string;
}

export default function AIProgressCard({
  open = true,
  onOpenChange,
  onUse,
  onDiscard,
  onRegenerate,
  onStop,
  isAIProcessing = false,
  aiProgress = "",
}: AIProgressCardProps) {
  const [isCompleted, setIsCompleted] = useState(false);

  // 判断是否为失败状态
  const isError =
    !isAIProcessing &&
    aiProgress &&
    (aiProgress.includes("失败") ||
      aiProgress.includes("错误") ||
      aiProgress.includes("检查根节点内容"));

  // 判断是否为成功完成状态
  const isSuccess =
    !isAIProcessing &&
    aiProgress &&
    (aiProgress.includes("完成") || aiProgress.includes("已停止"));

  // 监听AI处理状态变化
  useEffect(() => {
    if (!isAIProcessing && open && isSuccess) {
      // 只有成功时才设置为完成
      setIsCompleted(true);
    } else if (isAIProcessing || isError) {
      // AI开始处理或出错时重置状态
      setIsCompleted(false);
    }
  }, [isAIProcessing, open, isSuccess, isError]);

  // 组件显示时重置状态
  useEffect(() => {
    if (open) {
      setIsCompleted(false);
    }
  }, [open]);

  const handleClose = () => {
    onOpenChange?.(false);
  };

  const handleUse = () => {
    onUse?.();
    handleClose();
  };

  const handleDiscard = () => {
    onDiscard?.();
    handleClose();
  };

  const handleRegenerate = () => {
    setIsCompleted(false);
    onRegenerate?.();
  };

  return (
    <Toast.Root
      className="w-[600px] bg-white border border-gray-200 rounded-lg shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=open]:slide-in-from-top-2"
      open={open}
      onOpenChange={onOpenChange}
      duration={Infinity}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold">AI</span>
            </div>
            <Toast.Title className="text-xl font-semibold text-gray-900 whitespace-nowrap">
              {isAIProcessing
                ? "正在创作中..."
                : isError
                ? "创作失败"
                : "生成完成"}
            </Toast.Title>
          </div>

          {/* Action Buttons - 只在成功完成时显示 */}
          {isCompleted && isSuccess && (
            <div className="flex items-center gap-2">
              <button
                onClick={handleDiscard}
                className="flex items-center gap-2 px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap"
              >
                <CustomIcons type="trash2" className="w-4 h-4" />
                弃用
              </button>
              <button
                onClick={handleRegenerate}
                className="flex items-center gap-2 px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap"
              >
                <CustomIcons type="rotate-ccw" className="w-4 h-4" />
                重新生成
              </button>
              <button
                onClick={handleUse}
                className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-md text-sm font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg whitespace-nowrap"
              >
                <CustomIcons type="check" className="w-4 h-4" />
                使用
              </button>
            </div>
          )}

          {/* AI处理中时显示停止按钮 */}
          {isAIProcessing && (
            <div className="flex items-center gap-2">
              <button
                onClick={onStop}
                className="flex items-center gap-2 px-4 py-2 border border-red-200 rounded-md text-sm font-medium text-red-600 hover:bg-red-50 transition-colors whitespace-nowrap"
              >
                <CustomIcons type="x" className="w-4 h-4" />
                停止
              </button>
            </div>
          )}

          {/* 失败时显示重新生成按钮 */}
          {isError && (
            <div className="flex items-center gap-2">
              <button
                onClick={handleRegenerate}
                className="flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap"
              >
                <CustomIcons type="rotate-ccw" className="w-4 h-4" />
                重新生成
              </button>
              <button
                onClick={handleClose}
                className="flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap"
              >
                <CustomIcons type="x" className="w-4 h-4" />
                关闭
              </button>
            </div>
          )}
        </div>

        {/* Progress/Status */}
        <Toast.Description asChild>
          <div className="space-y-2">
            {isAIProcessing ? (
              <>
                <div className="w-full bg-gray-100 rounded-full h-3 overflow-hidden shadow-inner">
                  <div className="h-full bg-gradient-to-r from-pink-400 via-purple-500 to-purple-600 animate-pulse" />
                </div>
                <div className="text-sm text-gray-500">
                  {aiProgress || "正在生成中..."}
                </div>
              </>
            ) : isError ? (
              <div className="text-sm text-red-600 font-medium">
                {aiProgress}
              </div>
            ) : isSuccess ? (
              <div className="text-sm text-green-600 font-medium">
                AI创作已完成，请选择操作
              </div>
            ) : (
              <div className="text-sm text-gray-500">
                {aiProgress || "等待中..."}
              </div>
            )}
          </div>
        </Toast.Description>
      </div>
    </Toast.Root>
  );
}
