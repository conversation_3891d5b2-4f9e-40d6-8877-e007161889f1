lua_shared_dict logs_probability 1m; # 创建共享内存区域用于存储日志标记

map $pod_namespace $custom_header {
    'docer-frontend-gray' 'gray';
    default '';
}

server {
    listen  80;
    server_name  _;
    charset utf-8;
    root /app/dist/v1/;

    error_page 404 @errors404;

    keepalive_timeout   0;

    add_header Cache-Control max-age=60;
    add_header X-Server-Host $hostname;

    gzip off;
    gzip_vary off;

    set_by_lua $pod_namespace 'return os.getenv("POD_NAMESPACE")';

    location / {
        set $logs_enabled 0;

        access_by_lua_block {
            local probability = 1

            if math.random(1, 10) <= probability then
                ngx.var.logs_enabled = "1"
            else
                ngx.var.logs_enabled = "0"
            end
        }

        access_log /dev/stdout nginxlog_json if=$logs_enabled;
        add_header X-Trac-Env $custom_header;
        add_header Cache-Control max-age=60;
        add_header X-Server-Host $hostname;
        root /app/dist/v1/;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    location ~ .*\.(js|css|jpg|jpeg|png|gif|svg|ttf)?$ {
        access_log /dev/stdout nginxlog_json;
        try_files $uri =404;
    }

    location @errors404 {
        access_log /dev/stdout nginxlog_json;
        return  404 "docer resource not found.";
    }

    location /docker/ {
        access_log /dev/stdout nginxlog_json;
        return 404;
    }

    location /nginx_status {
        #stub_status on;
        #access_log   off;
    }

    location = /#path#/check {
        default_type application/json;
        return 200 '{"result":"ok"}';
    }

    location = /health/check {
        default_type application/json;
        return 200 '{"result":"ok"}';
    }

    location ~ .*/favicon.ico {
       rewrite /(.*) /favicon.ico break;
    }

    include /home/<USER>/projects/*.conf;

}