<template>
  <div class="factory-demo">
    <h2>纯工厂模式演示</h2>

    <div class="controls">
      <div class="form-group">
        <label>按钮文本:</label>
        <input v-model="buttonOptions.text" placeholder="按钮文字" />
      </div>

      <div class="form-group">
        <label>按钮类型:</label>
        <select v-model="buttonOptions.variant">
          <option value="primary">主要按钮</option>
          <option value="secondary">次要按钮</option>
          <option value="danger">危险按钮</option>
          <option value="text">文字按钮</option>
        </select>
      </div>

      <div class="form-group">
        <label>按钮尺寸:</label>
        <select v-model="buttonOptions.size">
          <option value="sm">小号</option>
          <option value="md">中号</option>
          <option value="lg">大号</option>
        </select>
      </div>

      <div class="form-group">
        <label>
          <input type="checkbox" v-model="buttonOptions.disabled" />
          禁用状态
        </label>
      </div>

      <div class="form-group">
        <label>图标(可选):</label>
        <input v-model="buttonOptions.icon" placeholder="图标名称" />
      </div>

      <button @click="createButton">生成按钮</button>
    </div>

    <div class="preview">
      <h3>按钮预览</h3>
      <div v-html="buttonHtml"></div>

      <h3>生成的代码</h3>
      <pre>{{ generatedCode }}</pre>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { ButtonFactory } from '@/utils/factory/ButtonFactory'
import type { ButtonOptions } from '@/utils/factory/button/type'

export default defineComponent({
  name: 'PureFactoryDemo',
  setup() {
    const buttonOptions = ref<ButtonOptions>({
      text: '点击我',
      variant: 'primary',
      size: 'md',
      disabled: false,
    })

    const buttonHtml = ref('')
    const generatedCode = ref('')

    const createButton = () => {
      const button = ButtonFactory.createButton(buttonOptions.value)
      buttonHtml.value = button.render()

      generatedCode.value = `ButtonFactory.createButton(${JSON.stringify(buttonOptions.value, null, 2)})`
    }

    // 初始创建
    createButton()

    return {
      buttonOptions,
      buttonHtml,
      generatedCode,
      createButton,
    }
  },
})
</script>

<style scoped>
.factory-demo {
  border: 1px solid #eee;
}

/* 使用深度选择器为动态HTML添加样式 */
:deep(.btn) {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  margin-right: 10px;
}

:deep(.btn-primary) {
  background: #42b983;
  color: white;
}

:deep(.btn-secondary) {
  background: #f0f0f0;
  color: #333;
}

:deep(.btn-danger) {
  background: #f56c6c;
  color: white;
}

:deep(.btn-text) {
  background: transparent;
  color: #42b983;
  text-decoration: underline;
}

:deep(.btn-sm) {
  padding: 4px 8px;
  font-size: 12px;
}

:deep(.btn-lg) {
  padding: 12px 24px;
  font-size: 16px;
}

:deep(.btn[disabled]) {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
