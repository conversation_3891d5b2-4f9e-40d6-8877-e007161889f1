<template>
  <div class="decorator-demo">
    <h2>装饰者模式演示 - 咖啡订单系统</h2>

    <div class="coffee-base">
      <h3>选择基础咖啡</h3>
      <button @click="selectCoffee('simple')" :class="{ active: selectedCoffee === 'simple' }">
        普通咖啡 (¥5)
      </button>
    </div>

    <div class="decorators">
      <h3>添加配料</h3>
      <div class="decorator-options">
        <label>
          <input type="checkbox" v-model="decorators.milk" />
          牛奶 (+¥2)
        </label>

        <label>
          <input type="checkbox" v-model="decorators.sugar" />
          糖 (+¥1)
        </label>

        <label>
          <input type="checkbox" v-model="decorators.vanilla" />
          香草 (+¥3)
        </label>
      </div>
    </div>

    <div class="order-summary">
      <h3>您的订单</h3>
      <p>{{ currentOrder.description }}</p>
      <p class="price">总价: ¥{{ currentOrder.cost }}</p>
    </div>

    <button class="order-button" @click="placeOrder">下单</button>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, ref } from 'vue'
import { SimpleCoffee } from '@/utils/decorators/types'
import type { Coffee } from '@/utils/decorators/types'
import { MilkDecorator, SugarDecorator, VanillaDecorator } from '@/utils/decorators/CoffeeDecorator'

export default defineComponent({
  name: 'DecoratorDemo',
  setup() {
    const selectedCoffee = ref<'simple'>('simple')
    const decorators = reactive({
      milk: false,
      sugar: false,
      vanilla: false,
    })

    // 创建装饰后的咖啡订单
    const currentOrder = computed(() => {
      let coffee: Coffee = new SimpleCoffee()

      if (decorators.milk) {
        coffee = new MilkDecorator(coffee)
      }

      if (decorators.sugar) {
        coffee = new SugarDecorator(coffee)
      }

      if (decorators.vanilla) {
        coffee = new VanillaDecorator(coffee)
      }

      return {
        description: coffee.description(),
        cost: coffee.cost(),
      }
    })

    const selectCoffee = (type: 'simple') => {
      selectedCoffee.value = type
    }

    const placeOrder = () => {
      alert(`订单已提交: ${currentOrder.value.description}\n总价: ¥${currentOrder.value.cost}`)
    }

    return {
      selectedCoffee,
      decorators,
      currentOrder,
      selectCoffee,
      placeOrder,
    }
  },
})
</script>

<style scoped>
.decorator-demo {
  border: 1px solid #eee;
  padding: 20px;
  margin: 20px 0;
  border-radius: 8px;
  font-family: Arial, sans-serif;
  max-width: 500px;
}

.coffee-base,
.decorators,
.order-summary {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

button {
  padding: 8px 15px;
  margin-right: 10px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

button.active {
  background: #42b983;
  color: white;
  border-color: #42b983;
}

.decorator-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.decorator-options label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-summary {
  background: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
}

.order-summary .price {
  font-weight: bold;
  font-size: 1.2em;
  color: #42b983;
}

.order-button {
  padding: 10px 20px;
  background: #42b983;
  color: white;
  border: none;
  font-size: 1em;
}
</style>
