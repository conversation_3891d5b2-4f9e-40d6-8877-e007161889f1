<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Veggies!</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f5f5f5;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background-color: white;
        border: 2px solid #ccc;
      }

      .nav-bar {
        display: flex;
        border-bottom: 2px solid #5bc0de;
      }

      .nav-item {
        flex: 1;
        padding: 15px 20px;
        border-right: 2px solid #5bc0de;
        border-top: 2px solid #5bc0de;
        border-left: 2px solid #5bc0de;
        background-color: white;
        text-align: left;
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }

      .nav-item:last-child {
        border-right: 2px solid #5bc0de;
      }

      .main-content {
        display: flex;
        padding: 30px;
        gap: 30px;
      }

      .content {
        flex: 2;
      }

      .content h1 {
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 25px;
        color: #333;
      }

      .content p {
        font-size: 16px;
        margin-bottom: 20px;
        line-height: 1.8;
      }

      .sidebar {
        flex: 1;
        background-color: #d9edf7;
        padding: 20px;
        border: 1px solid #bce8f1;
        height: fit-content;
      }

      .sidebar p {
        font-size: 16px;
        line-height: 1.6;
      }

      .sidebar a {
        color: #333;
        text-decoration: underline;
      }

      .footer-section {
        background-color: #dff0d8;
        padding: 25px 30px;
        border-top: 1px solid #d6e9c6;
      }

      .footer-section h2 {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #333;
      }

      .footer-links {
        list-style: none;
      }

      .footer-links li {
        margin-bottom: 8px;
      }

      .footer-links a {
        color: #333;
        text-decoration: underline;
        font-size: 16px;
      }

      .footer-links a:hover {
        color: #2e6da4;
      }

      @media (max-width: 430px) {
        .nav-bar {
          flex-direction: column;
        }

        .nav-item {
          border-right: 2px solid #5bc0de;
          border-bottom: none;
        }

        .main-content {
          flex-direction: column;
          padding: 20px;
        }

        .content h1 {
          font-size: 28px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <nav class="nav-bar">
        <div class="nav-item">About</div>
        <div class="nav-item">Contact</div>
        <div class="nav-item">Meet the team</div>
        <div class="nav-item">Blog</div>
      </nav>

      <div class="main-content">
        <main class="content">
          <h1>Veggies!</h1>
          <p>
            Veggies es bonus vobis, proinde vos postulo essum magis kohlrabi
            welsh onion daikon amaranth tatsoi tomatillo melon azuki bean
            garlic.
          </p>
          <p>
            Gumbo beet greens corn soko endive gumbo gourd. Parsley shallot
            courgette tatsoi pea sprouts fava bean collard greens dandelion okra
            wakame tomato. Dandelion cucumber earthnut pea peanut soko zucchini.
          </p>
        </main>

        <aside class="sidebar">
          <p>
            All these veggies are brought to you by the
            <a href="#">Veggie Ipsum generator</a>.
          </p>
        </aside>
      </div>

      <section class="footer-section">
        <h2>External vegetable-based</h2>
        <ul class="footer-links">
          <li><a href="#">How to cook broccoli</a></li>
          <li><a href="#">Swiss Chard</a></li>
          <li><a href="#">Christmas Parsnip Recipes</a></li>
        </ul>
      </section>
    </div>
  </body>
</html>
