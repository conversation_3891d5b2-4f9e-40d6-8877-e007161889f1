{"name": "wps-login", "private": true, "version": "0.0.0", "scripts": {"dev": "pnpm vite --config vite.config.mjs --mode development", "build": "pnpm tsc -b && pnpm vite build --config vite.config.mjs --mode production", "lint": "pnpm eslint .", "preview": "pnpm vite preview", "proxy": "w2 add --force", "test": "vitest run --coverage", "test:ui": "vitest --ui"}, "dependencies": {"axios": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.22.3"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.1.0", "@types/qs": "^6.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "qs": "^6.14.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vitest": "^3.2.4"}}