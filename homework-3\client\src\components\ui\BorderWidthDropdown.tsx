import React from "react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { ChevronDownIcon, CheckIcon } from "@radix-ui/react-icons";
import { useMindMapStore } from "../../store/index";
import CustomIcons from "../icons/SvgIcons";

interface BorderWidthDropdownProps {
  disabled?: boolean;
}

const BorderWidthDropdown: React.FC<BorderWidthDropdownProps> = ({
  disabled,
}) => {
  // 从store中获取所需状态和方法
  const selection = useMindMapStore((s) => s.selection);
  const nodes = useMindMapStore((s) => s.nodes);
  const updateNodeBorderWidth = useMindMapStore((s) => s.updateNodeBorderWidth);

  // 判断是否有选中节点
  const hasSelection = selection.nodes.length > 0;
  const selectedNode = nodes.find((n) => n.id === selection.nodes[0]);

  // 处理边框宽度选择
  const handleBorderWidthChange = (width: number | null) => {
    if (hasSelection && selectedNode) {
      updateNodeBorderWidth(selectedNode.id, width ?? 0);
    }
  };

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <button
          className={`flex items-center space-x-1 px-2 py-1 text-sm rounded transition-colors ${
            disabled || !hasSelection
              ? "text-gray-400 bg-gray-50"
              : "text-gray-700 hover:bg-gray-100 cursor-pointer"
          }`}
          disabled={disabled || !hasSelection}
        >
          <CustomIcons
            type="border-width"
            className="w-4 h-4"
            disabled={disabled || !hasSelection}
          />
          <span className="whitespace-nowrap">边框宽度</span>
          <ChevronDownIcon className="w-3 h-3" />
        </button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="min-w-[180px] bg-white rounded-lg shadow-lg border border-gray-200 p-2 z-[100]"
          sideOffset={5}
        >
          <DropdownMenu.Item
            className={`flex items-center px-2 py-2 text-sm rounded outline-none cursor-pointer ${
              selectedNode?.borderWidth === undefined ||
              selectedNode?.borderWidth === 0
                ? "bg-blue-50 text-blue-600"
                : "hover:bg-gray-100"
            }`}
            onClick={() => handleBorderWidthChange(null)}
          >
            <div className="flex items-center justify-between w-full">
              <span>无</span>
              {(selectedNode?.borderWidth === undefined ||
                selectedNode?.borderWidth === 0) && (
                <CheckIcon className="w-4 h-4" />
              )}
            </div>
          </DropdownMenu.Item>

          {[1, 2, 3, 4, 5].map((width) => (
            <DropdownMenu.Item
              key={width}
              className={`flex items-center px-2 py-2 text-sm rounded outline-none cursor-pointer ${
                selectedNode?.borderWidth === width
                  ? "bg-blue-50 text-blue-600"
                  : "hover:bg-gray-100"
              }`}
              onClick={() => handleBorderWidthChange(width)}
            >
              <div className="flex items-center justify-between w-full">
                <span>{width}px</span>
                {selectedNode?.borderWidth === width && (
                  <CheckIcon className="w-4 h-4" />
                )}
              </div>
            </DropdownMenu.Item>
          ))}

          <DropdownMenu.Arrow className="fill-white" />
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
};

export default BorderWidthDropdown;
