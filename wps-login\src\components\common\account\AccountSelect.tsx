import React, { useState } from "react";
import "./AccountSelect.css";

import type { AccountSelectProps } from "@/types/user";

const AccountSelect: React.FC<AccountSelectProps> = ({
  users,
  initialSelectedIds,
  onBack,
  onConfirm,
  isMobile = false,
}) => {
  const [selectedUserIds, setSelectedUserIds] =
    useState<number[]>(initialSelectedIds);

  // 切换账号选择
  const toggleAccount = (userId: number) => {
    const user = users.find((u) => u.userid === userId);
    if (user?.is_login) return; // 已登录的账号不能取消选择

    setSelectedUserIds((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId]
    );
  };

  // 确认登录
  const handleConfirmLogin = () => {
    onConfirm(selectedUserIds);
  };

  return (
    <div className={`account-select-container ${isMobile ? "mobile" : ""}`}>
      <div className="account-select-header">
        <button className="back-button" onClick={onBack}>
          <span style={{ lineHeight: "1" }}>返回</span>
        </button>
        <h2 className="account-select-title">选择账号登录</h2>
      </div>

      <div className="account-select-content">
        <h3 className="account-select-subtitle">
          当前手机绑定了以下账号，请选择账号登录
        </h3>

        <div className="accounts-list">
          {users.map((user) => (
            <div
              key={user.userid}
              className={`account-item ${user.is_login ? "logged-in" : ""} ${
                selectedUserIds.includes(user.userid) ? "selected" : ""
              }`}
              onClick={() => toggleAccount(user.userid)}
            >
              {user.company_id !== 0 && user.company_logo ? (
                <img
                  src={user.company_logo}
                  alt={user.company_name}
                  className="account-icon"
                />
              ) : user.avatar_url ? (
                <img
                  src={user.avatar_url}
                  alt={user.nickname}
                  className="account-icon"
                />
              ) : (
                <div className="account-icon">{user.nickname.charAt(0)}</div>
              )}
              <div className="account-info">
                <div className="account-name">
                  {user.is_company_account ? user.company_name : "个人账号"}
                </div>
                <div className="account-user">{user.nickname}</div>
                {user.status !== 1 && (
                  <div className="account-status">
                    {user.reason || "状态异常"}
                  </div>
                )}
              </div>
              {!user.is_login && selectedUserIds.includes(user.userid) && (
                <div className="account-check">✓</div>
              )}
            </div>
          ))}
        </div>

        <div className="account-footer">
          <p className="selected-info">
            已选{selectedUserIds.length}个账号，登录后可切换访问
            {selectedUserIds.length}个账号的数据
          </p>
          <button
            className="confirm-login-btn"
            onClick={handleConfirmLogin}
            disabled={selectedUserIds.length === 0}
          >
            确认登录
          </button>
        </div>
      </div>
    </div>
  );
};

export default AccountSelect;
