import type { Handler, Request, Response } from './types'

export abstract class BaseHandler implements Handler {
  private nextHandler: Handler | null = null

  setNext(handler: Handler): Handler {
    this.nextHandler = handler
    return handler
  }

  async handle(request: Request): Promise<Response> {
    if (this.nextHandler) {
      return this.nextHandler.handle(request)
    }
    return {
      success: false,
      message: '请求未被处理',
    }
  }
}
