<!-- components/TodoList.vue -->
<template>
  <div>
    <!-- Active Todo List -->
    <div class="todo-list" v-if="activeTodos.length > 0">
      <div
        v-for="todo in activeTodos"
        :key="todo.id"
        class="todo-item"
        @contextmenu.prevent="showContextMenu($event, todo)"
      >
        <div class="todo-content" @click="$emit('toggle', todo.id)">
          <input
            type="checkbox"
            :checked="todo.completed"
            @change="$emit('toggle', todo.id)"
            class="todo-checkbox"
            @click.stop
          />
          <span class="todo-text">{{ todo.text }}</span>
        </div>
        <div v-if="formatTodoDate(todo)" class="todo-deadline">
          <svg viewBox="0 0 1024 1024" width="18" height="18">
            <path
              d="M554.666667 516.266667l102.4 102.4-59.733334 59.733333-123.733333-123.733333H469.333333V341.333333h85.333334v174.933334zM512 853.333333c-187.733333 0-341.333333-153.6-341.333333-341.333333s153.6-341.333333 341.333333-341.333333 341.333333 153.6 341.333333 341.333333-153.6 341.333333-341.333333 341.333333z m0-85.333333c140.8 0 256-115.2 256-256s-115.2-256-256-256-256 115.2-256 256 115.2 256 256 256z"
              fill="#444444"
            ></path>
          </svg>
          {{ formatTodoDate(todo) }}
        </div>
      </div>
    </div>

    <!-- Show/Hide Completed -->
    <div v-if="completedCount > 0" class="completed-section">
      <button @click="$emit('toggleShowCompleted')" class="toggle-completed">
        <svg class="toggle-icon" width="30" height="20" viewBox="0 0 12 12">
          <path v-if="!showCompleted" d="M4 6l2 2 2-2" fill="#666" />
          <path v-else d="M4 8l2-2 2 2" fill="#666" />
        </svg>
        {{ showCompleted ? '隐藏已完成' : '显示已完成' }}
      </button>

      <!-- Completed Todo List -->
      <div v-if="showCompleted" class="todo-list completed-todos">
        <div
          v-for="todo in completedTodos"
          :key="todo.id"
          class="todo-item completed"
          @contextmenu.prevent="showContextMenu($event, todo)"
        >
          <div class="todo-content" @click="$emit('toggle', todo.id)">
            <input
              type="checkbox"
              :checked="todo.completed"
              @change="$emit('toggle', todo.id)"
              class="todo-checkbox"
              @click.stop
            />
            <span class="todo-text">{{ todo.text }}</span>
          </div>
          <div v-if="formatTodoDate(todo)" class="todo-deadline">{{ formatTodoDate(todo) }}</div>
        </div>
      </div>
    </div>

    <!-- Context Menu Popover -->
    <div
      v-if="contextMenu.visible"
      class="context-menu-popover"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
    >
      <div class="popover-item" @click.stop="deleteTodo">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path
            d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14zM10 11v6M14 11v6"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        删除待办
      </div>
    </div>

    <!-- Backdrop for context menu -->
    <div
      v-if="contextMenu.visible"
      class="context-menu-backdrop"
      @click.stop="hideContextMenu"
    ></div>

    <!-- Empty State -->
    <div v-if="activeTodos.length === 0 && completedTodos.length === 0" class="empty-state">
      <div class="empty-content">
        <svg class="empty-icon" viewBox="0 0 1025 1024" width="100" height="100">
          <path
            d="M198.937626 716.175455c-19.495887 0-35.808773 26.259767-35.808772 45.755654s16.312885 45.755654 35.808772 45.755654h123.341329c19.893763 0 35.808773-25.861891 35.808773-45.755654S342.172717 716.175455 322.278955 716.175455zM761.135359 512.463326c0-19.893763-16.312885-45.755654-35.808773-45.755654H198.937626c-19.495887 0-35.808773 25.861891-35.808772 45.755654S177.850238 557.025354 198.937626 557.025354h526.38896c19.495887 0 35.808773-26.259767 35.808773-45.755654zM198.937626 310.740573h526.786835c19.893763 0 35.808773-25.861891 35.808773-45.755655s-15.91501-45.755654-35.808773-45.755654H198.937626c-19.495887 0-35.808773 26.259767-35.808772 45.755654s16.312885 45.755654 35.808772 45.755655z"
            fill="#e6eaf1"
          ></path>
          <path
            d="M918.693959 534.74434V95.490061A96.683686 96.683686 0 0 0 822.408148 0H111.802946A126.52433 126.52433 0 0 0 0 111.007196v801.718634a126.126455 126.126455 0 0 0 111.802946 111.007196h550.65935a42.970527 42.970527 0 0 0 47.74503-43.368403c0-28.647018-20.689513-55.702535-39.787525-56.100411H140.84784c-28.249143 0-39.787525-12.732008-39.787526-39.787525V134.08396c0-24.668266 9.946881-34.615147 34.615147-34.615147h647.343037c25.066141 0 35.013022 9.946881 35.013022 34.615147v400.66038c0 19.495887 26.657642 36.604523 56.896161 36.604523a39.787525 39.787525 0 0 0 43.368403-36.604523z"
            fill="#cdd6e3"
          ></path>
          <path
            d="M460.739543 767.899238a70.42392 70.42392 0 1 0 70.42392-71.617545 71.21967 71.21967 0 0 0-70.42392 71.617545zM742.435222 696.281693a71.617546 71.617546 0 1 0 70.42392 71.617545 70.821795 70.821795 0 0 0-70.42392-71.617545zM954.900607 696.281693a71.617546 71.617546 0 1 0 70.42392 71.617545A71.21967 71.21967 0 0 0 954.900607 696.281693z"
            fill="#bbc6d3"
          ></path>
        </svg>
        <p class="empty-text">暂无待办事项</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue'

interface Todo {
  id: string
  text: string
  completed: boolean
  dueDate?: string
  time?: string
  order?: number
}

const props = defineProps<{
  todos: Todo[]
  showCompleted: boolean
  completedCount?: number
}>()

const emit = defineEmits(['toggle', 'toggleShowCompleted', 'delete'])

// Context menu state
const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  todo: null as Todo | null,
})

// Computed properties
const activeTodos = computed(() => props.todos.filter((t) => !t.completed))
const completedTodos = computed(() => props.todos.filter((t) => t.completed))
const completedCount = computed(() => props.completedCount || completedTodos.value.length)

// Context menu methods
const showContextMenu = (event: MouseEvent, todo: Todo) => {
  contextMenu.value = {
    visible: true,
    x: event.clientX,
    y: event.clientY,
    todo,
  }
}

const hideContextMenu = () => {
  contextMenu.value.visible = false
  contextMenu.value.todo = null
}

const deleteTodo = () => {
  if (contextMenu.value.todo) {
    const todoId = contextMenu.value.todo.id
    // 先重置 contextMenu
    contextMenu.value.visible = false
    contextMenu.value.todo = null
    // 然后执行删除操作
    nextTick(() => {
      emit('delete', todoId)
    })
  }
}

// Utility methods
function formatTodoDate(todo: Todo): string {
  if (!todo.dueDate) return ''

  if (todo.time) {
    return `${todo.dueDate} ${todo.time}`
  }
  return todo.dueDate
}
</script>

<style scoped>
.todo-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.todo-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s;
  cursor: pointer;
}

.todo-item:hover {
  background-color: #f8f9fa;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-item.completed {
  background-color: #f5f5f5;
  opacity: 0.7;
}

.todo-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.todo-checkbox {
  margin-right: 12px;
  width: 16px;
  height: 16px;
}

.todo-text {
  font-size: 16px;
  color: #333;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  color: #999;
}

.todo-deadline {
  color: #666;
  font-size: 16px;
  margin: 0 12px;
}

.drag-handle {
  color: #ccc;
  cursor: grab;
  font-weight: bold;
}

.drag-handle:active {
  cursor: grabbing;
}

.completed-section {
  text-align: center;
  margin-top: 20px;
}

.toggle-completed {
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 8px;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.toggle-completed:hover {
  color: #409eff;
}

.toggle-completed:hover .toggle-icon path {
  fill: #409eff;
}

.toggle-icon {
  transition: all 0.2s;
}

.context-menu-popover {
  position: fixed;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 150px;
}

.popover-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.popover-item:hover {
  background-color: #f0f0f0;
}

.context-menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 400px;
  width: 100%;
  /* max-width: 1000px; */
  margin-left: auto;
  margin-right: auto;
}

.empty-content {
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
}

.empty-text {
  color: #999;
  font-size: 16px;
  margin: 0;
}

svg {
  vertical-align: middle;
  flex-shrink: 0;
}
</style>
