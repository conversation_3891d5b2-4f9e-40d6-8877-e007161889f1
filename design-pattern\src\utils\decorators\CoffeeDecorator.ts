import type { Coffee } from './types'

export abstract class CoffeeDecorator implements Coffee {
  constructor(protected coffee: Coffee) {}

  abstract cost(): number
  abstract description(): string
}

// 具体装饰者 - 加牛奶
export class MilkDecorator extends CoffeeDecorator {
  cost(): number {
    return this.coffee.cost() + 2 // 加牛奶多2元
  }

  description(): string {
    return `${this.coffee.description()}, 加牛奶`
  }
}

// 具体装饰者 - 加糖
export class SugarDecorator extends CoffeeDecorator {
  cost(): number {
    return this.coffee.cost() + 1 // 加糖多1元
  }

  description(): string {
    return `${this.coffee.description()}, 加糖`
  }
}

// 具体装饰者 - 加香草
export class VanillaDecorator extends CoffeeDecorator {
  cost(): number {
    return this.coffee.cost() + 3 // 加香草多3元
  }

  description(): string {
    return `${this.coffee.description()}, 加香草`
  }
}
