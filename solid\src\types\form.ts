import { z } from "zod";

export interface FormOption {
  label: string;
  value: string;
}

// 基础字段接口
export interface BaseField {
  id: string;
  type: string;
  label: string;
  required?: boolean;
}

// 扩展现有字段接口
export interface TextField extends BaseField {
  type: "text";
  placeholder?: string;
  maxLength?: number;
}

export interface SelectField extends BaseField {
  type: "select";
  placeholder?: string;
  multiple?: boolean;
  options: FormOption[];
}

export interface RadioField extends BaseField {
  type: "radio";
  options: FormOption[];
}

export interface ImageUploadField extends BaseField {
  type: "imageUpload";
  maxFiles?: number;
  maxSizeMB?: number;
}

// 表单模式枚举
export enum FormMode {
  NORMAL = "normal",
  BUILDER = "builder",
  PREVIEW = "preview",
  READONLY = "readonly",
}

// 使用更灵活的字段类型，支持动态扩展
export type FormField =
  | TextField
  | SelectField
  | RadioField
  | ImageUploadField
  | (BaseField & Record<string, unknown>);

export interface FormConfig {
  title: string;
  submitButtonText: string;
  submitUrl: string;
  fields: FormField[];
}

export interface FormData {
  [key: string]: string;
}

export interface FormErrors {
  [key: string]: string;
}

export interface UseFormReturn {
  formData: FormData;
  errors: FormErrors;
  isSubmitting: boolean;
  schema: z.ZodObject<Record<string, z.ZodTypeAny>> | null;
  handleFieldChange: (fieldId: string, value: string) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  validateField: (fieldId: string, value: string) => string | null;
}
