FROM hub-mirror.wps.cn/sreopen/node:18.16 as builder

ADD . /build
WORKDIR /build

RUN npm install
RUN npm run build

FROM  hub-mirror.wps.cn/sreopen/openresty:********-10-alpine-v1 as runner

COPY --from=builder /build/dist/. /app/dist/
COPY --from=builder /build/docker/nginx.conf /usr/local/openresty/nginx/conf/vhost/default.conf
COPY --from=builder /build/docker/nginx_root.conf /usr/local/openresty/nginx/conf/nginx.conf
COPY --from=builder /build/docker/entrypoint.sh /app/entrypoint.sh

RUN ls -la /app/dist/
RUN chmod +x /app/entrypoint.sh
RUN touch /usr/local/openresty/nginx/logs/access_format.log
RUN chmod 666 /usr/local/openresty/nginx/logs/access_format.log

ENTRYPOINT ["/app/entrypoint.sh"]

EXPOSE 80

CMD  ["openresty"]