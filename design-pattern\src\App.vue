<template>
  <div class="app-container">
    <h1>设计模式演示</h1>

    <div class="pattern-section">
      <h2>单例模式</h2>
      <SingletonDemo />
      <p class="description">注意：多个组件共享同一个计数器实例</p>
    </div>

    <div class="pattern-section">
      <h2>观察者模式</h2>
      <ObserverDemo />
    </div>

    <div class="pattern-section">
      <h2>工厂模式</h2>
      <FactoryDemo />
    </div>

    <div class="pattern-section">
      <h2>策略模式</h2>
      <StrategyDemo />
    </div>

    <div class="pattern-section">
      <h2>装饰器模式</h2>
      <DecoratorDemo />
    </div>

    <div class="pattern-section">
      <h2>命令模式</h2>
      <CommandDemo />
    </div>

    <div class="pattern-section">
      <h2>代理模式</h2>
      <ProxyDemo />
    </div>

    <div class="pattern-section">
      <h2>状态模式</h2>
      <StateDemo />
    </div>

    <div class="pattern-section">
      <h2>适配器模式</h2>
      <AdaptDemo />
    </div>

    <div class="pattern-section">
      <h2>职责链模式</h2>
      <ChainDemo />
    </div>

    <div class="pattern-section">
      <h2>桥接模式</h2>
      <BridgeDemo />
    </div>

    <div class="pattern-section">
      <h2>组合模式</h2>
      <CompositeDemo />
    </div>
  </div>
</template>

<script setup lang="ts">
import SingletonDemo from './components/SingletonDemo.vue'
import ObserverDemo from './components/ObserverDemo.vue'
import FactoryDemo from './components/FactoryDemo.vue'
import StrategyDemo from './components/StrategyDemo.vue'
import DecoratorDemo from './components/DecoratorDemo.vue'
import CommandDemo from './components/CommandDemo.vue'
import ProxyDemo from './components/ProxyDemo.vue'
import StateDemo from './components/StateDemo.vue'
import AdaptDemo from './components/AdaptDemo.vue'
import ChainDemo from './components/ChainDemo.vue'
import BridgeDemo from './components/BridgeDemo.vue'
import CompositeDemo from './components/CompositeDemo.vue'
</script>

<style scoped>
.app-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.pattern-section {
  margin: 40px 0;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.description {
  color: #666;
  font-size: 0.9em;
  margin-top: 10px;
}

h1 {
  color: #2c3e50;
  text-align: center;
}

h2 {
  color: #42b983;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}
</style>
