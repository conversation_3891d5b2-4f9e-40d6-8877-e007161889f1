import { useState, useEffect, useCallback } from "react";
import { z } from "zod";
import type {
  FormConfig,
  FormData,
  FormErrors,
  UseFormReturn,
} from "@/types/form";

export function useForm(config: FormConfig): UseFormReturn {
  const [formData, setFormData] = useState<FormData>({}); //存储每个字段当前的值
  const [errors, setErrors] = useState<FormErrors>({}); //存储每个字段当前的错误信息
  const [isSubmitting, setIsSubmitting] = useState(false); //是否正在提交表单
  const [schema, setSchema] = useState<z.ZodObject<
    Record<string, z.ZodTypeAny>
  > | null>(null); //存储表单的验证模式

  // 初始化表单数据和验证模式
  useEffect(() => {
    const initialData: FormData = {};
    const schemaFields: { [key: string]: z.ZodTypeAny } = {};

    config.fields.forEach((field) => {
      initialData[field.id] = "";

      let fieldSchema: z.ZodTypeAny = z.string();

      if (field.type === "text") {
        fieldSchema = z.string().max(field.maxLength || 255, {
          message: `最大长度为 ${field.maxLength || 255} 个字符`,
        });
      } else if (field.type === "select" || field.type === "radio") {
        fieldSchema = z.string().min(1, { message: `${field.label} 为必填项` });
      }

      if (field.required) {
        fieldSchema = fieldSchema.min(1, {
          message: `${field.label} 为必填项`,
        });
      } else {
        fieldSchema = fieldSchema.optional().or(z.literal(""));
      }

      schemaFields[field.id] = fieldSchema;
    });

    setFormData(initialData);
    setSchema(z.object(schemaFields));
  }, [config]);

  //单字段验证函数
  const validateField = useCallback(
    (fieldId: string, value: string): string | null => {
      if (!schema) return null;

      try {
        const fieldSchema = schema.shape[fieldId];
        fieldSchema.parse(value);
        return null;
      } catch (error) {
        if (error instanceof z.ZodError) {
          return error.errors[0]?.message || "验证错误";
        }
        return "验证错误";
      }
    },
    [schema]
  );

  const handleFieldChange = useCallback((fieldId: string, value: string) => {
    setFormData((prev) => ({ ...prev, [fieldId]: value }));

    // 清除该字段的错误信息
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[fieldId];
      return newErrors;
    });
  }, []);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!schema) return;

      // 验证所有字段
      const newErrors: FormErrors = {};
      let isValid = true;

      config.fields.forEach((field) => {
        const error = validateField(field.id, formData[field.id] || "");
        if (error) {
          newErrors[field.id] = error;
          isValid = false;
        }
      });

      setErrors(newErrors);

      if (!isValid) return;

      setIsSubmitting(true);

      console.log("表单成功提交");
      setIsSubmitting(false);

      // try {
      //   const response = await fetch(config.submitUrl, {
      //     method: "POST",
      //     headers: {
      //       "Content-Type": "application/json",
      //     },
      //     body: JSON.stringify(formData),
      //   });

      //   if (response.ok) {
      //     console.log("表单提交成功！");
      //     alert("表单提交成功！");
      //     // 重置表单
      //     setFormData(
      //       config.fields.reduce((acc, field) => {
      //         acc[field.id] = "";
      //         return acc;
      //       }, {} as FormData)
      //     );
      //   } else {
      //     console.error("表单提交失败:", response.statusText);
      //     alert("表单提交失败，请重试。");
      //   }
      // } catch (error) {
      //   console.error("提交过程中发生错误:", error);
      //   alert("提交过程中发生错误。");
      // } finally {
      //   setIsSubmitting(false);
      // }
    },
    [config, formData, schema, validateField]
  );

  return {
    formData,
    errors,
    isSubmitting,
    schema,
    handleFieldChange,
    handleSubmit,
    validateField,
  };
}
